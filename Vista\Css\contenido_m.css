/* Estilos específicos para la página de contenido del curso (vista de maestros) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Encabezado del curso */
  .course-header {
    padding: 30px;
    color: white;
  }
  
  .course-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .course-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .back-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
  }
  
  .back-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  .course-title h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
  }
  
  .course-title p {
    font-size: 1rem;
    opacity: 0.8;
  }
  
  .course-header-right {
    display: flex;
    align-items: center;
  }
  
  .course-schedule-info {
    display: flex;
    gap: 20px;
  }
  
  .schedule-day {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 10px 15px;
    border-radius: 8px;
  }
  
  .day-label {
    font-weight: 500;
    margin-bottom: 5px;
  }
  
  .day-time {
    font-size: 0.85rem;
    opacity: 0.8;
  }
  
  /* Navegación del curso */
  .course-navigation {
    background-color: white;
    padding: 0 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .course-nav-tabs {
    display: flex;
    gap: 30px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
  }
  
  .course-nav-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }
  
  .course-tab {
    padding: 15px 0;
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition);
    white-space: nowrap;
  }
  
  .course-tab:hover {
    color: var(--primary-color);
  }
  
  .course-tab.active {
    color: var(--primary-color);
  }
  
  .course-tab.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px 3px 0 0;
  }
  
  /* Sección de videoconferencia */
  .meet-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
  }
  
  .no-meet-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
  }
  
  .no-meet-message .material-icons {
    font-size: 3rem;
    opacity: 0.5;
  }
  
  .no-meet-message p {
    font-size: 1rem;
  }
  
  /* Sección de videoconferencia activa */
  .meet-section.active-meet {
    background-color: #e8f5e9;
    min-height: auto;
    padding: 0;
  }
  
  .meet-info {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    width: 100%;
  }
  
  .meet-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4caf50;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .meet-icon .material-icons {
    color: white;
    font-size: 1.8rem;
  }
  
  .meet-details {
    flex: 1;
  }
  
  .meet-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .meet-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 10px;
  }
  
  .meet-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  .meet-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 8px 16px;
    background-color: var(--primary-light);
    border-radius: 20px;
    transition: var(--transition);
  }
  
  .meet-link:hover {
    background-color: #d4e6f9;
  }
  
  .meet-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .meet-action-btn:hover {
    background-color: white;
  }
  
  .meet-action-btn.edit-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .meet-action-btn.delete-btn:hover {
    color: var(--danger-color);
    border-color: var(--danger-color);
  }
  
  /* Botones de acción */
  .section-actions {
    display: flex;
    gap: 10px;
  }
  
  .action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background-color: #1e40af;
  }
  
  .create-folder-btn {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .create-folder-btn:hover {
    background-color: var(--primary-color);
    color: white;
  }
  
  /* Contenido del curso */
  .course-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .week-container {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
    box-shadow: var(--shadow-sm);
  }
  
  .week-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .week-header:hover {
    background-color: #e8eef7;
  }
  
  .week-title h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .week-dates {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .week-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .week-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .week-action-btn:hover {
    background-color: white;
  }
  
  .week-action-btn.edit-btn:hover {
    color: var(--primary-color);
  }
  
  .week-action-btn.delete-btn:hover {
    color: var(--danger-color);
  }
  
  .toggle-icon {
    cursor: pointer;
    transition: transform 0.3s;
  }
  
  .folder-content {
    padding: 20px;
  }
  
  .content-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    background-color: white;
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
    transition: var(--transition);
  }
  
  .content-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
  
  .content-item:last-child {
    margin-bottom: 0;
  }
  
  .content-item.locked {
    background-color: #f9f9f9;
    border: 1px dashed var(--border-color);
  }
  
  .content-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .content-icon.pdf {
    background-color: #ffebee;
  }
  
  .content-icon.pdf .material-icons {
    color: #f44336;
  }
  
  .content-icon.ppt {
    background-color: #fff8e1;
  }
  
  .content-icon.ppt .material-icons {
    color: #ff9800;
  }
  
  .content-icon.task {
    background-color: #e8f5e9;
  }

  .content-icon.task .material-icons {
    color: #4caf50;
  }

  .content-icon.exam {
    background-color: #fff3e0;
  }

  .content-icon.exam .material-icons {
    color: #ff9800;
  }

  .content-icon.link {
    background-color: #e3f2fd;
  }
  
  .content-icon.link .material-icons {
    color: #2196f3;
  }
  
  .content-icon.announcement {
    background-color: #ede7f6;
  }

  .content-icon.announcement .material-icons {
    color: #673ab7;
  }

  .content-icon.video {
    background-color: #ffebee;
  }

  .content-icon.video .material-icons {
    color: #e91e63;
  }

  /* Estilos para vista previa de video */
  .video-player-placeholder {
    margin: 20px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    border: 2px dashed #ddd;
  }

  .video-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .video-thumbnail .play-icon {
    font-size: 64px;
    color: #e91e63;
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .video-thumbnail .play-icon:hover {
    transform: scale(1.1);
  }

  .video-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
  }

  .content-icon.participation {
    background-color: #e8f5e9;
  }

  .content-icon.participation .material-icons {
    color: #4caf50;
  }

  /* Estilos para vista de participación */
  .participation-container {
    padding: 20px;
  }

  .participation-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
  }

  .participation-grades h3 {
    margin-bottom: 20px;
    color: var(--text-primary);
  }

  .participation-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background-color: #e8f5e9;
    border-radius: 8px;
    color: #4caf50;
  }

  .participation-grade-display {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
  }

  .grade-label {
    font-weight: 500;
    color: var(--text-secondary);
  }

  .grade-value {
    font-size: 1.2em;
    font-weight: 600;
    color: #4caf50;
    padding: 5px 10px;
    background-color: #e8f5e9;
    border-radius: 4px;
  }

  .participation-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
  
  .content-details {
    flex: 1;
    cursor: pointer;
  }
  
  .content-details h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 5px;
  }
  
  .content-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
  }
  
  .content-date {
    font-size: 0.8rem;
    color: var(--text-light);
    display: block;
  }
  
  .content-lock {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    color: var(--warning-color);
    margin-top: 5px;
  }
  
  .content-lock .material-icons {
    font-size: 1rem;
  }
  
  .content-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .content-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .content-action-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .content-action-btn.edit-btn:hover {
    color: var(--primary-color);
  }
  
  .content-action-btn.delete-btn:hover {
    color: var(--danger-color);
  }
  
  /* Fix the task-status design */
  .task-status {
    font-size: 0.85rem;
    color: var(--warning-color);
    background-color: rgba(255, 152, 0, 0.1);
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    font-weight: 500;
  }
  
  /* Botón para agregar contenido */
  .add-content-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
    margin-top: 15px;
  }
  
  .add-content-btn:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
  }
  
  .add-content-btn .material-icons {
    color: var(--primary-color);
  }
  
  /* Mensaje de contenido vacío */
  .empty-content {
    padding: 30px;
    text-align: center;
    color: var(--text-light);
  }
  
  .empty-content .material-icons {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
  }
  
  .empty-content p {
    font-size: 1rem;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  .modal-small {
    max-width: 450px;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    min-width: 110px;
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  /* Formulario */
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  .form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
  }
  
  .form-group input[type="text"],
  .form-group input[type="number"],
  .form-group input[type="date"],
  .form-group input[type="time"],
  .form-group input[type="url"],
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .checkbox-group {
    margin-top: 5px;
  }
  
  .checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    cursor: pointer;
  }
  
  .file-upload {
    position: relative;
  }
  
  .file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  
  .file-upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 15px;
    border: 1px dashed var(--border-color);
    border-radius: 5px;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .file-upload:hover .file-upload-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
  }
  
  .btn-secondary,
  .btn-primary,
  .btn-danger {
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: #e0e0e0;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  .btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #d32f2f;
  }
  
  /* Modal de anuncio */
  .announcement-meta {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 15px;
  }
  
  .announcement-text {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
  }
  
  .announcement-text p {
    margin-bottom: 15px;
  }
  
  .announcement-text ul {
    margin-bottom: 15px;
    padding-left: 20px;
  }
  
  .announcement-text li {
    margin-bottom: 5px;
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
  
  /* Vista de tarea */
  .assignment-container {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
  }
  
  /* Update the assignment header to remove the grade display */
  .assignment-header {
    display: flex;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px 10px 0 0;
  }
  
  .assignment-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
  }
  
  .assignment-title .material-icons {
    font-size: 1.8rem;
  }
  
  .assignment-content {
    padding: 0;
  }
  
  .assignment-section {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .assignment-section:last-child {
    border-bottom: none;
  }
  
  .assignment-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }
  
  .assignment-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
  }
  
  .assignment-image {
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 15px;
  }
  
  .assignment-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .meta-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  
  .meta-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .meta-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .submission-section {
    padding: 0;
  }
  
  .submission-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }
  
  .task-status-info {
    margin-bottom: 20px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }

  .status-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
  }

  .status-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .status-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .status-value {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
  }
  
  .submission-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .file-upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 30px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background-color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  .file-upload-icon {
    font-size: 3rem;
    color: var(--text-light);
  }
  
  .file-upload-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
    margin: 0;
  }
  
  .file-upload-hint {
    font-size: 0.85rem;
    color: var(--text-light);
    text-align: center;
    margin: 0;
  }
  
  .text-submission-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .text-submission-label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .text-submission-textarea {
    width: 100%;
    height: 120px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    outline: none;
    transition: var(--transition);
  }
  
  .text-submission-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .submission-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
  }
  
  .assignment-action-btn {
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .secondary-btn {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .secondary-btn:hover {
    background-color: #e0e0e0;
  }
  
  .primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .primary-btn:hover {
    background-color: #1e40af;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .course-header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
    }
  
    .course-header-right {
      width: 100%;
    }
  
    .course-schedule-info {
      width: 100%;
      justify-content: space-between;
    }
  
    .meet-info {
      flex-direction: column;
      text-align: center;
    }
  
    .meet-icon {
      margin: 0 auto;
    }
  
    .meet-actions {
      justify-content: center;
    }
  
    .section-actions {
      flex-wrap: wrap;
    }
  
    .form-row {
      flex-direction: column;
      gap: 20px;
    }
  
    .assignment-meta {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 768px) {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  
    .section-actions {
      width: 100%;
    }
  
    .action-btn {
      flex: 1;
      justify-content: center;
    }
  
    .week-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  
    .week-actions {
      align-self: flex-end;
    }
  
    .content-item {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .content-actions {
      align-self: flex-end;
      margin-top: 10px;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn-secondary,
    .btn-primary,
    .btn-danger {
      width: 100%;
    }
  
    .submission-actions {
      flex-direction: column;
    }
  
    .assignment-action-btn {
      width: 100%;
    }
  }

  /* Estilos para campos de participación */
  .students-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 10px;
  }

  .student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
  }

  .student-item:last-child {
    border-bottom: none;
  }

  .student-item:hover {
    background-color: var(--secondary-color);
  }

  .student-name {
    font-weight: 500;
    color: var(--text-color);
  }

  .participation-grade {
    width: 80px;
    padding: 5px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
  }

  .participation-grade:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(42, 77, 183, 0.1);
  }

  /* Editor de texto enriquecido */
  .rich-text-editor {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .editor-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    gap: 4px;
  }

  .toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    color: var(--text-color);
  }

  .toolbar-btn:hover {
    background-color: rgba(42, 77, 183, 0.1);
  }

  .toolbar-btn.active {
    background-color: var(--primary-color);
    color: white;
  }

  .toolbar-btn .material-icons {
    font-size: 18px;
  }

  .toolbar-separator {
    width: 1px;
    height: 20px;
    background-color: var(--border-color);
    margin: 0 4px;
  }

  .editor-content {
    min-height: 150px;
    padding: 15px;
    outline: none;
    line-height: 1.6;
    font-family: inherit;
    font-size: 14px;
    direction: ltr !important;
    text-align: left !important;
  }

  .editor-content:empty:before {
    content: attr(placeholder);
    color: #999;
    font-style: italic;
  }

  .editor-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 10px 0;
  }

  .editor-content ul, .editor-content ol {
    margin: 10px 0;
    padding-left: 30px;
  }

  .editor-content li {
    margin: 5px 0;
  }

  .editor-content a {
    color: var(--primary-color);
    text-decoration: underline;
  }

  /* Modal de vista previa de documentos */
  .modal-large {
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
  }

  .document-preview-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .document-info {
    padding: 15px;
    background-color: var(--secondary-color);
    border-radius: 8px;
  }

  .document-meta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .document-meta span {
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .document-type {
    background-color: var(--primary-light);
    color: var(--primary-color) !important;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
  }

  .document-content {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .document-description-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #007bff;
  }

  .document-description-section h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }

  .document-description-section .document-description {
    margin: 0;
    color: #666;
    line-height: 1.5;
  }

  .document-description-content {
    background: white;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    font-style: normal;
  }

  .document-description-content p {
    margin: 0;
    color: #333;
    line-height: 1.6;
    font-style: normal;
  }

  .document-viewer {
    width: 100%;
    height: 400px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
  }

  .document-placeholder {
    text-align: center;
    color: var(--text-light);
  }

  .document-placeholder .material-icons {
    font-size: 4rem;
    margin-bottom: 15px;
    opacity: 0.5;
  }

  .document-placeholder p {
    margin: 5px 0;
  }

  .document-filename {
    margin: 8px 0 0 0 !important;
    color: #007bff !important;
    font-size: 13px !important;
    font-weight: 500;
  }

  .document-description {
    font-style: normal;
    max-width: 300px;
    margin: 10px auto 0;
  }

  .document-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
  }

  /* Estilos para vista previa de estudiante */
  .student-preview-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .preview-notice {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background-color: #e3f2fd;
    border-radius: 8px;
    margin-bottom: 20px;
    color: #1976d2;
  }

  .preview-notice .material-icons {
    font-size: 1.2rem;
  }

  .student-content-item {
    background-color: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .content-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e0e0;
  }

  .content-icon-large {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #666;
  }

  .announcement-preview .content-icon-large {
    background-color: #fff3e0;
    color: #f57c00;
  }

  .task-preview .content-icon-large {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  .document-preview .content-icon-large {
    background-color: #ffebee;
    color: #f44336;
  }

  .link-preview .content-icon-large {
    background-color: #e3f2fd;
    color: #2196f3;
  }

  .content-info h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
  }

  .content-meta {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
  }

  .content-body {
    line-height: 1.6;
    color: #444;
  }

  .content-body p {
    margin-bottom: 16px;
  }

  .task-submission {
    margin-top: 24px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .task-submission h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: #333;
  }

  .submission-area {
    text-align: center;
    padding: 20px;
  }

  .no-submission {
    color: #666;
    margin-bottom: 16px;
  }

  .document-actions,
  .link-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
  }

  .document-actions button,
  .link-actions button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary {
    background-color: #2196f3;
    color: white;
    border: none;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #1976d2;
  }

  .btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .btn-secondary {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;
  }

  .btn-secondary:hover {
    background-color: #f5f5f5;
  }

  /* Estilos para vista detallada de contenido */
  .content-details-view {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }

  .content-details-view .content-icon-large {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: #f5f5f5;
    border-radius: 12px;
    flex-shrink: 0;
  }

  .content-details-view .content-icon-large .material-icons {
    font-size: 40px;
    color: #666;
  }

  .content-details-view .content-info {
    flex: 1;
  }

  .content-details-view .content-description {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #333;
  }

  .content-details-view .content-date {
    margin-bottom: 15px;
    color: #666;
  }

  .content-url {
    margin-bottom: 15px;
  }

  .content-url a {
    color: #2196f3;
    text-decoration: none;
    word-break: break-all;
  }

  .content-url a:hover {
    text-decoration: underline;
  }

  .video-preview {
    margin-top: 15px;
  }

  .video-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
  }

  .video-thumbnail .play-icon {
    font-size: 48px;
    color: #2196f3;
    margin-bottom: 10px;
  }

  .video-thumbnail p {
    margin: 0;
    color: #666;
  }

  /* Estilos para tarjetas de vista previa de archivos */
  .document-preview-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-preview-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    gap: 16px;
  }

  .file-icon-container {
    flex-shrink: 0;
  }

  .file-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  .pdf-icon {
    background-color: #ffebee;
    color: #f44336;
  }

  .ppt-icon {
    background-color: #fff3e0;
    color: #ff9800;
  }



  .file-info {
    flex: 1;
  }

  .file-name {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
  }

  .file-details {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .file-details span {
    font-size: 0.85rem;
    color: #666;
    background: white;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }

  .file-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  .btn-download,
  .btn-view {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-download {
    background-color: #2196f3;
    color: white;
  }

  .btn-download:hover {
    background-color: #1976d2;
  }

  .btn-view {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;
  }

  .btn-view:hover {
    background-color: #f5f5f5;
  }

  .file-preview-content {
    padding: 20px;
    text-align: center;
    background: #fafafa;
  }

  .preview-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    color: #666;
  }

  .preview-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.6;
  }

  .preview-thumbnail p {
    margin: 0;
    font-size: 0.9rem;
  }

  /* Estilos para descripción mejorada de documentos */
  .document-description-enhanced {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #2196f3;
  }

  .description-content p {
    margin: 0 0 12px 0;
    color: #333;
    line-height: 1.6;
    font-size: 0.95rem;
  }

  .description-content p:last-child {
    margin-bottom: 0;
  }

  .description-content strong {
    font-weight: 600;
  }

  /* Descripción de contenido igual a los recuadros principales, pero con formato */
  .content-details .content-description {
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.5;
    font-family: "Poppins", "Segoe UI", sans-serif;
    background: none;
    border: none;
    padding: 0;
    margin: 0 0 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    font-weight: 400;
  }

  /* Editor de texto enriquecido igual a los recuadros principales */
  .rich-text-editor .editor-content {
    color: var(--text-color);
    font-size: 1rem;
    min-height: 120px;
    font-family: "Poppins", "Segoe UI", sans-serif;
    background: #fff;
    border-radius: 6px;
    padding: 10px 12px;
    border: 1px solid #d0d0d0;
    outline: none;
    transition: border 0.2s;
  }

  .rich-text-editor .editor-content:focus {
    border: 1.5px solid #2196f3;
  }

  /* Permitir formato en la descripción */
  .content-details .content-description b,
  .content-details .content-description strong {
    font-weight: bold;
  }
  .content-details .content-description i,
  .content-details .content-description em {
    font-style: italic;
  }
  .content-details .content-description s,
  .content-details .content-description del {
    text-decoration: line-through;
  }

  /* Ajuste para textos largos en los modales de vista de contenido */
  .view-content-description {
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-line;
    max-width: 100%;
    font-size: 1.08em;
  }

  /* Estilos para la sección de calificaciones */
  .grades-content {
    margin-top: 20px;
  }

  .grades-table-container {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: white;
  }

  .grades-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
  }

  .grades-table th {
    background: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .grades-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
  }

  .grades-table tbody tr:hover {
    background-color: #f8f9fa;
  }

  .student-info {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
  }

  .student-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
  }

  .student-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .student-details {
    display: flex;
    flex-direction: column;
  }

  .student-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
  }

  .student-grade {
    font-size: 12px;
    color: var(--text-light);
  }

  .grade-input {
    width: 80px;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-size: 14px;
    transition: var(--transition);
  }

  .grade-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
  }

  .final-grade {
    text-align: center;
    font-weight: 600;
  }

  .grade-display {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    background: var(--secondary-color);
    color: var(--text-color);
    font-size: 14px;
    min-width: 50px;
  }

  .grade-display.excellent {
    background: #e8f5e8;
    color: #2e7d32;
  }

  .grade-display.good {
    background: #fff3e0;
    color: #f57c00;
  }

  .grade-display.poor {
    background: #ffebee;
    color: #d32f2f;
  }

  .grade-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .btn-save-grade,
  .btn-view-details {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 18px;
  }

  .btn-save-grade {
    background: var(--success-color);
    color: white;
  }

  .btn-save-grade:hover {
    background: #45a049;
    transform: scale(1.1);
  }

  .btn-view-details {
    background: var(--primary-color);
    color: white;
  }

  .btn-view-details:hover {
    background: #1e40af;
    transform: scale(1.1);
  }

  .export-grades-btn {
    background: var(--success-color);
    color: white;
  }

  .export-grades-btn:hover {
    background: #45a049;
  }

  /* Estilos para botón de calificar participación */
  .grade-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
  }

  .grade-btn:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
    transform: scale(1.1);
  }

  /* Estilos para sección de participación */
  .participation-grades {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid var(--border-color);
  }

  .participation-students {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .participation-student-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
  }

  .participation-student-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .participation-student-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .participation-student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
  }

  .participation-student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .participation-student-details h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
  }

  .participation-student-details p {
    margin: 0;
    font-size: 12px;
    color: var(--text-light);
  }

  .participation-grade {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .participation-grade-input {
    width: 80px;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-size: 14px;
  }

  .participation-grade-display {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    background: var(--secondary-color);
    color: var(--text-color);
  }

  .participation-grade-display.excellent {
    background: #e8f5e8;
    color: #2e7d32;
  }

  .participation-grade-display.good {
    background: #fff3e0;
    color: #f57c00;
  }

  .participation-grade-display.poor {
    background: #ffebee;
    color: #d32f2f;
  }

  /* Estilos para modal de calificación */
  .students-grading-section {
    margin-top: 20px;
  }

  .students-grading-section h4 {
    margin-bottom: 16px;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
  }

  .students-grading-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: #fafafa;
  }

  .student-grading-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: white;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
  }

  .student-grading-item:last-child {
    border-bottom: none;
  }

  .student-grading-item:hover {
    background: #f8f9fa;
  }

  .student-grading-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .student-grading-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
  }

  .student-grading-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .student-grading-details h5 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
  }

  .student-grading-details p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
  }

  .student-grading-input {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .student-grading-input input {
    width: 100px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-size: 14px;
  }

  .student-grading-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
  }

  .save-individual-grade {
    padding: 6px 12px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
  }

  .save-individual-grade:hover {
    background: #45a049;
  }

  .content-info-section {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
  }

  .content-info-section h5 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
  }

  .content-info-section p {
    margin: 0;
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
  }

  /* Estilos para lista de estudiantes en modal de creación */
  .students-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: #fafafa;
  }

  .student-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
  }

  .student-item:last-child {
    border-bottom: none;
  }

  .student-item:hover {
    background: #f8f9fa;
  }

  .student-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .student-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
  }

  .student-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .student-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
  }

  .student-grade-info {
    font-size: 12px;
    color: var(--text-light);
    margin-left: 8px;
  }

  .participation-grade {
    width: 80px;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-size: 14px;
  }

  .participation-grade:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
  }

  .no-students-message {
    padding: 20px;
    text-align: center;
  }

  .no-students-message .material-icons {
    color: #ccc;
  }

  /* Responsive para tabla de calificaciones */
  @media (max-width: 768px) {
    .participation-student-item,
    .student-grading-item,
    .student-item {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .participation-student-info,
    .student-grading-info,
    .student-info {
      justify-content: center;
    }

    .participation-grade,
    .student-grading-input {
      justify-content: center;
    }

    .students-list {
      max-height: 250px;
    }
  }

