-- INSERT DE EJEMPLO: TAREA CON ENTREGAS DE ESTUDIANTES

-- Insertar una nueva tarea en el curso 6, semana 11
INSERT INTO `contenido` (`id`, `sesion_id`, `tipo`, `titulo`, `descripcion`, `imagen`, `archivo`, `url`, `fecha_limite`, `hora_limite`, `puntos`, `activo`, `created_at`, `updated_at`) VALUES
(12, 5, 'tarea', 'Ejercicios de Matemáticas - Fracciones', 
'<p>Resolver los ejercicios del capítulo 5 sobre fracciones. Incluir:</p><ul><li>Suma y resta de fracciones</li><li>Multiplicación y división</li><li>Simplificación de fracciones</li><li>Problemas de aplicación</li></ul><p><strong>Fecha de entrega:</strong> 2025-07-05</p><p><strong>Puntos:</strong> 20</p>', 
NULL, NULL, NULL, '2025-07-05', '23:59:00', 20, 1, NOW(), NOW());

-- Entrega de Diego Martínez López - ENTREGADO
INSERT INTO `entregas` (`id`, `contenido_id`, `estudiante_id`, `archivo`, `comentario_estudiante`, `fecha_entrega`, `estado`, `calificacion`, `comentario_maestro`, `fecha_calificacion`, `created_at`, `updated_at`) VALUES
(1, 12, 2, 'ejercicios_fracciones_diego_martinez.pdf', 
'Profesor, adjunto mis ejercicios resueltos. Tuve algunas dudas en los problemas de aplicación pero creo que están bien resueltos. Espero su retroalimentación.', 
'2025-07-03 14:30:00', 'entregado', 18.5, 
'Excelente trabajo Diego. Los ejercicios están bien resueltos, solo algunos errores menores en la simplificación. Sigue así.', 
'2025-07-06 10:15:00', '2025-07-03 14:30:00', '2025-07-06 10:15:00');

-- Entrega de Sofía Sánchez Díaz - PENDIENTE
INSERT INTO `entregas` (`id`, `contenido_id`, `estudiante_id`, `archivo`, `comentario_estudiante`, `fecha_entrega`, `estado`, `calificacion`, `comentario_maestro`, `fecha_calificacion`, `created_at`, `updated_at`) VALUES
(2, 12, 1, NULL, NULL, 
'2025-06-29 08:00:00', 'pendiente', NULL, NULL, NULL, '2025-06-29 08:00:00', '2025-06-29 08:00:00');
