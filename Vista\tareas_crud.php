<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

// Verificar que se proporcione el ID del curso
if (!isset($_GET['curso_id']) || empty($_GET['curso_id'])) {
    header('Location: cursos_m.php');
    exit;
}

$cursoId = (int)$_GET['curso_id'];
$maestroId = $_SESSION['usuario_id'];

require_once '../Controlador/CursoController.php';

$controller = new CursoController();

// Obtener información del curso
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados para mostrar el nombre del grado
$grados = $controller->obtenerGrados();
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Administrar Tareas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_crud.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <?php if ($maestro && $maestro['foto_perfil']): ?>
                          <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                      <?php else: ?>
                          <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.php">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.php">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.php">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="tareas_m.php?curso_id=<?php echo $cursoId; ?>" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1><?php echo htmlspecialchars($curso['nombre']); ?></h1>
                          <p><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <?php if (!empty($curso['horarios_procesados'])): ?>
                              <?php 
                              $dias = [
                                  'lunes' => 'Lunes',
                                  'martes' => 'Martes',
                                  'miercoles' => 'Miércoles',
                                  'jueves' => 'Jueves',
                                  'viernes' => 'Viernes'
                              ];
                              $contador = 0;
                              foreach ($curso['horarios_procesados'] as $dia => $horario): 
                                  if ($contador < 2): // Mostrar solo los primeros 2 horarios
                              ?>
                                  <div class="schedule-day">
                                      <span class="day-label"><?php echo $dias[$dia]; ?></span>
                                      <span class="day-time"><?php echo htmlspecialchars($horario); ?></span>
                                  </div>
                              <?php 
                                  endif;
                                  $contador++;
                              endforeach; 
                              ?>
                          <?php endif; ?>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Contenido</a>
                  <a href="tareas_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de administración de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Administrar Tareas y Exámenes</h2>
                      <div class="section-actions">
                          <div class="search-container">
                              <input type="text" id="task-search" class="search-input" placeholder="Buscar tarea...">
                              <span class="material-icons search-icon">search</span>
                          </div>
                          <button id="create-task-btn" class="action-btn create-task-btn">
                              <span class="material-icons">add</span>
                              <span>Nueva Tarea</span>
                          </button>
                      </div>
                  </div>
                  
                  <!-- Loading indicator -->
                  <div id="loading-indicator" class="loading-container">
                      <div class="loading-spinner"></div>
                      <p>Cargando tareas...</p>
                  </div>
                  
                  <!-- Error message -->
                  <div id="error-message" class="error-container" style="display: none;">
                      <span class="material-icons">error</span>
                      <p>Error al cargar las tareas. Inténtalo de nuevo.</p>
                      <button class="retry-btn" onclick="location.reload()">Reintentar</button>
                  </div>
                  
                  <!-- Empty state -->
                  <div id="empty-state" class="empty-container" style="display: none;">
                      <span class="material-icons">assignment</span>
                      <h3>No hay tareas disponibles</h3>
                      <p>Aún no se han creado tareas para este curso.</p>
                      <button id="create-first-task-btn" class="action-btn create-first-task-btn">
                          <span class="material-icons">add</span>
                          <span>Crear primera tarea</span>
                      </button>
                  </div>
                  
                  <!-- Tasks table -->
                  <div id="tasks-table-container" class="tasks-table-container" style="display: none;">
                      <div class="table-wrapper">
                          <table class="tasks-table">
                              <thead>
                                  <tr>
                                      <th>Tarea</th>
                                      <th>Tipo</th>
                                      <th>Semana</th>
                                      <th>Fecha Límite</th>
                                      <th>Hora</th>
                                      <th>Puntos</th>
                                      <th>Entregas</th>
                                      <th>Acciones</th>
                                  </tr>
                              </thead>
                              <tbody id="tasks-table-body">
                                  <!-- Las tareas se cargarán dinámicamente aquí con JS -->
                              </tbody>
                          </table>
                      </div>
                      
                      <!-- Pagination -->
                      <div class="pagination-container">
                          <div class="pagination-info">
                              <span>Página <span id="current-page">1</span> de <span id="total-pages">1</span></span>
                          </div>
                          <div class="pagination-controls">
                              <button id="prev-page" class="pagination-btn" disabled>
                                  <span class="material-icons">chevron_left</span>
                              </button>
                              <button id="next-page" class="pagination-btn" disabled>
                                  <span class="material-icons">chevron_right</span>
                              </button>
                          </div>
                      </div>
                  </div>
              </section>
          </div>
      </main>
  </div>
  
  <!-- Modal para crear/editar tarea -->
  <div id="task-form-modal" class="modal-overlay" style="display: none;">
      <div class="modal-content">
          <div class="modal-header">
              <h3 id="task-form-title">Nueva Tarea</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form id="task-form">
                  <input type="hidden" id="task-id" name="task-id" value="">
                  
                  <div class="form-group">
                      <label for="content-type">Tipo de contenido</label>
                      <select id="content-type" name="content-type" required>
                          <option value="">Seleccionar tipo</option>
                          <option value="tarea">Tarea</option>
                          <option value="examen">Examen</option>
                      </select>
                  </div>
                  
                  <div class="form-group">
                      <label for="week-id">Semana académica</label>
                      <select id="week-id" name="week-id">
                          <option value="">Seleccionar semana</option>
                          <!-- Las semanas se cargarán dinámicamente -->
                      </select>
                  </div>
                  
                  <div class="form-group">
                      <label for="task-title">Título</label>
                      <input type="text" id="task-title" name="task-title" required placeholder="Ej: Tarea: Ejercicios de fracciones">
                  </div>
                  
                  <div class="form-group">
                      <label for="task-description">Descripción</label>
                      <textarea id="task-description" name="task-description" rows="4" required placeholder="Describe la tarea o examen..."></textarea>
                  </div>
                  
                  <div class="form-row">
                      <div class="form-group">
                          <label for="due-date">Fecha límite</label>
                          <input type="date" id="due-date" name="due-date" required>
                      </div>
                      <div class="form-group">
                          <label for="due-time">Hora límite</label>
                          <input type="time" id="due-time" name="due-time" value="23:59">
                      </div>
                  </div>
                  
                  <div class="form-group">
                      <label for="points">Puntos</label>
                      <input type="number" id="points" name="points" min="0" max="100" value="10" required>
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal de confirmación para eliminar -->
  <div id="delete-confirm-modal" class="modal-overlay" style="display: none;">
      <div class="modal-content modal-small">
          <div class="modal-header">
              <h3>Confirmar eliminación</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <p id="delete-confirm-message">¿Está seguro de que desea eliminar esta tarea?</p>
              <p class="warning-text">Esta acción no se puede deshacer y eliminará todas las entregas asociadas.</p>
              <div class="form-actions">
                  <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                  <button type="button" class="btn-danger" id="confirm-delete-btn">Eliminar</button>
              </div>
          </div>
      </div>
  </div>

  <!-- Datos para JavaScript -->
  <script>
      window.cursoData = <?php echo json_encode($curso); ?>;
      window.cursoId = <?php echo $cursoId; ?>;
      window.maestroId = <?php echo $maestroId; ?>;
  </script>

  <!-- Scripts -->
  <script src="./Js/tareas_crud.js"></script>
</body>
</html>
