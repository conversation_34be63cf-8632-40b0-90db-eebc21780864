/* Estilos específicos para la sección de mensajes (vista de estudiante) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Sección de mensajes al profesor */
  .teacher-message-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
  }
  
  .message-form {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .message-form h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }
  
  .form-group {
    margin-bottom: 15px;
  }
  
  .form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
  }
  
  .send-message-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .send-message-btn:hover {
    background-color: #1e40af;
  }
  
  .message-history {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .message-history h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }
  
  .message-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .message-item {
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
  }
  
  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .message-info {
    display: flex;
    flex-direction: column;
  }
  
  .message-subject {
    font-weight: 600;
    color: var(--text-color);
  }
  
  .message-date {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .message-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    padding: 3px 10px;
    border-radius: 15px;
  }
  
  .message-status.replied {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
  
  .message-status.pending {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  .message-body {
    padding: 15px;
    background-color: white;
  }
  
  .message-body p {
    font-size: 0.95rem;
    color: var(--text-color);
    line-height: 1.5;
  }
  
  .message-reply {
    padding: 15px;
    background-color: var(--primary-light);
    border-top: 1px solid var(--border-color);
  }
  
  .reply-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .reply-author {
    font-weight: 600;
    color: var(--primary-color);
  }
  
  .reply-date {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .reply-content p {
    font-size: 0.95rem;
    color: var(--text-color);
    line-height: 1.5;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .teacher-message-container {
      grid-template-columns: 1fr;
    }
  }