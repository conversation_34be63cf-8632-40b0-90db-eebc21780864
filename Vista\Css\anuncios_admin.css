/* Estilos específicos para la administración de anuncios */

/* Variables mejoradas */
:root {
    --primary-color: #3b82f6;
    --primary-color-rgb: 59, 130, 246;
    --primary-light: #eff6ff;
    --primary-dark: #1d4ed8;
    --secondary-color: #f8fafc;
    --accent-color: #8b5cf6;
    --text-color: #1e293b;
    --text-color-dark: #0f172a;
    --text-color-light: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* Estados de anuncios */
    --estado-publicado: #10b981;
    --estado-borrador: #f59e0b;
    --estado-archivado: #6b7280;

    /* Sombras mejoradas */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transiciones */
    --transition-fast: all 0.15s ease-in-out;
    --transition-normal: all 0.25s ease-in-out;
    --transition-slow: all 0.35s ease-in-out;
  }

  /* Sección de acciones principales */
  .anuncios-actions {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
  }

  .anuncios-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--info-color));
  }

  .actions-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }

  .actions-left h2 {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--text-color-dark);
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .actions-left p {
    font-size: 1rem;
    color: var(--text-color-light);
    margin: 0;
    font-weight: 500;
  }

  .btn-crear-anuncio {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-normal);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-crear-anuncio::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
  }

  .btn-crear-anuncio:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(var(--primary-color-rgb), 0.4);
  }

  .btn-crear-anuncio:hover::before {
    left: 100%;
  }

  .btn-crear-anuncio .material-icons {
    font-size: 1.5rem;
    transition: var(--transition-fast);
  }

  .btn-crear-anuncio:hover .material-icons {
    transform: rotate(90deg) scale(1.1);
  }

  .btn-crear-anuncio:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.3);
  }
  
  /* Filtros de anuncios mejorados */
  .anuncios-filtros {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
  }

  .anuncios-filtros::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  }

  .filtros-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    align-items: end;
  }

  .filtro-busqueda {
    position: relative;
  }

  .filtro-busqueda label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color-dark);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .input-group {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 12px;
    padding: 0 16px;
    border: 2px solid var(--border-color);
    transition: var(--transition-normal);
    position: relative;
    box-shadow: var(--shadow-xs);
  }

  .input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    transform: translateY(-1px);
  }

  .input-group .material-icons {
    color: var(--text-muted);
    margin-right: 12px;
    font-size: 1.25rem;
    transition: var(--transition-fast);
  }

  .input-group:focus-within .material-icons {
    color: var(--primary-color);
  }

  .input-group input {
    width: 100%;
    padding: 14px 0;
    border: none;
    background: transparent;
    outline: none;
    font-family: inherit;
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
  }

  .input-group input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
  }

  .filtro-estado {
    position: relative;
  }

  .filtro-estado label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color-dark);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .filtro-estado select {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    background-color: white;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    outline: none;
    cursor: pointer;
    appearance: none;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-xs);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 18px;
    padding-right: 45px;
  }

  .filtro-estado select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    transform: translateY(-1px);
  }

  .filtro-estado select:hover {
    border-color: var(--primary-color);
  }
  
  /* Tabla de anuncios mejorada */
  .anuncios-tabla-container {
    margin-bottom: 32px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
  }

  .anuncios-tabla {
    width: 100%;
    border-collapse: collapse;
    background: white;
  }

  .anuncios-tabla th {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f1f5f9 100%);
    color: var(--text-color-dark);
    font-weight: 700;
    text-align: left;
    padding: 20px 24px;
    border-bottom: 2px solid var(--border-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .anuncios-tabla td {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
    transition: var(--transition-fast);
  }

  .anuncios-tabla tbody tr {
    transition: var(--transition-normal);
    position: relative;
  }

  .anuncios-tabla tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0.08) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.15);
  }

  .anuncios-tabla tbody tr:hover td {
    border-bottom-color: rgba(var(--primary-color-rgb), 0.2);
  }
  
  .anuncio-titulo-cell {
    max-width: 450px;
    min-width: 350px;
  }

  .anuncio-info {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .anuncio-imagen-mini {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
  }

  .anuncio-imagen-mini::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.1) 100%);
    z-index: 1;
  }

  .anuncio-imagen-mini img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
  }

  .anuncios-tabla tbody tr:hover .anuncio-imagen-mini {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }

  .anuncio-info h3 {
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 6px 0;
    color: var(--text-color-dark);
    line-height: 1.4;
    transition: var(--transition-fast);
  }

  .anuncios-tabla tbody tr:hover .anuncio-info h3 {
    color: var(--primary-color);
  }

  .anuncio-extracto-mini {
    font-size: 0.875rem;
    color: var(--text-color-light);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 320px;
    line-height: 1.5;
    font-weight: 400;
  }
  
  /* Badges mejorados */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
  }

  .badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .badge:hover::before {
    left: 100%;
  }

  .badge-categoria {
    background: linear-gradient(135deg, var(--primary-light) 0%, #dbeafe 100%);
    color: var(--primary-color);
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  }

  .badge-estado {
    color: white;
    position: relative;
  }

  .badge-publicado {
    background: linear-gradient(135deg, var(--estado-publicado) 0%, #059669 100%);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  }

  .badge-borrador {
    background: linear-gradient(135deg, var(--estado-borrador) 0%, #d97706 100%);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  }

  .badge-archivado {
    background: linear-gradient(135deg, var(--estado-archivado) 0%, #4b5563 100%);
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
  }
  
  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 20px;
    transition: 0.4s;
  }
  
  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
  }
  
  input:checked + .toggle-slider {
    background-color: var(--primary-color);
  }
  
  input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
  }
  
  input:checked + .toggle-slider:before {
    transform: translateX(20px);
  }
  
  input:disabled + .toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Acciones mejoradas */
  .acciones-grupo {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .accion-btn {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 2px solid var(--border-color);
    color: var(--text-color-light);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xs);
  }

  .accion-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: var(--transition-fast);
    z-index: 0;
  }

  .accion-btn .material-icons {
    font-size: 1.2rem;
    position: relative;
    z-index: 1;
    transition: var(--transition-fast);
  }

  .accion-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .accion-btn:hover::before {
    opacity: 0.1;
  }

  .accion-btn:hover .material-icons {
    color: white;
    transform: scale(1.1);
  }

  .ver-btn:hover {
    color: var(--info-color);
    border-color: var(--info-color);
    background: var(--info-color);
  }

  .editar-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: var(--primary-color);
  }

  .eliminar-btn:hover {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background: var(--danger-color);
  }

  .restaurar-btn:hover {
    color: var(--success-color);
    border-color: var(--success-color);
    background: var(--success-color);
  }

  .accion-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
  
  /* Paginación */
  .paginacion {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 30px;
    margin-bottom: 20px;
  }
  
  .paginacion-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--text-color);
  }
  
  .paginacion-btn:hover:not(:disabled) {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .paginacion-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .paginacion-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Modal de anuncio */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 16px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: modalFadeIn 0.3s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    padding: 28px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f1f5f9 100%);
    position: relative;
  }

  .modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  }

  .modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color-dark);
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .modal-close-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-light);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .modal-close-btn:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #ef4444;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
  }

  .modal-close-btn:active {
    transform: scale(0.95);
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }
  
  .modal-fecha {
    font-size: 0.9rem;
    color: var(--text-color-light);
  }
  
  .modal-categoria {
    font-size: 0.9rem;
    padding: 3px 10px;
    border-radius: 20px;
    color: white;
    font-weight: 500;
    background-color: var(--primary-color);
  }
  
  .modal-estado {
    font-size: 0.9rem;
    padding: 3px 10px;
    border-radius: 20px;
    color: white;
    font-weight: 500;
    background-color: var(--estado-publicado);
  }
  
  .modal-titulo {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.3;
  }
  
  .modal-imagen-container {
    width: 100%;
    margin-bottom: 25px;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .modal-imagen-container img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .modal-contenido-texto {
    color: var(--text-color);
    line-height: 1.7;
    font-size: 1.05rem;
    margin-bottom: 30px;
  }
  
  .modal-contenido-texto p {
    margin-bottom: 15px;
  }
  
  .modal-contenido-texto ul {
    margin-bottom: 15px;
    padding-left: 20px;
  }
  
  .modal-contenido-texto li {
    margin-bottom: 5px;
  }
  
  .modal-footer {
    border-top: 2px solid var(--border-color);
    padding: 24px 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f1f5f9 100%);
    gap: 16px;
  }

  .modal-acciones {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
  }
  
  /* Botones mejorados */
  .btn-primary, .btn-secondary, .btn-danger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    border-radius: 12px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    gap: 10px;
    text-decoration: none;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .btn-primary::before,
  .btn-secondary::before,
  .btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before,
  .btn-secondary:hover::before,
  .btn-danger:hover::before {
    left: 100%;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-color-dark);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    color: white;
    border-color: var(--danger-color);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
  }

  .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  }

  .btn-primary:active,
  .btn-secondary:active,
  .btn-danger:active {
    transform: translateY(0);
  }
  
  /* Botones específicos del modal */
  .modal-footer .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: none;
    letter-spacing: 0.3px;
    border-radius: 10px;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 56px; /* Altura fija para alineación */
    min-width: 160px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
  }

  .modal-footer .btn-primary:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
  }

  .modal-footer .btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    color: #475569;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 56px; /* Altura fija para alineación */
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #94a3b8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  /* Botón de peligro específico del modal */
  .modal-footer .btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 56px; /* Altura fija para alineación */
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  }

  .modal-footer .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
  }

  .modal-footer .btn-danger:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
  }

  /* Confirmación Modal */
  .confirmacion-modal {
    max-width: 500px;
  }
  
  .confirmacion-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background-color: rgba(244, 67, 54, 0.1);
    border-radius: 50%;
  }
  
  .confirmacion-icon .material-icons {
    font-size: 40px;
    color: var(--danger-color);
  }
  
  #confirmacion-mensaje {
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.5;
    color: var(--text-color);
    margin-bottom: 20px;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .actions-container {
      flex-direction: column;
      text-align: center;
      gap: 20px;
    }

    .actions-left h2 {
      font-size: 1.5rem;
    }

    .btn-crear-anuncio {
      width: 100%;
      justify-content: center;
    }

    .filtros-container {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .anuncio-titulo-cell {
      max-width: 300px;
    }

    .anuncio-extracto-mini {
      max-width: 200px;
    }
  }
  
  @media (max-width: 768px) {
    .anuncios-tabla {
      display: block;
      overflow-x: auto;
    }
    
    .modal-content {
      width: 95%;
      max-height: 85vh;
    }
    
    .modal-titulo {
      font-size: 1.5rem;
    }
    
    .modal-acciones {
      flex-direction: column;
      width: 100%;
    }
    
    .modal-acciones button {
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    .anuncios-actions {
      padding: 20px;
      margin-bottom: 16px;
    }

    .actions-left h2 {
      font-size: 1.25rem;
    }

    .actions-left p {
      font-size: 0.875rem;
    }

    .btn-crear-anuncio {
      padding: 14px 24px;
      font-size: 0.875rem;
    }

    .anuncio-info {
      flex-direction: column;
      align-items: flex-start;
    }

    .anuncio-imagen-mini {
      margin-bottom: 10px;
    }

    .acciones-grupo {
      flex-wrap: wrap;
    }
  }