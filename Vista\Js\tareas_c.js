document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const taskItems = document.querySelectorAll(".task-item")
    const taskViewModal = document.getElementById("task-view")
    const closeTaskBtn = document.getElementById("close-task-btn")

    // Añadir evento para ver detalles de la tarea (botones de vista)
    const viewTaskBtns = document.querySelectorAll(".view-task-btn")
    viewTaskBtns.forEach((btn) => {
      btn.addEventListener("click", (e) => {
        e.stopPropagation() // Evitar que se propague al contenedor de la tarea
        taskViewModal.classList.add("active")
      })
    })

    // Hacer que las tareas sean clickeables
    const clickableTasks = document.querySelectorAll(".clickable-task")
    clickableTasks.forEach((task) => {
      task.addEventListener("click", () => {
        taskViewModal.classList.add("active")
      })

      // Añadir estilo de cursor pointer
      task.style.cursor = "pointer"

      // Añadir efecto hover
      task.addEventListener("mouseenter", () => {
        task.style.backgroundColor = "#f9f9f9"
      })

      task.addEventListener("mouseleave", () => {
        task.style.backgroundColor = ""
      })
    })
    
    // Cerrar modal de tarea
    if (closeTaskBtn) {
      closeTaskBtn.addEventListener("click", () => {
        taskViewModal.classList.remove("active")
      })
    }
    
    // Cerrar modal al hacer clic fuera del contenido
    if (taskViewModal) {
      taskViewModal.addEventListener("click", (e) => {
        if (e.target === taskViewModal) {
          taskViewModal.classList.remove("active")
        }
      })
    }
    
    // Manejar la subida de archivos
    const fileUploadArea = document.querySelector(".file-upload-area")
    let selectedFiles = []

    if (fileUploadArea) {
      fileUploadArea.addEventListener("click", () => {
        // Crear un input de archivo oculto
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = ".pdf,.jpg,.jpeg,.png"
        fileInput.multiple = true

        // Simular clic en el input
        fileInput.click()

        // Manejar la selección de archivos
        fileInput.addEventListener("change", (e) => {
          const files = e.target.files
          if (files.length > 0) {
            selectedFiles = Array.from(files)
            displaySelectedFiles(selectedFiles)
          }
        })
      })
      
      // Permitir arrastrar y soltar archivos
      fileUploadArea.addEventListener("dragover", (e) => {
        e.preventDefault()
        fileUploadArea.style.backgroundColor = "var(--primary-light)"
        fileUploadArea.style.borderColor = "var(--primary-color)"
      })

      fileUploadArea.addEventListener("dragleave", () => {
        fileUploadArea.style.backgroundColor = "white"
        fileUploadArea.style.borderColor = "var(--border-color)"
      })

      fileUploadArea.addEventListener("drop", (e) => {
        e.preventDefault()
        fileUploadArea.style.backgroundColor = "white"
        fileUploadArea.style.borderColor = "var(--border-color)"

        const files = e.dataTransfer.files
        if (files.length > 0) {
          selectedFiles = Array.from(files)
          displaySelectedFiles(selectedFiles)
        }
      })
    }

    // Función para mostrar archivos seleccionados
    function displaySelectedFiles(files) {
      const fileUploadText = document.querySelector(".file-upload-text")
      const fileUploadHint = document.querySelector(".file-upload-hint")

      if (files.length > 0) {
        let fileList = "<div class='selected-files'>"
        fileList += "<h4>Archivos seleccionados:</h4>"

        files.forEach((file, index) => {
          const fileSize = (file.size / 1024 / 1024).toFixed(2) // MB
          fileList += `
            <div class='file-item'>
              <span class='file-name'>${file.name}</span>
              <span class='file-size'>(${fileSize} MB)</span>
              <button class='remove-file-btn' data-index='${index}'>
                <span class='material-icons'>close</span>
              </button>
            </div>
          `
        })

        fileList += "</div>"
        fileUploadText.innerHTML = fileList
        fileUploadHint.style.display = "none"

        // Agregar eventos para remover archivos
        const removeButtons = document.querySelectorAll(".remove-file-btn")
        removeButtons.forEach(btn => {
          btn.addEventListener("click", (e) => {
            e.stopPropagation()
            const index = parseInt(btn.dataset.index)
            selectedFiles.splice(index, 1)

            if (selectedFiles.length === 0) {
              resetFileUploadArea()
            } else {
              displaySelectedFiles(selectedFiles)
            }
          })
        })
      }
    }

    // Función para resetear el área de subida
    function resetFileUploadArea() {
      const fileUploadText = document.querySelector(".file-upload-text")
      const fileUploadHint = document.querySelector(".file-upload-hint")

      fileUploadText.innerHTML = "Arrastra y suelta archivos aquí o haz clic para seleccionar archivos"
      fileUploadHint.style.display = "block"
      selectedFiles = []
    }
    
    // Manejar el botón de entregar tarea
    const submitBtn = document.querySelector(".submission-actions .primary-btn")
    if (submitBtn) {
      submitBtn.addEventListener("click", () => {
        const comments = document.querySelector(".text-submission-textarea").value

        if (selectedFiles.length === 0 && comments.trim() === "") {
          alert("Por favor, selecciona al menos un archivo o añade comentarios antes de entregar la tarea.")
          return
        }

        // Simular envío de archivos
        if (selectedFiles.length > 0) {
          console.log("Archivos a enviar:", selectedFiles)
          // Aquí iría la lógica real para enviar los archivos al servidor
        }

        if (comments.trim() !== "") {
          console.log("Comentarios:", comments)
          // Aquí iría la lógica para enviar los comentarios
        }

        alert("Tarea enviada correctamente")
        resetFileUploadArea()
        document.querySelector(".text-submission-textarea").value = ""
        taskViewModal.classList.remove("active")
      })
    }

    // Agregar estilos CSS para los archivos seleccionados
    const style = document.createElement("style")
    style.textContent = `
      .selected-files {
        text-align: left;
        margin-top: 10px;
      }

      .selected-files h4 {
        margin-bottom: 10px;
        color: var(--text-color);
        font-size: 1rem;
      }

      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 6px;
        margin-bottom: 8px;
        border: 1px solid var(--border-color);
      }

      .file-name {
        font-weight: 500;
        color: var(--text-color);
        flex: 1;
      }

      .file-size {
        font-size: 0.85rem;
        color: var(--text-light);
        margin-left: 10px;
      }

      .remove-file-btn {
        background: none;
        border: none;
        color: var(--danger-color);
        cursor: pointer;
        padding: 2px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        margin-left: 10px;
      }

      .remove-file-btn:hover {
        background-color: var(--danger-color);
        color: white;
      }

      .remove-file-btn .material-icons {
        font-size: 16px;
      }
    `
    document.head.appendChild(style)
  })