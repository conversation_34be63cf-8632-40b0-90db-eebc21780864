document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const createUserBtn = document.querySelector(".create-user-btn")
    const userModal = document.getElementById("user-modal")
    const userForm = document.getElementById("user-form")
    const userRole = document.getElementById("user-role")
    const roleSpecificFields = document.querySelectorAll(".role-specific-fields")
    const formTabs = document.querySelectorAll(".form-tab")
    const formTabContents = document.querySelectorAll(".form-tab-content")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const roleFilter = document.getElementById("role-filter")
    const searchInput = document.querySelector(".search-input")
    const actionBtns = document.querySelectorAll(".action-btn")
    const paginationBtns = document.querySelectorAll(".pagination-btn")
    const paginationPages = document.querySelectorAll(".pagination-page")
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
    }
  
    function setupEventListeners() {
      // Abrir modal para crear usuario
      if (createUserBtn) {
        createUserBtn.addEventListener("click", openCreateUserModal)
      }
  
      // Cambiar campos según el rol seleccionado
      if (userRole) {
        userRole.addEventListener("change", toggleRoleSpecificFields)
      }
  
      // Cambiar entre pestañas del formulario
      formTabs.forEach((tab) => {
        tab.addEventListener("click", () => {
          switchFormTab(tab.dataset.tab)
        })
      })
  
      // Cerrar modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })
  

  
      // Filtrar usuarios por rol
      if (roleFilter) {
        roleFilter.addEventListener("change", filterUsersByRole)
      }
  
      // Buscar usuarios
      if (searchInput) {
        searchInput.addEventListener("input", searchUsers)
      }
  
      // Acciones de usuario (ver, editar, eliminar)
      actionBtns.forEach((btn) => {
        btn.addEventListener("click", handleUserAction)
      })
  
      // Paginación
      paginationPages.forEach((page) => {
        page.addEventListener("click", changePage)
      })
  
      // Botones de paginación (anterior, siguiente)
      paginationBtns.forEach((btn) => {
        if (!btn.disabled) {
          btn.addEventListener("click", navigatePages)
        }
      })
  
      // Enviar formulario de usuario
      if (userForm) {
        userForm.addEventListener("submit", handleUserFormSubmit)
      }
  
      // Ver ejemplo de creación de cuenta
      const accountExampleBtn = document.querySelector(".account-example-btn")
      if (accountExampleBtn) {
        accountExampleBtn.addEventListener("click", showAccountCreationExample)
      }
  
      // Ver detalles de usuario según su tipo
      const viewBtns = document.querySelectorAll(".action-btn.view-btn")
      viewBtns.forEach((btn) => {
        btn.addEventListener("click", showUserDetails)
      })
    }
  
    // Abrir modal para crear usuario
    function openCreateUserModal() {
      // Limpiar formulario
      userForm.reset()
  
      // Cambiar título del modal
      document.getElementById("user-modal-title").textContent = "Crear Nuevo Usuario"
  
      // Mostrar la primera pestaña
      switchFormTab("basic-info")
  
      // Ocultar campos específicos de roles
      roleSpecificFields.forEach((field) => {
        field.style.display = "none"
      })
  
      // Mostrar modal
      userModal.classList.add("active")
    }
  
    // Abrir modal para editar usuario
    function openEditUserModal(userId) {
      // Limpiar formulario
      userForm.reset()
  
      // Cambiar título del modal
      document.getElementById("user-modal-title").textContent = "Editar Usuario"
  
      // Mostrar la primera pestaña
      switchFormTab("basic-info")
  
      // Simular carga de datos del usuario
      setTimeout(() => {
        // Aquí se cargarían los datos del usuario desde el servidor
        // Por ahora, usamos datos de ejemplo
        document.getElementById("user-first-name").value = "María"
        document.getElementById("user-last-name").value = "López"
        document.getElementById("user-email").value = "<EMAIL>"
        document.getElementById("user-phone").value = "+51 987 654 321"
        document.getElementById("user-address").value = "Av. Principal 123, Lima"
        document.getElementById("user-birthdate").value = "2015-05-15"
        document.getElementById("user-gender").value = "female"
  
        document.getElementById("user-username").value = "maria.lopez"
        document.getElementById("user-status").value = "active"
  
        document.getElementById("user-role").value = "student"
        toggleRoleSpecificFields()
  
        // Simular selección de campos específicos
        document.getElementById("student-grade").value = "3"
        document.getElementById("student-section").value = "A"
        document.getElementById("student-parent").value = "1"
      }, 100)
  
      // Mostrar modal
      userModal.classList.add("active")
    }
  
    // Cambiar campos según el rol seleccionado
    function toggleRoleSpecificFields() {
      const selectedRole = userRole.value
  
      // Ocultar todos los campos específicos
      roleSpecificFields.forEach((field) => {
        field.style.display = "none"
      })
  
      // Mostrar campos según el rol seleccionado
      if (selectedRole) {
        const fieldsToShow = document.getElementById(`${selectedRole}-fields`)
        if (fieldsToShow) {
          fieldsToShow.style.display = "block"
        }
      }
    }
  
    // Cambiar entre pestañas del formulario
    function switchFormTab(tabId) {
      // Desactivar todas las pestañas
      formTabs.forEach((tab) => {
        tab.classList.remove("active")
      })
  
      // Ocultar todos los contenidos
      formTabContents.forEach((content) => {
        content.classList.remove("active")
      })
  
      // Activar la pestaña seleccionada
      const selectedTab = document.querySelector(`.form-tab[data-tab="${tabId}"]`)
      if (selectedTab) {
        selectedTab.classList.add("active")
      }
  
      // Mostrar el contenido seleccionado
      const selectedContent = document.getElementById(tabId)
      if (selectedContent) {
        selectedContent.classList.add("active")
      }
    }
  
    // Cerrar todos los modales
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  

  
    // Filtrar usuarios por rol
    function filterUsersByRole() {
      const roleValue = roleFilter.value
      const userRows = document.querySelectorAll(".users-table tbody tr")

      userRows.forEach((row) => {
        const rowRole = row.getAttribute("data-role")

        if (roleValue === "all" || rowRole === roleValue) {
          row.style.display = ""
        } else {
          row.style.display = "none"
        }
      })
    }
  
    // Buscar usuarios
    function searchUsers() {
      const searchTerm = searchInput.value.toLowerCase()
  
      // Aquí iría la lógica para buscar usuarios
  
      const userRows = document.querySelectorAll(".users-table tbody tr")
  
      userRows.forEach((row) => {
        const userName = row.querySelector(".user-name").textContent.toLowerCase()
        const userEmail = row.querySelector("td:nth-child(3)").textContent.toLowerCase()

        if (userName.includes(searchTerm) || userEmail.includes(searchTerm)) {
          row.style.display = ""
        } else {
          row.style.display = "none"
        }
      })
    }
  
    // Manejar acciones de usuario (ver, editar, eliminar)
    function handleUserAction(e) {
      const button = e.currentTarget
      const userName = button.closest("tr").querySelector(".user-name").textContent

      if (button.classList.contains("view-btn")) {
        viewUser(userName)
      } else if (button.classList.contains("edit-btn")) {
        openEditUserModal(userName)
      } else if (button.classList.contains("delete-btn")) {
        deleteUser(userName)
      }
    }
  
    // Ver detalles de usuario
    function viewUser(userName) {
      // Esta función ahora es manejada por showUserDetails
      console.log(`Ver detalles del usuario: ${userName}`)
    }

    // Eliminar usuario
    function deleteUser(userName) {
      if (confirm(`¿Está seguro que desea eliminar el usuario: ${userName}?`)) {
        // Aquí iría la lógica para eliminar el usuario
        alert(`Usuario ${userName} eliminado correctamente.`)
      }
    }
  
    // Cambiar página
    function changePage(e) {
      const page = e.currentTarget
  
      // Desactivar todas las páginas
      paginationPages.forEach((p) => {
        p.classList.remove("active")
      })
  
      // Activar la página seleccionada
      page.classList.add("active")
  
      // Aquí iría la lógica para cargar los usuarios de la página seleccionada
      console.log(`Cambiar a página ${page.textContent}`)
    }
  
    // Navegar entre páginas (anterior, siguiente)
    function navigatePages(e) {
      const button = e.currentTarget
      const currentPage = document.querySelector(".pagination-page.active")
      const currentPageNum = Number.parseInt(currentPage.textContent)
  
      if (button.classList.contains("prev-btn")) {
        // Ir a la página anterior
        if (currentPageNum > 1) {
          const prevPage = document.querySelector(`.pagination-page:nth-child(${currentPageNum - 1})`)
          if (prevPage) {
            prevPage.click()
          }
        }
      } else if (button.classList.contains("next-btn")) {
        // Ir a la página siguiente
        const nextPage = document.querySelector(`.pagination-page:nth-child(${currentPageNum + 1})`)
        if (nextPage) {
          nextPage.click()
        }
      }
    }
  
    // Manejar envío del formulario de usuario
    function handleUserFormSubmit(e) {
      e.preventDefault()
  
      // Validar formulario
      if (!validateUserForm()) {
        return
      }
  
      // Aquí iría la lógica para guardar el usuario
      alert("Usuario guardado correctamente.")
  
      // Cerrar modal
      closeAllModals()
    }
  
    // Validar formulario de usuario
    function validateUserForm() {
      // Obtener valores del formulario
      const firstName = document.getElementById("user-first-name").value
      const lastName = document.getElementById("user-last-name").value
      const email = document.getElementById("user-email").value
      const username = document.getElementById("user-username").value
      const password = document.getElementById("user-password").value
      const confirmPassword = document.getElementById("user-confirm-password").value
      const role = document.getElementById("user-role").value
  
      // Validar campos obligatorios
      if (!firstName || !lastName || !email || !username || !password || !confirmPassword || !role) {
        alert("Por favor, complete todos los campos obligatorios.")
        return false
      }
  
      // Validar que las contraseñas coincidan
      if (password !== confirmPassword) {
        alert("Las contraseñas no coinciden.")
        return false
      }
  
      // Validar formato de correo electrónico
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        alert("Por favor, ingrese un correo electrónico válido.")
        return false
      }
  
      return true
    }
  
    // Mostrar ejemplo de creación de cuenta
    function showAccountCreationExample() {
      const modal = document.getElementById("account-creation-example-modal")
      if (modal) {
        modal.classList.add("active")
      }
    }
  
    // Mostrar detalles de usuario según su tipo
    function showUserDetails(e) {
      const button = e.currentTarget
      const row = button.closest("tr")
      const userName = row.querySelector(".user-name").textContent
      const userRole = row.querySelector(".role-col").textContent.trim()

      // Debug: mostrar el rol detectado
      console.log("Rol detectado:", userRole)

      // Configurar el modal con la información del usuario
      const modal = document.getElementById("user-details-modal")
      if (modal) {
        // Actualizar información básica
        document.getElementById("detail-user-name").textContent = userName

        // Actualizar información del grado según el tipo de usuario
        const gradeElement = document.getElementById("detail-user-grade")
        const guardianTypeElement = document.getElementById("detail-guardian-type")

        if (userRole.toLowerCase().includes("estudiante")) {
          document.getElementById("detail-user-role").textContent = userRole
          gradeElement.textContent = "Grado: 5° Primaria"
          guardianTypeElement.textContent = ""
        } else if (userRole.toLowerCase().includes("maestro")) {
          document.getElementById("detail-user-role").textContent = userRole
          gradeElement.textContent = "Grado Tutor: 5° Primaria"
          guardianTypeElement.textContent = ""
        } else if (userRole.toLowerCase().includes("padre") || userRole.toLowerCase().includes("madre")) {
          // Para padres, mostrar el rol específico (Padre, Madre, Tutor legal)
          document.getElementById("detail-user-role").textContent = "Padre" // Esto se puede cambiar dinámicamente
          gradeElement.textContent = ""
          guardianTypeElement.textContent = "Tipo de Apoderado: Apoderado Principal"
        } else {
          document.getElementById("detail-user-role").textContent = userRole
          gradeElement.textContent = ""
          guardianTypeElement.textContent = ""
        }

        // Ocultar todos los detalles específicos de rol
        document.querySelectorAll(".role-specific-details").forEach(el => {
          el.style.display = "none"
        })

        // Mostrar detalles según el rol
        if (userRole.toLowerCase().includes("estudiante")) {
          document.getElementById("student-details").style.display = "block"
        } else if (userRole.toLowerCase().includes("maestro")) {
          document.getElementById("teacher-details").style.display = "block"
        } else if (userRole.toLowerCase().includes("padre") || userRole.toLowerCase().includes("madre")) {
          document.getElementById("parent-details").style.display = "block"
        } else if (userRole.toLowerCase().includes("administrador")) {
          document.getElementById("admin-details").style.display = "block"
        }

        // Mostrar el modal
        modal.classList.add("active")
      }
    }

    // Variables para controlar el paso actual
    let currentStep = 1

    // Función para mostrar el paso específico
    function showStep(step) {
      currentStep = step

      // Ocultar todos los contenidos de tabs y botones
      document.querySelectorAll('.form-tab-content').forEach(content => {
        content.classList.remove('active')
      })
      document.querySelectorAll('.form-tab').forEach(tab => {
        tab.classList.remove('active')
      })
      document.querySelectorAll('.step-buttons').forEach(buttons => {
        buttons.style.display = 'none'
      })

      // Mostrar el contenido y botones del paso actual
      if (step === 1) {
        document.getElementById('basic-info').classList.add('active')
        document.querySelector('[data-tab="basic-info"]').classList.add('active')
        document.getElementById('basic-info-buttons').style.display = 'flex'
      } else if (step === 2) {
        document.getElementById('account-info').classList.add('active')
        document.querySelector('[data-tab="account-info"]').classList.add('active')
        document.getElementById('account-info-buttons').style.display = 'flex'
      } else if (step === 3) {
        document.getElementById('role-info').classList.add('active')
        document.querySelector('[data-tab="role-info"]').classList.add('active')
        document.getElementById('role-config-buttons').style.display = 'flex'
      }
    }

    // Función para avanzar al siguiente paso
    function nextStep(fromStep) {
      if (validateStep(fromStep)) {
        showStep(fromStep + 1)
      }
    }

    // Función para retroceder al paso anterior
    function previousStep(toStep) {
      showStep(toStep)
    }

    // Función para validar cada paso
    function validateStep(step) {
      if (step === 1) {
        // Validar información básica
        const firstName = document.getElementById('user-first-name').value.trim()
        const lastName = document.getElementById('user-last-name').value.trim()
        const email = document.getElementById('user-email').value.trim()
        const phone = document.getElementById('user-phone').value.trim()

        if (!firstName || !lastName || !email || !phone) {
          alert('Por favor, complete todos los campos de información básica.')
          return false
        }

        // Validar formato de email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(email)) {
          alert('Por favor, ingrese un correo electrónico válido.')
          return false
        }

        return true
      } else if (step === 2) {
        // Validar información de cuenta
        const username = document.getElementById('user-username').value.trim()
        const password = document.getElementById('user-password').value
        const confirmPassword = document.getElementById('user-confirm-password').value

        if (!username || !password || !confirmPassword) {
          alert('Por favor, complete todos los campos de información de cuenta.')
          return false
        }

        if (password !== confirmPassword) {
          alert('Las contraseñas no coinciden.')
          return false
        }

        if (password.length < 6) {
          alert('La contraseña debe tener al menos 6 caracteres.')
          return false
        }

        return true
      }

      return true
    }

    // Cerrar modal de creación de usuario
    function closeCreateUserModal() {
      const modal = document.getElementById("user-modal")
      if (modal) {
        modal.classList.remove("active")
        document.getElementById("user-form").reset()
        // Resetear a la primera sección
        showStep(1)
      }
    }

    // Función para mostrar modal de cambio de estado de pago
    function showChangePaymentModal(paymentId, currentStatus) {
      const changePaymentModal = document.getElementById("change-payment-modal")

      // Datos de ejemplo para los pagos
      const paymentData = {
        'matricula-2025': { concept: 'Matrícula 2025', amount: 'S/. 500.00', dueDate: '15/01/2025' },
        'marzo-2025': { concept: 'Mensualidad Marzo', amount: 'S/. 450.00', dueDate: '05/03/2025' },
        'abril-2025': { concept: 'Mensualidad Abril', amount: 'S/. 450.00', dueDate: '05/04/2025' },
        'mayo-2025': { concept: 'Mensualidad Mayo', amount: 'S/. 450.00', dueDate: '05/05/2025' }
      }

      const payment = paymentData[paymentId]
      if (!payment) return

      // Crear el contenido del modal
      const modalContent = `
        <div class="modal-header">
          <h3>Modificar Estado de Pago</h3>
          <button class="modal-close-btn">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="payment-info">
            <h4>${payment.concept}</h4>
            <div class="payment-details">
              <p><strong>Monto:</strong> ${payment.amount}</p>
              <p><strong>Fecha límite:</strong> ${payment.dueDate}</p>
            </div>
          </div>

          <div class="form-group">
            <label>Estado actual:</label>
            <div class="status-badge ${currentStatus}">${getPaymentStatusText(currentStatus)}</div>
          </div>

          <div class="form-group">
            <label>Nuevo estado:</label>
            <div class="payment-status-options">
              <div class="status-option pending ${currentStatus === "pending" ? "active" : ""}" data-status="pending" onclick="selectPaymentStatus(this)">Pendiente</div>
              <div class="status-option complete ${currentStatus === "complete" ? "active" : ""}" data-status="complete" onclick="selectPaymentStatus(this)">Pagado</div>
              <div class="status-option overdue ${currentStatus === "overdue" ? "active" : ""}" data-status="overdue" onclick="selectPaymentStatus(this)">Vencido</div>
            </div>
          </div>

          <div class="form-group">
            <label for="payment-notes">Notas administrativas (opcional):</label>
            <textarea id="payment-notes" placeholder="Ej: Pago realizado en efectivo el día..." rows="3"></textarea>
          </div>

          <div class="modal-actions">
            <button class="btn-secondary modal-close-btn">Cancelar</button>
            <button class="btn-primary" onclick="savePaymentStatus('${paymentId}')">Guardar Cambios</button>
          </div>
        </div>
      `

      changePaymentModal.querySelector(".modal-content").innerHTML = modalContent

      // Agregar event listeners para cerrar el modal
      changePaymentModal.querySelectorAll(".modal-close-btn").forEach(btn => {
        btn.addEventListener("click", () => {
          changePaymentModal.classList.remove("active")
        })
      })

      // Mostrar el modal
      changePaymentModal.classList.add("active")
    }

    // Función para obtener el texto del estado de pago
    function getPaymentStatusText(status) {
      switch(status) {
        case 'complete': return 'Pagado'
        case 'pending': return 'Pendiente'
        case 'overdue': return 'Vencido'
        default: return 'Desconocido'
      }
    }

    // Función para seleccionar nuevo estado de pago
    function selectPaymentStatus(element) {
      // Remover clase active de todas las opciones
      element.parentElement.querySelectorAll('.status-option').forEach(opt => {
        opt.classList.remove('active')
      })
      // Agregar clase active al elemento seleccionado
      element.classList.add('active')
    }

    // Función para guardar el nuevo estado de pago
    function savePaymentStatus(paymentId) {
      const activeOption = document.querySelector('.payment-status-options .status-option.active')
      const notes = document.getElementById('payment-notes').value

      if (!activeOption) {
        alert('Por favor seleccione un estado')
        return
      }

      const newStatus = activeOption.dataset.status

      // Simular guardado (aquí se haría la llamada al servidor)
      console.log('Guardando estado de pago:', { paymentId, newStatus, notes })

      // Mostrar mensaje de éxito
      alert('Estado de pago actualizado correctamente')

      // Cerrar modal
      document.getElementById("change-payment-modal").classList.remove("active")

      // Actualizar la tabla de pagos (simulado)
      updatePaymentTable(paymentId, newStatus)
    }

    // Función para actualizar la tabla de pagos
    function updatePaymentTable(paymentId, newStatus) {
      // Esta función actualizaría la tabla de pagos en la interfaz
      // Por ahora solo mostramos un mensaje
      console.log('Actualizando tabla de pagos:', paymentId, newStatus)
    }

    // Hacer las funciones globales para que puedan ser llamadas desde el HTML
    window.nextStep = nextStep
    window.previousStep = previousStep
    window.closeCreateUserModal = closeCreateUserModal
    window.redirectToJustifications = redirectToJustifications
    window.showChangePaymentModal = showChangePaymentModal
    window.selectPaymentStatus = selectPaymentStatus
    window.savePaymentStatus = savePaymentStatus
  })

  // Función para redirigir a la página de justificaciones de asistencia
  function redirectToJustifications() {
    // Obtener información del maestro actual
    const teacherName = document.getElementById("detail-user-name").textContent
    const teacherRoleElement = document.getElementById("detail-user-role")
    const teacherGradeElement = document.getElementById("detail-user-grade")

    // Extraer información adicional si está disponible
    let teacherRole = "Matemáticas" // Valor por defecto
    let teacherGrade = "5° Primaria" // Valor por defecto

    if (teacherRoleElement && teacherRoleElement.textContent.includes("Maestro")) {
      // Intentar extraer la especialidad del rol si está disponible
      teacherRole = "Matemáticas" // Se podría mejorar para extraer dinámicamente
    }

    if (teacherGradeElement && teacherGradeElement.textContent.includes("Grado")) {
      teacherGrade = teacherGradeElement.textContent.replace("Grado Tutor: ", "")
    }

    // Construir URL con parámetros
    const params = new URLSearchParams({
      teacher: teacherName,
      role: teacherRole,
      grade: teacherGrade
    })

    // Redirigir a la nueva página
    window.location.href = `justificaciones_asistencia.html?${params.toString()}`
  }