document.addEventListener("DOMContentLoaded", function () {
    // Elementos del DOM
    const addStudentBtn = document.getElementById("add-student-btn")
    const exportExcelBtn = document.getElementById("export-excel-btn")
    const studentSearch = document.getElementById("student-search")
    const searchAvailableStudents = document.getElementById("search-available-students")
    
    // Modales
    const addStudentModal = document.getElementById("add-student-modal")
    const deleteStudentModal = document.getElementById("delete-student-modal")

    // Botones de modal
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const cancelAddStudentBtn = document.getElementById("cancel-add-student")
    const cancelDeleteStudentBtn = document.getElementById("cancel-delete-student")
    const confirmDeleteStudentBtn = document.getElementById("confirm-delete-student")

    // Variables globales
    const cursoId = document.body.dataset.cursoId || (new URLSearchParams(window.location.search)).get('curso_id');
    console.log('Curso ID detectado:', cursoId);

    if (!cursoId) {
        console.error('No se pudo obtener el ID del curso');
        alert('Error: No se pudo identificar el curso');
        return;
    }

    let studentToDelete = null;
    
    // Event listeners
    if (addStudentBtn) {
        addStudentBtn.addEventListener("click", openAddStudentModal)
    }
    
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener("click", handleExportExcel)
    }
    
    if (studentSearch) {
        studentSearch.addEventListener("input", handleStudentSearch)
    }
    
    if (searchAvailableStudents) {
        searchAvailableStudents.addEventListener("input", handleAvailableStudentsSearch)
    }
    
    // Event listeners para cerrar modales
    modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", closeAllModals)
    })
    
    if (cancelAddStudentBtn) {
        cancelAddStudentBtn.addEventListener("click", closeAllModals)
    }
    
    if (cancelDeleteStudentBtn) {
        cancelDeleteStudentBtn.addEventListener("click", closeAllModals)
    }

    if (confirmDeleteStudentBtn) {
        confirmDeleteStudentBtn.addEventListener("click", handleDeleteStudent)
    }
    
    // Event listeners para botones de acción de estudiantes
    document.addEventListener("click", function(e) {
        if (e.target.closest(".delete-btn")) {
            const studentCard = e.target.closest(".student-card")
            const studentId = e.target.closest(".delete-btn").getAttribute('data-student-id');
            const studentName = e.target.closest(".delete-btn").getAttribute('data-student-name');
            showDeleteConfirmation(studentId, studentName)
        } else if (e.target.closest(".add-student-btn")) {
            const studentId = e.target.closest(".add-student-btn").dataset.studentId
            addStudentToCourse(studentId)
        }
    })
    
    // Funciones principales
    function openAddStudentModal() {
        addStudentModal.classList.add("active")
        // Limpiar búsqueda
        if (searchAvailableStudents) {
            searchAvailableStudents.value = ""
        }
        loadAvailableStudents()
    }

    // Función para cargar estudiantes disponibles
    async function loadAvailableStudents() {
        const availableStudentsList = document.getElementById("available-students-list")
        if (!availableStudentsList) return;

        try {
            availableStudentsList.innerHTML = '<div class="loading"><span class="material-icons">hourglass_empty</span><p>Cargando estudiantes disponibles...</p></div>'

            console.log('Cargando estudiantes disponibles para curso:', cursoId);
            const response = await fetch(`../api_inscripciones.php?action=disponibles&curso_id=${cursoId}`, {
                credentials: 'same-origin',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Respuesta de estudiantes disponibles:', data);

            if (!data.success) {
                throw new Error(data.error || 'Error desconocido');
            }

            if (!data.data || data.data.length === 0) {
                availableStudentsList.innerHTML = '<div class="no-students"><p>No hay estudiantes disponibles para inscribir.</p></div>'
                return;
            }

            // Renderizar estudiantes disponibles
            availableStudentsList.innerHTML = data.data.map(student => `
                <div class="student-search-item">
                    <div class="student-avatar">
                        <img src="${student.avatar}" alt="Foto de ${student.nombre}">
                    </div>
                    <div class="student-info">
                        <h4>${student.nombre}</h4>
                        <p class="student-grade">${student.grado}</p>
                    </div>
                    <button class="add-student-btn" data-student-id="${student.id}">
                        <span class="material-icons">person_add</span>
                        Agregar
                    </button>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error cargando estudiantes disponibles:', error);
            availableStudentsList.innerHTML = `<div class="error"><p>Error: ${error.message}</p></div>`
        }
    }
    
    function openDeleteStudentModal(studentName, studentCard) {
        studentToDelete = studentCard
        document.getElementById("student-to-delete-name").textContent = studentName
        deleteStudentModal.classList.add("active")
    }

    function closeAllModals() {
        document.querySelectorAll(".modal-overlay").forEach(modal => {
            modal.classList.remove("active")
        })
        studentToDelete = null
    }
    
    function handleStudentSearch() {
        const searchTerm = studentSearch.value.toLowerCase()
        const studentCards = document.querySelectorAll(".student-card")

        studentCards.forEach(card => {
            const studentName = card.querySelector("h3").textContent.toLowerCase()

            if (studentName.includes(searchTerm)) {
                card.style.display = "block"
            } else {
                card.style.display = "none"
            }
        })

        updateStudentCount()
    }
    
    function handleAvailableStudentsSearch() {
        const searchTerm = searchAvailableStudents.value.toLowerCase()
        const studentItems = document.querySelectorAll(".student-search-item")

        studentItems.forEach(item => {
            const studentName = item.querySelector("h4").textContent.toLowerCase()

            if (studentName.includes(searchTerm)) {
                item.style.display = "flex"
            } else {
                item.style.display = "none"
            }
        })
    }
    
    function showAllAvailableStudents() {
        const studentItems = document.querySelectorAll(".student-search-item")
        studentItems.forEach(item => {
            item.style.display = "flex"
        })
    }
    
    // Función para agregar estudiante al curso
    async function addStudentToCourse(studentId) {
        if (!studentId || !cursoId) {
            alert('Error: Datos incompletos');
            return;
        }

        try {
            console.log('Agregando estudiante:', {studentId, cursoId});
            const resp = await fetch(`../api_inscripciones.php?action=agregar&curso_id=${cursoId}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({ estudiante_id: studentId })
            });
            const data = await resp.json();
            console.log('Respuesta API agregar:', data);
            if (!data.success) {
                if (data.error && data.error.includes('Duplicate')) {
                    throw new Error('Este estudiante ya está inscrito en el curso.');
                }
                throw new Error(data.error || 'Error al agregar');
            }
        } catch (err) {
            console.error('Error:', err);
            alert(err.message);
            return;
        }

        // Cerrar modal y recargar página
        closeAllModals();
        alert('Estudiante agregado exitosamente');
        window.location.reload();
    }
    
    // Función para mostrar confirmación de eliminación
    function showDeleteConfirmation(studentId, studentName) {
        studentToDelete = { id: studentId, name: studentName };

        const deleteMessage = document.getElementById('delete-student-message');
        if (deleteMessage) {
            deleteMessage.textContent = `¿Estás seguro de que deseas eliminar a ${studentName} del curso?`;
        }

        deleteStudentModal.classList.add("active");
    }

    // Confirmar eliminación
    async function handleDeleteStudent() {
        if (!studentToDelete) return;

        try {
            const response = await fetch(`../api_inscripciones.php?action=eliminar&curso_id=${cursoId}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({ estudiante_id: studentToDelete.id })
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Error al eliminar');
            }

            closeAllModals();
            alert('Estudiante eliminado del curso exitosamente');
            window.location.reload();

        } catch (error) {
            console.error('Error eliminando estudiante:', error);
            alert('Error: ' + error.message);
        }
    }
    
    function handleExportExcel() {
        // Obtener datos de estudiantes
        const studentCards = document.querySelectorAll(".student-card")
        const studentsData = []

        studentCards.forEach((card, index) => {
            const name = card.querySelector("h3").textContent
            const grade = card.querySelector(".student-grade").textContent
            studentsData.push({
                'No.': index + 1,
                'Nombre': name,
                'Grado': grade
            })
        })

        // Crear contenido CSV
        const headers = ['No.', 'Nombre', 'Grado']
        let csvContent = headers.join(',') + '\n'

        studentsData.forEach(student => {
            const row = headers.map(header => `"${student[header]}"`).join(',')
            csvContent += row + '\n'
        })

        // Crear y descargar archivo
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)

        link.setAttribute('href', url)
        link.setAttribute('download', 'lista_estudiantes_matematicas.csv')
        link.style.visibility = 'hidden'

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Mostrar mensaje de éxito
        showSuccessMessage('Lista de estudiantes exportada exitosamente en formato Excel.')
    }
    
    function createStudentCard(name, grade, avatar) {
        const card = document.createElement("div")
        card.className = "student-card enrolled"

        card.innerHTML = `
            <div class="student-avatar">
                <img src="${avatar}" alt="Foto de estudiante">
            </div>
            <div class="student-info">
                <h3>${name}</h3>
                <p class="student-grade">${grade}</p>
            </div>
            <div class="student-actions">
                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="${name}">
                    <span class="material-icons">delete</span>
                </button>
            </div>
        `

        return card
    }
    
    function updateStudentCount() {
        const visibleStudents = document.querySelectorAll(".student-card:not([style*='display: none'])")
        const totalStudents = document.querySelectorAll(".student-card").length
        const studentCountElement = document.querySelector(".student-count")
        
        if (studentCountElement) {
            studentCountElement.textContent = `${totalStudents} estudiantes`
        }
    }
    
    function showSuccessMessage(message) {
        // Crear elemento de mensaje
        const messageElement = document.createElement("div")
        messageElement.className = "success-message"
        messageElement.innerHTML = `
            <span class="material-icons">check_circle</span>
            <span>${message}</span>
        `
        
        // Agregar estilos
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.3s ease-out;
        `
        
        // Agregar al DOM
        document.body.appendChild(messageElement)
        
        // Remover después de 3 segundos
        setTimeout(() => {
            messageElement.style.animation = "slideOutRight 0.3s ease-in"
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement)
                }
            }, 300)
        }, 3000)
    }
    
    // Agregar estilos de animación
    const style = document.createElement("style")
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `
    document.head.appendChild(style)

    // Función para cargar estudiantes inscritos
    async function loadEnrolledStudents() {
        try {
            console.log('Cargando estudiantes inscritos para curso:', cursoId);
            const response = await fetch(`../api_inscripciones.php?action=inscritos&curso_id=${cursoId}`, {
                credentials: 'same-origin',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Respuesta de estudiantes inscritos:', data);

            if (!data.success) {
                throw new Error(data.error || 'Error desconocido');
            }

            // Actualizar contador
            const studentCount = document.querySelector('.student-count');
            if (studentCount) {
                studentCount.textContent = `${data.data.length} estudiantes`;
            }

            // Renderizar estudiantes
            const studentsGrid = document.querySelector('.students-grid');
            if (studentsGrid && data.data.length > 0) {
                studentsGrid.innerHTML = data.data.map(student => `
                    <div class="student-card enrolled">
                        <div class="student-avatar">
                            <img src="${student.avatar}" alt="Foto de ${student.nombre}">
                        </div>
                        <div class="student-info">
                            <h3>${student.nombre}</h3>
                            <p class="student-grade">${student.grado}</p>
                        </div>
                        <div class="student-actions">
                            <button class="student-action-btn delete-btn" title="Eliminar estudiante"
                                    data-student-id="${student.id}" data-student-name="${student.nombre}">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </div>
                `).join('');
            } else if (studentsGrid) {
                studentsGrid.innerHTML = '<div class="no-students"><p>No hay estudiantes inscritos en este curso.</p></div>';
            }

        } catch (error) {
            console.error('Error cargando estudiantes inscritos:', error);
        }
    }

    // Cargar estudiantes inscritos al cargar la página
    loadEnrolledStudents();
})
