document.addEventListener("DOMContentLoaded", function () {
    // Elementos del DOM
    const addStudentBtn = document.getElementById("add-student-btn")
    const exportExcelBtn = document.getElementById("export-excel-btn")
    const studentSearch = document.getElementById("student-search")
    const searchAvailableStudents = document.getElementById("search-available-students")
    
    // Modales
    const addStudentModal = document.getElementById("add-student-modal")
    const deleteStudentModal = document.getElementById("delete-student-modal")

    // Botones de modal
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const cancelAddStudentBtn = document.getElementById("cancel-add-student")
    const cancelDeleteStudentBtn = document.getElementById("cancel-delete-student")
    const confirmDeleteStudentBtn = document.getElementById("confirm-delete-student")

    // Variables globales
    let studentToDelete = null
    
    // Event listeners
    if (addStudentBtn) {
        addStudentBtn.addEventListener("click", openAddStudentModal)
    }
    
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener("click", handleExportExcel)
    }
    
    if (studentSearch) {
        studentSearch.addEventListener("input", handleStudentSearch)
    }
    
    if (searchAvailableStudents) {
        searchAvailableStudents.addEventListener("input", handleAvailableStudentsSearch)
    }
    
    // Event listeners para cerrar modales
    modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", closeAllModals)
    })
    
    if (cancelAddStudentBtn) {
        cancelAddStudentBtn.addEventListener("click", closeAllModals)
    }
    
    if (cancelDeleteStudentBtn) {
        cancelDeleteStudentBtn.addEventListener("click", closeAllModals)
    }

    if (confirmDeleteStudentBtn) {
        confirmDeleteStudentBtn.addEventListener("click", handleDeleteStudent)
    }
    
    // Event listeners para botones de acción de estudiantes
    document.addEventListener("click", function(e) {
        if (e.target.closest(".delete-btn")) {
            const studentCard = e.target.closest(".student-card")
            const studentName = studentCard.querySelector("h3").textContent
            openDeleteStudentModal(studentName, studentCard)
        } else if (e.target.closest(".add-student-btn")) {
            const studentId = e.target.closest(".add-student-btn").dataset.studentId
            const studentItem = e.target.closest(".student-search-item")
            handleAddStudentToCourse(studentId, studentItem)
        }
    })
    
    // Funciones principales
    function openAddStudentModal() {
        addStudentModal.classList.add("active")
        // Limpiar búsqueda
        if (searchAvailableStudents) {
            searchAvailableStudents.value = ""
        }
        // Mostrar todos los estudiantes disponibles
        showAllAvailableStudents()
    }
    
    function openDeleteStudentModal(studentName, studentCard) {
        studentToDelete = studentCard
        document.getElementById("student-to-delete-name").textContent = studentName
        deleteStudentModal.classList.add("active")
    }

    function closeAllModals() {
        document.querySelectorAll(".modal-overlay").forEach(modal => {
            modal.classList.remove("active")
        })
        studentToDelete = null
    }
    
    function handleStudentSearch() {
        const searchTerm = studentSearch.value.toLowerCase()
        const studentCards = document.querySelectorAll(".student-card")

        studentCards.forEach(card => {
            const studentName = card.querySelector("h3").textContent.toLowerCase()

            if (studentName.includes(searchTerm)) {
                card.style.display = "block"
            } else {
                card.style.display = "none"
            }
        })

        updateStudentCount()
    }
    
    function handleAvailableStudentsSearch() {
        const searchTerm = searchAvailableStudents.value.toLowerCase()
        const studentItems = document.querySelectorAll(".student-search-item")

        studentItems.forEach(item => {
            const studentName = item.querySelector("h4").textContent.toLowerCase()

            if (studentName.includes(searchTerm)) {
                item.style.display = "flex"
            } else {
                item.style.display = "none"
            }
        })
    }
    
    function showAllAvailableStudents() {
        const studentItems = document.querySelectorAll(".student-search-item")
        studentItems.forEach(item => {
            item.style.display = "flex"
        })
    }
    
    function handleAddStudentToCourse(studentId, studentItem) {
        // Obtener datos del estudiante
        const studentName = studentItem.querySelector("h4").textContent
        const studentGrade = studentItem.querySelector(".student-grade").textContent
        const studentAvatar = studentItem.querySelector("img").src

        // Crear nueva tarjeta de estudiante
        const newStudentCard = createStudentCard(studentName, studentGrade, studentAvatar)

        // Agregar a la lista de estudiantes del curso
        const studentsGrid = document.querySelector(".students-grid")
        studentsGrid.appendChild(newStudentCard)

        // Remover de la lista de estudiantes disponibles
        studentItem.remove()

        // Actualizar contador
        updateStudentCount()

        // Mostrar mensaje de éxito
        showSuccessMessage(`${studentName} ha sido agregado al curso exitosamente.`)

        // Cerrar modal
        closeAllModals()
    }
    
    function handleDeleteStudent() {
        if (studentToDelete) {
            const studentName = studentToDelete.querySelector("h3").textContent

            // Eliminar la tarjeta del estudiante
            studentToDelete.remove()

            // Actualizar contador
            updateStudentCount()

            // Mostrar mensaje de éxito
            showSuccessMessage(`${studentName} ha sido eliminado correctamente.`)

            // Cerrar modal
            closeAllModals()
        }
    }
    
    function handleExportExcel() {
        // Obtener datos de estudiantes
        const studentCards = document.querySelectorAll(".student-card")
        const studentsData = []

        studentCards.forEach((card, index) => {
            const name = card.querySelector("h3").textContent
            const grade = card.querySelector(".student-grade").textContent
            studentsData.push({
                'No.': index + 1,
                'Nombre': name,
                'Grado': grade
            })
        })

        // Crear contenido CSV
        const headers = ['No.', 'Nombre', 'Grado']
        let csvContent = headers.join(',') + '\n'

        studentsData.forEach(student => {
            const row = headers.map(header => `"${student[header]}"`).join(',')
            csvContent += row + '\n'
        })

        // Crear y descargar archivo
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)

        link.setAttribute('href', url)
        link.setAttribute('download', 'lista_estudiantes_matematicas.csv')
        link.style.visibility = 'hidden'

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Mostrar mensaje de éxito
        showSuccessMessage('Lista de estudiantes exportada exitosamente en formato Excel.')
    }
    
    function createStudentCard(name, grade, avatar) {
        const card = document.createElement("div")
        card.className = "student-card enrolled"

        card.innerHTML = `
            <div class="student-avatar">
                <img src="${avatar}" alt="Foto de estudiante">
            </div>
            <div class="student-info">
                <h3>${name}</h3>
                <p class="student-grade">${grade}</p>
            </div>
            <div class="student-actions">
                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="${name}">
                    <span class="material-icons">delete</span>
                </button>
            </div>
        `

        return card
    }
    
    function updateStudentCount() {
        const visibleStudents = document.querySelectorAll(".student-card:not([style*='display: none'])")
        const totalStudents = document.querySelectorAll(".student-card").length
        const studentCountElement = document.querySelector(".student-count")
        
        if (studentCountElement) {
            studentCountElement.textContent = `${totalStudents} estudiantes`
        }
    }
    
    function showSuccessMessage(message) {
        // Crear elemento de mensaje
        const messageElement = document.createElement("div")
        messageElement.className = "success-message"
        messageElement.innerHTML = `
            <span class="material-icons">check_circle</span>
            <span>${message}</span>
        `
        
        // Agregar estilos
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.3s ease-out;
        `
        
        // Agregar al DOM
        document.body.appendChild(messageElement)
        
        // Remover después de 3 segundos
        setTimeout(() => {
            messageElement.style.animation = "slideOutRight 0.3s ease-in"
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement)
                }
            }, 300)
        }, 3000)
    }
    
    // Agregar estilos de animación
    const style = document.createElement("style")
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `
    document.head.appendChild(style)
})
