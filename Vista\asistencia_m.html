<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Asistencia Maestros</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/asistencia_m.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Carlos García</h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Registro de Asistencia</h1>
                    <p class="current-date">Lunes, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Tarjeta de asistencia del día -->
                <section class="attendance-today-section">
                    <div class="attendance-card">
                        <div class="attendance-status">
                            <div class="status-icon">
                                <span class="material-icons">schedule</span>
                            </div>
                            <div class="status-info">
                                <h2>Estado de Asistencia</h2>
                                <div class="status-value">Pendiente de registro</div>
                            </div>
                        </div>
                        
                        <div class="attendance-actions">
                            <div class="time-display">
                                <div class="current-time">08:15:42</div>
                                <div class="time-label">Hora actual</div>
                            </div>
                            <button id="register-entry-btn" class="attendance-btn entry-btn">
                                <span class="material-icons">login</span>
                                Registrar Entrada
                            </button>
                            <button id="register-exit-btn" class="attendance-btn exit-btn" disabled>
                                <span class="material-icons">logout</span>
                                Registrar Salida
                            </button>
                        </div>
                    </div>
                </section>
                
                <!-- Resumen de asistencia -->
                <section class="attendance-summary-section">
                    <div class="summary-header">
                        <h2>Resumen del Mes</h2>
                        <div class="month-selector">
                            <button class="month-nav-btn">
                                <span class="material-icons">chevron_left</span>
                            </button>
                            <div class="current-month">Marzo 2025</div>
                            <button class="month-nav-btn">
                                <span class="material-icons">chevron_right</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-value">22</div>
                            <div class="stat-label">Días Asistidos</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Faltas</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">1</div>
                            <div class="stat-label">Tardanzas</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Salidas Anticipadas</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">98%</div>
                            <div class="stat-label">Porcentaje</div>
                        </div>
                    </div>
                </section>
                
                <!-- Registro detallado de asistencia -->
                <section class="attendance-log-section">
                    <div class="section-header">
                        <h2>Registro Detallado</h2>
                        <div class="filter-options">
                            <select id="attendance-filter">
                                <option value="all">Todos los registros</option>
                                <option value="present">Asistencias</option>
                                <option value="late">Tardanzas</option>
                                <option value="absent">Faltas</option>
                                <option value="early-exit">Salidas anticipadas</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="attendance-table-container">
                        <table class="attendance-table">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Día</th>
                                    <th>Hora de Entrada</th>
                                    <th>Hora de Salida</th>
                                    <th>Estado</th>
                                    <th>Observaciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="attendance-row present">
                                    <td>21/03/2025</td>
                                    <td>Viernes</td>
                                    <td>07:45</td>
                                    <td>15:10</td>
                                    <td><span class="status-badge present">Asistencia</span></td>
                                    <td>-</td>
                                </tr>
                                <tr class="attendance-row present">
                                    <td>20/03/2025</td>
                                    <td>Jueves</td>
                                    <td>07:50</td>
                                    <td>15:05</td>
                                    <td><span class="status-badge present">Asistencia</span></td>
                                    <td>-</td>
                                </tr>
                                <tr class="attendance-row late">
                                    <td>19/03/2025</td>
                                    <td>Miércoles</td>
                                    <td>08:15</td>
                                    <td>15:00</td>
                                    <td><span class="status-badge late">Tardanza</span></td>
                                    <td>Tráfico intenso en la ruta principal</td>
                                </tr>
                                <tr class="attendance-row present">
                                    <td>18/03/2025</td>
                                    <td>Martes</td>
                                    <td>07:40</td>
                                    <td>15:05</td>
                                    <td><span class="status-badge present">Asistencia</span></td>
                                    <td>-</td>
                                </tr>
                                <tr class="attendance-row present">
                                    <td>17/03/2025</td>
                                    <td>Lunes</td>
                                    <td>07:45</td>
                                    <td>15:10</td>
                                    <td><span class="status-badge present">Asistencia</span></td>
                                    <td>-</td>
                                </tr>

                                <tr class="attendance-row present">
                                    <td>14/03/2025</td>
                                    <td>Viernes</td>
                                    <td>07:50</td>
                                    <td>15:00</td>
                                    <td><span class="status-badge present">Asistencia</span></td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <button class="pagination-btn" disabled>
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="pagination-btn page-number active">1</button>
                        <button class="pagination-btn page-number">2</button>
                        <button class="pagination-btn page-number">3</button>
                        <button class="pagination-btn">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </section>
                
                <!-- Solicitudes de justificación -->
                <section class="justification-section">
                    <div class="section-header">
                        <h2>Solicitudes de Justificación</h2>
                        <button class="new-request-btn">
                            <span class="material-icons">add</span>
                            Nueva Solicitud
                        </button>
                    </div>
                    
                    <div class="justification-list">
                        <div class="justification-item">
                            <div class="justification-info">
                                <div class="justification-date">19/03/2025</div>
                                <div class="justification-type">Tardanza</div>
                                <div class="justification-reason">Tráfico intenso en la ruta principal</div>
                            </div>
                            <div class="justification-status">
                                <span class="status-badge pending">Pendiente</span>
                            </div>
                        </div>
                        
                        <div class="justification-item">
                            <div class="justification-info">
                                <div class="justification-date">10/03/2025</div>
                                <div class="justification-type">Salida Anticipada</div>
                                <div class="justification-reason">Cita médica programada</div>
                            </div>
                            <div class="justification-status">
                                <span class="status-badge approved">Aprobada</span>
                            </div>
                        </div>
                        
                        <div class="justification-item">
                            <div class="justification-info">
                                <div class="justification-date">05/03/2025</div>
                                <div class="justification-type">Falta</div>
                                <div class="justification-reason">Enfermedad - Reposo médico</div>
                            </div>
                            <div class="justification-status">
                                <span class="status-badge approved">Aprobada</span>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para nueva solicitud de justificación -->
    <div id="justification-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Nueva Solicitud de Justificación</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="justification-form">
                    <div class="form-group">
                        <label for="justification-date">Fecha</label>
                        <input type="date" id="justification-date" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="justification-type">Tipo de Justificación</label>
                        <select id="justification-type" required>
                            <option value="">Seleccione un tipo</option>
                            <option value="tardanza">Tardanza</option>
                            <option value="falta">Falta</option>
                            <option value="salida-anticipada">Salida Anticipada</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="justification-reason">Motivo</label>
                        <textarea id="justification-reason" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="justification-file">Adjuntar Documento (opcional)</label>
                        <div class="file-upload">
                            <input type="file" id="justification-file">
                            <div class="file-upload-btn">
                                <span class="material-icons">attach_file</span>
                                Seleccionar archivo
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Enviar Solicitud</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/asistencia_m.js"></script>
</body>
</html>

