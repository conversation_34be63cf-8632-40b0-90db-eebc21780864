document.addEventListener("DOMContentLoaded", () => {
  // Manejar la expansión de las semanas
  const weekHeaders = document.querySelectorAll(".week-header")

  weekHeaders.forEach((header) => {
    header.addEventListener("click", () => {
      const weekNumber = header.getAttribute("data-week")
      const content = document.getElementById(`week-${weekNumber}-content`)

      // Alternar la visibilidad
      if (content.style.display === "none") {
        content.style.display = "block"
        header.querySelector(".material-icons").textContent = "expand_less"
      } else {
        content.style.display = "none"
        header.querySelector(".material-icons").textContent = "expand_more"
      }
    })
  })

  // Expandir la semana 1 por defecto y colapsar las demás
  const week1Content = document.getElementById("week-1-content")
  const week1Header = document.querySelector('[data-week="1"]')
  if (week1Content && week1Header) {
    week1Content.style.display = "block"
    const icon = week1Header.querySelector(".material-icons")
    if (icon) {
      icon.textContent = "expand_less"
    }
  }

  const week2Content = document.getElementById("week-2-content")
  const week2Header = document.querySelector('[data-week="2"]')
  if (week2Content && week2Header) {
    week2Content.style.display = "none"
    const icon = week2Header.querySelector(".material-icons")
    if (icon) {
      icon.textContent = "expand_more"
    }
  }

  const week3Content = document.getElementById("week-3-content")
  const week3Header = document.querySelector('[data-week="3"]')
  if (week3Content && week3Header) {
    week3Content.style.display = "none"
    const icon = week3Header.querySelector(".material-icons")
    if (icon) {
      icon.textContent = "expand_more"
    }
  }

  // La funcionalidad de modales se maneja en contenido_c.js
})

// Función para manejar la selección de archivos
function handleFileSelect(input) {
  const selectedFilesDiv = document.getElementById('selected-files')
  selectedFilesDiv.innerHTML = ''

  if (input.files.length > 0) {
    const file = input.files[0]
    const fileItem = document.createElement('div')
    fileItem.className = 'selected-file-item'
    fileItem.innerHTML = `
      <span class="material-icons">description</span>
      <span class="file-name">${file.name}</span>
      <span class="file-size">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
      <button type="button" class="remove-file-btn" onclick="removeFile()">
        <span class="material-icons">close</span>
      </button>
    `
    selectedFilesDiv.appendChild(fileItem)
  }
}

// Función para remover archivo seleccionado
function removeFile() {
  document.getElementById('file-input').value = ''
  document.getElementById('selected-files').innerHTML = ''
}
