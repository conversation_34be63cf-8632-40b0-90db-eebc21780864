/* Estilos específicos para la página de crear anuncio */

/* Variables adicionales */
:root {
  --form-bg: #ffffff;
  --form-border: #e2e8f0;
  --form-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  --section-bg: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

/* Formulario mejorado */
.crear-anuncio-form {
  width: 100%;
  background: var(--form-bg);
  border-radius: 20px;
  box-shadow: var(--form-shadow);
  overflow: hidden;
  position: relative;
}

.crear-anuncio-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
}

.form-section {
  margin-bottom: 0;
  padding: 32px;
  background: var(--section-bg);
  border-bottom: 2px solid var(--form-border);
  position: relative;
  transition: all 0.3s ease;
}

.form-section:last-child {
  border-bottom: none;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
}

.form-section h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  color: var(--text-color-dark);
  position: relative;
  padding-left: 20px;
}

.form-section h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
  }

  .form-group {
    margin-bottom: 24px;
    position: relative;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color-dark);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
  }

  .form-group label::after {
    content: '*';
    color: #ef4444;
    margin-left: 4px;
    font-weight: 700;
  }

  .form-group input[type="text"],
  .form-group input[type="date"],
  .form-group input[type="time"],
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid var(--form-border);
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: var(--input-focus-shadow), 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .form-group input:hover,
  .form-group select:hover,
  .form-group textarea:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 18px;
    padding-right: 50px;
    cursor: pointer;
  }

  .input-help {
    margin-top: 8px;
    font-size: 0.875rem;
    color: var(--text-color-light);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .input-help::before {
    content: 'ℹ️';
    font-size: 0.75rem;
  }
  
  /* Editor de contenido mejorado */
  .editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid var(--form-border);
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: white;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color-light);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  .toolbar-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
  }

  .toolbar-btn:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .toolbar-btn:hover::before {
    left: 100%;
  }

  .toolbar-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  }

  .toolbar-separator {
    width: 2px;
    height: 24px;
    margin: 0 8px;
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    border-radius: 1px;
    align-self: center;
  }

  .content-editor {
    min-height: 350px;
    padding: 24px;
    border: 2px solid var(--form-border);
    border-radius: 0 0 12px 12px;
    background: white;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    line-height: 1.7;
    overflow-y: auto;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .content-editor:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: var(--input-focus-shadow), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .content-editor:empty::before {
    content: 'Escriba aquí el contenido completo del anuncio...';
    color: var(--text-color-light);
    font-style: italic;
  }
  
  /* Carga de imagen mejorada */
  .file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    padding: 24px;
    border-radius: 16px;
    border: 2px solid var(--form-border);
  }

  .imagen-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 280px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 3px dashed #cbd5e1;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
  }

  .imagen-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .imagen-preview:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
  }

  .imagen-preview:hover::before {
    opacity: 1;
  }

  .imagen-preview .material-icons {
    font-size: 4rem;
    color: #94a3b8;
    margin-bottom: 16px;
    transition: all 0.3s ease;
  }

  .imagen-preview:hover .material-icons {
    color: #3b82f6;
    transform: scale(1.1);
  }

  .imagen-preview p {
    color: var(--text-color-light);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    margin: 0;
    transition: color 0.3s ease;
  }

  .imagen-preview:hover p {
    color: #3b82f6;
  }

  .imagen-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    transition: transform 0.3s ease;
  }

  .imagen-preview:hover img {
    transform: scale(1.05);
  }

  .file-upload-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }

  .file-upload-actions .btn-secondary,
  .file-upload-actions .btn-outline {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .file-upload-actions .btn-secondary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .file-upload-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  .file-upload-actions .btn-outline {
    background: white;
    color: #ef4444;
    border: 2px solid #ef4444;
  }

  .file-upload-actions .btn-outline:hover {
    background: #ef4444;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
  
  /* Toggle Switch */
  .toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 1.5rem;
    transition: 0.4s;
  }
  
  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 1.125rem;
    width: 1.125rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
  }
  
  input:checked + .toggle-slider {
    background-color: var(--primary-color);
  }
  
  input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
  }
  
  input:checked + .toggle-slider:before {
    transform: translateX(1.5rem);
  }
  
  .toggle-label {
    font-size: 0.9375rem;
    color: var(--text-color);
  }
  
  /* Form Actions mejoradas */
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 16px;
    border-top: 2px solid var(--form-border);
  }

  .form-actions button {
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 160px;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }

  .form-actions button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .form-actions button:hover::before {
    left: 100%;
  }

  .form-actions .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  }

  .form-actions .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
  }

  .form-actions .btn-secondary {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: var(--text-color-dark);
    border: 2px solid #cbd5e1;
  }

  .form-actions .btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }

  .form-actions button:active {
    transform: translateY(0);
  }
  
  /* Modales mejorados */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
  }

  .modal-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(30px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .modal-overlay.active .modal-content {
    transform: translateY(0) scale(1);
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 2px solid var(--form-border);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
  }

  .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  }

  .modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color-dark);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .modal-close-btn {
    background: white;
    border: 2px solid #e2e8f0;
    color: var(--text-color-light);
    cursor: pointer;
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .modal-close-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }

  .modal-body {
    padding: 32px;
    overflow-y: auto;
    flex: 1;
    background: white;
  }

  .modal-footer {
    padding: 24px 32px;
    border-top: 2px solid var(--form-border);
    display: flex;
    justify-content: center;
    gap: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }
  
  /* Vista Previa Modal - Correcciones */
  .vista-previa-modal {
    max-width: 800px;
  }
  
  .vista-previa-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .vista-previa-imagen {
    width: 100%;
    height: 300px;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
  }
  
  .vista-previa-imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .vista-previa-contenido {
    padding: 0.5rem;
  }
  
  .vista-previa-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .preview-categoria {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .preview-fecha {
    font-size: 0.875rem;
    color: var(--text-color-light);
  }
  
  .preview-titulo {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color-dark);
  }
  
  .preview-descripcion {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  
  .preview-contenido-completo {
    font-size: 0.9375rem;
    color: var(--text-color);
    line-height: 1.6;
    padding: 0.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
  }
  
  /* Confirmación Modal - Correcciones */
  .confirmacion-modal {
    max-width: 500px;
  }
  
  .confirmacion-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1.5rem;
    background-color: rgba(255, 193, 7, 0.15);
    border-radius: 50%;
  }
  
  .confirmacion-icon .material-icons {
    font-size: 2.5rem;
    color: #f29d0f;
  }
  
  #confirmacion-mensaje {
    text-align: center;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 1rem;
  }
  
  /* Botones en modales - Correcciones */
  .modal-footer .btn-primary,
  .modal-footer .btn-secondary {
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
  }
  
  .modal-footer .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .modal-footer .btn-primary:hover {
    background-color: #1e3a8a;
  }
  
  .modal-footer .btn-secondary {
    background-color: #f1f5f9;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .modal-footer .btn-secondary:hover {
    background-color: #e2e8f0;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .form-actions {
      flex-direction: column-reverse;
    }
    
    .form-actions button {
      width: 100%;
    }
    
    .file-upload-actions {
      flex-direction: column;
    }
    
    .vista-previa-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .modal-content {
      width: 95%;
      max-height: 95vh;
    }
    
    .modal-header, 
    .modal-body, 
    .modal-footer {
      padding: 1rem;
    }
    
    .modal-footer {
      flex-direction: column;
    }
    
    .modal-footer button {
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    .toolbar-btn {
      width: 2rem;
      height: 2rem;
    }
    
    .toolbar-btn .material-icons {
      font-size: 1.125rem;
    }
    
    .vista-previa-imagen {
      height: 200px;
    }
    
    .preview-titulo {
      font-size: 1.25rem;
    }
    
    .preview-descripcion {
      font-size: 0.9375rem;
    }
  }