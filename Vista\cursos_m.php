<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

require_once '../Controlador/CursoController.php';

$controller = new CursoController();
$maestroId = $_SESSION['usuario_id'];
$cursos = $controller->obtenerCursosPorMaestro($maestroId);
$grados = $controller->obtenerGrados();
$iconos = $controller->obtenerIconos();

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

// Consulta corregida para obtener información del maestro usando la relación correcta
$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Gestión de Cursos</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/cursos_m.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php if ($maestro && $maestro['foto_perfil']): ?>
                            <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                        <?php else: ?>
                            <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.php">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.php">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Gestión de Cursos</h1>
                    <p class="current-date"><?php echo date('l, j \d\e F \d\e Y'); ?></p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Filtro y acciones de cursos -->
                <section class="courses-actions">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="search-input" placeholder="Buscar cursos por nombre o grado...">
                        </div>
                        <div class="filter-options">
                            <select id="period-filter">
                                <option value="all">Todos los periodos</option>
                                <option value="current" selected>Periodo actual</option>
                                <option value="past">Periodos anteriores</option>
                            </select>
                            <select id="grade-filter">
                                <option value="all">Todos los grados</option>
                                <?php foreach ($grados as $valor => $etiqueta): ?>
                                    <option value="<?php echo htmlspecialchars($valor); ?>"><?php echo htmlspecialchars($etiqueta); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <button id="create-course-btn" class="create-course-btn">
                        <span class="material-icons">add</span>
                        Crear Nuevo Curso
                    </button>
                </section>
                
                <!-- Lista de cursos -->
                <section class="courses-section">
                    <div class="section-header">
                        <h2>Mis Cursos</h2>
                        <div class="view-options">
                            <button class="view-btn active" data-view="grid">
                                <span class="material-icons">grid_view</span>
                            </button>
                            <button class="view-btn" data-view="list">
                                <span class="material-icons">view_list</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="courses-grid" id="courses-grid">
                        <?php if (empty($cursos)): ?>
                            <div class="no-courses">
                                <div class="no-courses-icon">
                                    <span class="material-icons">school</span>
                                </div>
                                <h3>No tienes cursos creados</h3>
                                <p>Crea tu primer curso para comenzar a gestionar tu contenido educativo y horarios.</p>
                                <button id="create-first-course-btn" class="create-course-btn">
                                    <span class="material-icons">add</span>
                                    Crear mi primer curso
                                </button>
                            </div>
                        <?php else: ?>
                            <?php foreach ($cursos as $curso): ?>
                                <div class="course-card" data-id="<?php echo $curso['id']; ?>" data-grade="<?php echo htmlspecialchars($curso['grado']); ?>">
                                    <div class="course-card-header">
                                        <?php if ($curso['imagen']): ?>
                                            <img src="<?php echo htmlspecialchars($curso['imagen']); ?>" alt="<?php echo htmlspecialchars($curso['nombre']); ?>">
                                        <?php else: ?>
                                            <div class="course-placeholder">
                                                <span class="material-icons">school</span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="course-icon <?php echo htmlspecialchars($curso['icono'] ?? 'school'); ?>-icon">
                                            <span class="material-icons"><?php echo htmlspecialchars($curso['icono'] ?? 'school'); ?></span>
                                        </div>
                                        <div class="course-actions">
                                            <button class="course-action-btn edit-btn" data-id="<?php echo $curso['id']; ?>" title="Editar curso">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="course-action-btn delete-btn" data-id="<?php echo $curso['id']; ?>" title="Eliminar curso">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                        <div class="course-status">
                                            <span class="status-badge active">Activo</span>
                                        </div>
                                    </div>
                                    <div class="course-card-body">
                                        <h3><?php echo htmlspecialchars($curso['nombre']); ?></h3>
                                        <p class="course-grade"><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                                        <?php if (!empty($curso['horarios_procesados'])): ?>
                                            <p class="course-schedule" style="margin:0 0 10px 0; color:#2a4db7; font-weight:500;">
                                                <?php 
                                                $horariosTexto = [];
                                                foreach ($curso['horarios_procesados'] as $dia => $horario) {
                                                    $dias = [
                                                        'lunes' => 'Lun',
                                                        'martes' => 'Mar',
                                                        'miercoles' => 'Mié',
                                                        'jueves' => 'Jue',
                                                        'viernes' => 'Vie'
                                                    ];
                                                    $partes = explode(' - ', $horario);
                                                    if (count($partes) === 2 && $partes[0] && $partes[1]) {
                                                        $hora_inicio = substr($partes[0], 0, 5);
                                                        $hora_fin = substr($partes[1], 0, 5);
                                                        $horariosTexto[] = $dias[$dia] . ' ' . $hora_inicio . ' - ' . $hora_fin;
                                                    } else {
                                                        $horariosTexto[] = $dias[$dia];
                                                    }
                                                }
                                                echo implode(', ', $horariosTexto);
                                                ?>
                                            </p>
                                        <?php endif; ?>
                                        <div class="course-details">
                                            <div class="course-detail">
                                                <span class="material-icons">groups</span>
                                                <span><?php echo $curso['total_estudiantes'] ?? 0; ?> estudiantes</span>
                                            </div>
                                            <div class="course-detail">
                                                <span class="material-icons">calendar_today</span>
                                                <span>Año <?php echo $curso['anio_escolar'] ?? date('Y'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="course-card-footer">
                                        <a href="contenido_m.php?curso_id=<?php echo $curso['id']; ?>" class="view-course-btn">
                                            <span class="material-icons">visibility</span>
                                            Ver curso
                                        </a>
                                        <a href="estudiantes_m.php?curso_id=<?php echo $curso['id']; ?>" class="students-btn" title="Gestionar estudiantes">
                                            <span class="material-icons">groups</span>
                                            Estudiantes
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para crear/editar curso -->
    <div id="course-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Crear Nuevo Curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="course-form">
                    <input type="hidden" id="course-id" value="">
                    
                    <div class="form-group">
                        <label for="course-name">Nombre del Curso *</label>
                        <input type="text" id="course-name" required placeholder="Ej: Matemáticas Básicas">
                        <small class="form-help">Ingresa un nombre descriptivo para tu curso</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-grade">Grado *</label>
                            <select id="course-grade" required>
                                <option value="">Seleccionar grado</option>
                                <?php foreach ($grados as $valor => $etiqueta): ?>
                                    <option value="<?php echo htmlspecialchars($valor); ?>"><?php echo htmlspecialchars($etiqueta); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="course-icon">Ícono del Curso *</label>
                            <select id="course-icon" required>
                                <?php foreach ($iconos as $valor => $etiqueta): ?>
                                    <option value="<?php echo htmlspecialchars($valor); ?>"><?php echo htmlspecialchars($etiqueta); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Horario de Clases *</label>
                        <small class="form-help">Selecciona los días y horarios en que se imparte el curso</small>
                        <div class="schedule-inputs">
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="lunes"> Lunes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-lunes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-lunes" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="martes"> Martes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-martes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-martes" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="miercoles"> Miércoles
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-miercoles" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-miercoles" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="jueves"> Jueves
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-jueves" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-jueves" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="viernes"> Viernes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-viernes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-viernes" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="course-image">Imagen del Curso (opcional)</label>
                        <small class="form-help">Agrega una imagen representativa para tu curso (máximo 5MB)</small>
                        <div class="file-upload">
                            <input type="file" id="course-image" accept="image/*">
                            <div class="file-upload-btn">
                                <span class="material-icons">image</span>
                                Seleccionar imagen
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">
                            <span class="material-icons">save</span>
                            Guardar Curso
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal de confirmación para eliminar curso -->
    <div id="delete-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>Eliminar Curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <span class="material-icons">warning</span>
                    <p>¿Está seguro que desea eliminar este curso?</p>
                    <p class="warning-text">Esta acción no se puede deshacer y eliminará todos los datos asociados al curso.</p>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-btn">
                        <span class="material-icons">delete</span>
                        Eliminar Curso
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de carga -->
    <div id="loading-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-body">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Procesando...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Datos para JavaScript -->
    <script>
        window.cursosData = <?php echo json_encode($cursos); ?>;
        window.grados = <?php echo json_encode($grados); ?>;
        window.iconos = <?php echo json_encode($iconos); ?>;
        window.currentYear = <?php echo date('Y'); ?>;
    </script>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/cursos_m.js"></script>
</body>
</html>

