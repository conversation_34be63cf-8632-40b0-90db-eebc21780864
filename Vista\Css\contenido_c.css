/* Estilos específicos para la sección de contenido del curso */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Contenido del curso */
  .course-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .week-container {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .week-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .week-header:hover {
    background-color: #e8eef7;
  }
  
  .week-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
  }
  
  .week-content,
  .folder-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
    padding: 0 20px;
  }

  .week-content.expanded,
  .folder-content[style*="display: block"] {
    max-height: 1000px;
    padding: 20px;
  }
  
  .content-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: white;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .content-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
  
  .content-item:last-child {
    margin-bottom: 0;
  }
  
  .content-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .content-icon.pdf {
    background-color: #ffebee;
  }
  
  .content-icon.pdf .material-icons {
    color: #f44336;
  }
  
  .content-icon.ppt {
    background-color: #fff8e1;
  }
  
  .content-icon.ppt .material-icons {
    color: #ff9800;
  }
  
  .content-icon.task {
    background-color: #e8f5e9;
  }
  
  .content-icon.task .material-icons {
    color: #4caf50;
  }
  
  .content-icon.link {
    background-color: #e3f2fd;
  }
  
  .content-icon.link .material-icons {
    color: #2196f3;
  }
  
  .content-icon.announcement {
    background-color: #ede7f6;
  }
  
  .content-icon.announcement .material-icons {
    color: #673ab7;
  }

  .content-icon.video {
    background-color: #ffebee;
  }

  .content-icon.video .material-icons {
    color: #e91e63;
  }

  .content-icon.document {
    background-color: #ffebee;
  }

  .content-icon.document .material-icons {
    color: #f44336;
  }

  .content-icon.exam {
    background-color: #fff3e0;
  }

  .content-icon.exam .material-icons {
    color: #ff9800;
  }

  .content-icon.participation {
    background-color: #e0f2f1;
  }

  .content-icon.participation .material-icons {
    color: #009688;
  }

  .content-icon.presentation {
    background-color: #fff3e0;
  }

  .content-icon.presentation .material-icons {
    color: #ff9800;
  }
  
  .content-details {
    flex: 1;
    cursor: pointer;
  }
  
  .content-details h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 5px;
  }
  
  .content-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
  }
  
  .content-date {
    font-size: 0.8rem;
    color: var(--text-light);
  }
  
  .content-actions {
    display: flex;
    gap: 10px;
  }
  
  .content-action-btn {
    background: none;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .content-action-btn:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .task-grade {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
    margin-right: 5px;
  }
  
  /* Mensaje de contenido vacío */
  .empty-content {
    padding: 30px;
    text-align: center;
    color: var(--text-light);
  }
  
  .empty-content .material-icons {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
  }
  
  .empty-content p {
    font-size: 1rem;
  }
  
  /* Sección de videoconferencia */
  .meet-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
  }
  
  .no-meet-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
  }
  
  .no-meet-message .material-icons {
    font-size: 3rem;
    opacity: 0.5;
  }
  
  .no-meet-message p {
    font-size: 1rem;
  }
  
  /* Sección de videoconferencia activa */
  .meet-section.active-meet {
    background-color: #e8f5e9;
    min-height: auto;
    padding: 0;
  }
  
  .meet-info {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    width: 100%;
  }
  
  .meet-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4caf50;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .meet-icon .material-icons {
    color: white;
    font-size: 1.8rem;
  }
  
  .meet-details {
    flex: 1;
  }
  
  .meet-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .meet-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 10px;
  }
  
  .meet-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 8px 16px;
    background-color: var(--primary-light);
    border-radius: 20px;
    transition: var(--transition);
  }
  
  .meet-link:hover {
    background-color: #d4e6f9;
  }
  
  /* Modal para anuncios */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
  }
  
  .modal-overlay.active {
    opacity: 1;
    visibility: visible;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    transform: translateY(20px);
    transition: transform 0.3s;
  }
  
  .modal-overlay.active .modal-content {
    transform: translateY(0);
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f9f9f9;
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .announcement-meta {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 15px;
  }
  
  .announcement-text {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
  }
  
  .announcement-text p {
    margin-bottom: 15px;
  }
  
  .announcement-text ul {
    margin-bottom: 15px;
    padding-left: 20px;
  }
  
  .announcement-text li {
    margin-bottom: 5px;
  }
  
  /* Estilos para modales de contenido */
  .modal-large {
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
  }

  /* Header simplificado para modales */
  .content-header-simple {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }

  .content-header-simple .content-icon-large {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .content-header-simple .content-icon-large .material-icons {
    font-size: 24px;
  }

  .content-header-simple .content-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #333;
  }

  .content-header-simple .content-meta {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
  }

  .document-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 4px;
  }

  .document-meta span {
    font-size: 0.85rem;
    color: #666;
  }

  .document-type {
    background-color: #e3f2fd;
    color: #2196f3 !important;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.8rem;
  }

  .document-size {
    background-color: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
  }

  /* Colores específicos para íconos */
  .video-preview .content-icon-large {
    background-color: #ffebee;
    color: #e91e63;
  }

  .link-preview .content-icon-large {
    background-color: #e3f2fd;
    color: #2196f3;
  }

  .presentation-preview .content-icon-large {
    background-color: #fff3e0;
    color: #ff9800;
  }

  .participation-preview .content-icon-large {
    background-color: #e0f2f1;
    color: #009688;
  }

  .document-preview .content-icon-large {
    background-color: #ffebee;
    color: #f44336;
  }



  /* Secciones de contenido */
  .content-description {
    margin-bottom: 20px;
  }

  .content-description p {
    margin-bottom: 16px;
    color: #333;
    line-height: 1.6;
  }

  .content-description h4 {
    margin: 20px 0 10px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
  }

  .content-description ul {
    margin: 0 0 16px 0;
    padding-left: 20px;
  }

  .content-description li {
    margin-bottom: 8px;
    color: #333;
    line-height: 1.5;
  }

  .activity-section,
  .criteria-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
  }

  .activity-section h4,
  .criteria-section h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
  }

  .activity-section p {
    margin: 8px 0;
    color: #333;
    line-height: 1.5;
  }

  .criteria-section ul {
    margin: 0;
    padding-left: 20px;
  }

  .criteria-section li {
    margin-bottom: 8px;
    color: #333;
    line-height: 1.5;
  }

  /* Elementos de vista previa */
  .document-viewer,
  .presentation-viewer {
    margin: 20px 0;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
  }

  .video-container {
    margin: 20px 0;
  }

  .document-placeholder {
    text-align: center;
    color: #666;
  }

  .document-placeholder .material-icons {
    font-size: 4rem;
    margin-bottom: 15px;
    opacity: 0.5;
  }

  .document-placeholder p {
    margin: 5px 0;
  }

  .document-filename {
    margin: 8px 0 0 0 !important;
    color: #007bff !important;
    font-size: 13px !important;
    font-weight: 500;
  }

  .document-actions,
  .presentation-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    margin-top: 20px;
  }

  /* Estilos para elementos específicos */
  .participation-grade {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
  }

  .grade-label {
    font-weight: 600;
    color: #333;
  }

  .grade-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #666;
  }

  /* Enlaces y URLs */
  .content-url {
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .content-url a {
    color: #2196f3;
    text-decoration: none;
    word-break: break-all;
  }

  .content-url a:hover {
    text-decoration: underline;
  }

  /* Video thumbnail */
  .video-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    min-height: 200px;
  }

  .video-thumbnail .play-icon {
    font-size: 48px;
    color: #2196f3;
    margin-bottom: 10px;
  }

  .video-thumbnail p {
    margin: 5px 0;
    color: #666;
  }

  /* Botones de acción */
  .link-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
  }

  .btn-primary {
    background-color: #2196f3;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #1976d2;
  }

  .btn-secondary {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-secondary:hover {
    background-color: #f5f5f5;
  }

  /* Estilos para tarjetas de vista previa de archivos */
  .document-preview-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-preview-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    gap: 16px;
  }

  .file-icon-container {
    flex-shrink: 0;
  }

  .file-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  .pdf-icon {
    background-color: #ffebee;
    color: #f44336;
  }

  .ppt-icon {
    background-color: #fff3e0;
    color: #ff9800;
  }



  .file-info {
    flex: 1;
  }

  .file-name {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
  }

  .file-details {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .file-details span {
    font-size: 0.85rem;
    color: #666;
    background: white;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }

  .file-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  .btn-download,
  .btn-view {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-download {
    background-color: #2196f3;
    color: white;
  }

  .btn-download:hover {
    background-color: #1976d2;
  }

  .btn-view {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;
  }

  .btn-view:hover {
    background-color: #f5f5f5;
  }

  .file-preview-content {
    padding: 20px;
    text-align: center;
    background: #fafafa;
  }

  .preview-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    color: #666;
  }

  .preview-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.6;
  }

  .preview-thumbnail p {
    margin: 0;
    font-size: 0.9rem;
  }



  /* Responsive */
  @media (max-width: 768px) {
    .meet-info {
      flex-direction: column;
      text-align: center;
    }

    .meet-icon {
      margin: 0 auto;
    }
  }