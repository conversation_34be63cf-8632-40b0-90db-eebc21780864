/* Estilos específicos para la gestión de usuarios */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }


  
  /* Barra de her<PERSON>ientas */
  .users-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    width: 300px;
    padding: 10px 15px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
  }
  
  .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
  }
  
  .filter-container {
    display: flex;
    gap: 10px;
  }
  
  .filter-select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
  }
  
  .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .toolbar-right {
    display: flex;
    gap: 10px;
  }
  
  .account-example-btn,
  .create-user-btn,
  .export-excel-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 18px;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
  }
  
  .create-user-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .create-user-btn:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .account-example-btn,
  .export-excel-btn {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .account-example-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .export-excel-btn {
    background-color: #10b981;
    color: white;
    border: 1px solid #10b981;
  }

  .export-excel-btn:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
  
  /* Tabla de usuarios */
  .users-table-container {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
  }

  .users-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }

  .users-table th,
  .users-table td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
  }

  .users-table th.role-col,
  .users-table td.role-col,
  .users-table th.date-col,
  .users-table td.date-col,
  .users-table th.actions-col,
  .users-table td.actions-col {
    text-align: center;
  }

  .users-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 600;
    color: #475569;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 2px solid #e2e8f0;
  }

  .users-table tr:last-child td {
    border-bottom: none;
  }

  .users-table tbody tr {
    transition: var(--transition);
  }

  .users-table tbody tr:hover {
    background-color: #f8fafc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .users-table tbody tr:nth-child(even) {
    background-color: #fafbfc;
  }

  .users-table tbody tr:nth-child(even):hover {
    background-color: #f1f5f9;
  }
  

  
  .user-col {
    width: 30%;
  }

  .role-col {
    width: 15%;
    text-align: center;
  }

  .email-col {
    width: 30%;
  }

  .date-col {
    width: 15%;
    text-align: center;
  }

  .actions-col {
    width: 10%;
    text-align: center;
  }
  
  /* Estilos específicos para la tabla de usuarios */
  .users-table .user-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .users-table .user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid #e2e8f0;
    transition: var(--transition);
  }

  .users-table .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .users-table tr:hover .user-avatar {
    border-color: var(--primary-color);
    transform: scale(1.05);
  }

  .users-table .user-details {
    display: flex;
    flex-direction: column;
  }

  .user-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
    font-size: 0.95rem;
  }

  .user-id {
    font-size: 0.8rem;
    color: #64748b;
  }

  /* Estilos para las celdas de rol y fecha */
  .role-col {
    font-weight: 500;
    color: #475569;
    font-size: 0.9rem;
  }

  .date-col {
    font-weight: 400;
    color: #64748b;
    font-size: 0.85rem;
  }

  .email-col {
    font-weight: 400;
    color: #475569;
    font-size: 0.9rem;
  }
  
  .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }
  
  .status-badge.active {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .status-badge.inactive {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .status-badge.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .user-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid #e2e8f0;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .action-btn.view-btn:hover {
    color: white;
    background-color: var(--info-color);
    border-color: var(--info-color);
  }

  .action-btn.edit-btn:hover {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .action-btn.delete-btn:hover {
    color: white;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
  }

  .action-btn .material-icons {
    font-size: 18px;
  }
  
  /* Paginación */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
  }
  
  .pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .pagination-btn:hover:not(:disabled) {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .pagination-pages {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .pagination-page {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .pagination-page:hover {
    background-color: var(--secondary-color);
  }
  
  .pagination-page.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .pagination-ellipsis {
    color: var(--text-light);
  }
  
  /* Modal para crear/editar usuario */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  .user-modal {
    max-width: 800px;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  /* Formulario de usuario */
  .form-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
  }
  
  .form-tab {
    background: none;
    border: none;
    padding: 10px 15px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-light);
    cursor: default;
    transition: var(--transition);
    position: relative;
    pointer-events: none;
  }
  
  .form-tab.active {
    color: var(--primary-color);
  }
  
  .form-tab.active::after {
    content: "";
    position: absolute;
    bottom: -11px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
  }
  
  .form-tab-content {
    display: none;
    margin-bottom: 20px;
  }
  
  .form-tab-content.active {
    display: block;
  }

  /* Estilos para los botones de navegación por pasos */
  .step-buttons {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }

  .step-buttons .btn-secondary,
  .step-buttons .btn-primary {
    min-width: 120px;
    padding: 12px 20px;
    font-weight: 500;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
  }
  
  .form-group input[type="text"],
  .form-group input[type="email"],
  .form-group input[type="tel"],
  .form-group input[type="password"],
  .form-group input[type="date"],
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .checkbox-group {
    margin-top: 5px;
  }
  
  .checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin-bottom: 5px;
    cursor: pointer;
  }
  
  .checkbox-group input[type="checkbox"] {
    margin: 0;
  }
  
  .permissions-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
  }
  
  .permission-item label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin-bottom: 0;
    cursor: pointer;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
  
  .btn-secondary,
  .btn-primary {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-color);
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .users-toolbar {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .toolbar-left,
    .toolbar-right {
      width: 100%;
    }
  
    .toolbar-right {
      justify-content: flex-end;
    }
  
    .search-input {
      width: 100%;
    }
  
    .filter-container {
      width: 100%;
    }
  
    .filter-select {
      flex: 1;
    }
  
    .form-row {
      flex-direction: column;
      gap: 20px;
    }
  }
  
  @media (max-width: 768px) {
    .users-table th,
    .users-table td {
      padding: 10px;
    }
  
    .role-col,
    .date-col {
      display: none;
    }
  
    .toolbar-right {
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  
    .create-user-btn,
    .import-users-btn,
    .export-users-btn {
      flex: 1;
    }
  
    .form-tabs {
      flex-direction: column;
      gap: 5px;
    }
  
    .form-tab.active::after {
      display: none;
    }
  
    .form-tab.active {
      background-color: var(--primary-light);
      border-radius: 5px;
    }
  }
  
  @media (max-width: 576px) {
    .email-col {
      display: none;
    }
  
    .pagination-pages {
      display: none;
    }
  }
  
  /* Estilos para el modal de detalles de usuario */
  .user-details-modal {
    max-width: 1200px;
    width: 95%;
  }
  
  .user-profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .user-avatar.large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
  }
  
  .user-avatar.large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .user-profile-info h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
  }
  
  .user-profile-info p {
    margin: 0;
    color: #666;
  }
  
  .role-specific-details {
    display: none;
    margin-bottom: 20px;
  }
  
  .role-specific-details h3 {
    margin: 20px 0 10px 0;
    font-size: 1.2rem;
    color: #333;
  }
  
  /* Estilos para tablas en los detalles */
  .grades-table,
  .courses-table,
  .payment-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }
  
  .grades-table th,
  .courses-table th,
  .payment-table th {
    background-color: #f5f5f5;
    padding: 10px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }
  
  .grades-table td,
  .courses-table td,
  .payment-table td {
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }
  
  /* Estilos para estadísticas de asistencia */
  .attendance-stats {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
  }
  
  .attendance-stat {
    flex: 1;
    text-align: center;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin: 0 5px;
  }
  
  .attendance-stat .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
  }
  
  .attendance-stat .stat-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
  }
  
  /* Estilos para lista de hijos */
  .children-list {
    margin-top: 15px;
  }
  
  .child-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 10px;
  }
  
  .child-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
  }
  
  .child-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .child-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
  }
  
  .child-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
  }

  /* Estilos para botón de modificar asistencias */
  .attendance-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .modify-attendance-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }

  .modify-attendance-btn:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(42, 77, 183, 0.3);
  }

  /* Estilos para timeline de actividad */
  .activity-timeline {
    margin-top: 15px;
  }
  
  .timeline-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
  }
  
  .timeline-date {
    width: 150px;
    color: #666;
    font-size: 0.9rem;
  }
  
  .timeline-content {
    display: flex;
    align-items: center;
  }
  
  .timeline-content .material-icons {
    margin-right: 10px;
    color: #2196f3;
  }
  
  /* Estilos para permisos de administrador */
  .admin-permissions ul {
    list-style: none;
    padding: 0;
    margin: 15px 0;
  }
  
  .admin-permissions li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .admin-permissions .material-icons {
    margin-right: 10px;
    color: #4caf50;
  }
  
  /* Estilos para ejemplo de creación de cuenta */
  .creation-example {
    margin-bottom: 20px;
  }
  
  .example-step {
    display: flex;
    margin-bottom: 25px;
  }
  
  .step-number {
    width: 30px;
    height: 30px;
    background-color: #2196f3;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
  }
  
  .step-content {
    flex: 1;
  }
  
  .step-content h4 {
    margin: 0 0 15px 0;
    color: #333;
  }
  
  .example-form .form-row,
  .example-form .form-group {
    margin-bottom: 10px;
  }
  
  .example-form input,
  .example-form select {
    background-color: #f9f9f9;
    cursor: not-allowed;
  }
  
  .confirmation-message {
    text-align: center;
    padding: 20px;
    background-color: #f1f8e9;
    border-radius: 8px;
  }
  
  .success-icon {
    font-size: 48px;
    color: #4caf50;
    margin-bottom: 10px;
  }
  
  .user-created-info {
    text-align: left;
    max-width: 300px;
    margin: 15px auto;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  

  
  /* Estilos para badges de estado */
  .status-badge.complete {
    background-color: #4caf50;
    color: white;
  }
  
  .status-badge.pending {
    background-color: #ff9800;
    color: white;
  }

  .status-badge.overdue {
    background-color: #f44336;
    color: white;
  }

  /* Estilos para la sección administrativa de pagos */
  .admin-payment-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }

  .admin-payment-section h4 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
  }

  .admin-note {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
  }

  .admin-note .material-icons {
    font-size: 18px;
    color: var(--info-color);
  }

  /* Estilos para el modal de cambio de estado de pago */
  .payment-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--secondary-color);
    border-radius: 8px;
  }

  .payment-info h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 1.1rem;
  }

  .payment-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .payment-details p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .payment-status-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
  }

  .payment-status-options .status-option {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
  }

  .payment-status-options .status-option.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }

  .payment-status-options .status-option.complete {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }

  .payment-status-options .status-option.overdue {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }

  .payment-status-options .status-option.active {
    border-color: currentColor;
    font-weight: 600;
  }

  .payment-status-options .status-option:hover {
    opacity: 0.8;
  }

  /* Botón de cambio de estado de pago */
  .change-payment-btn {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .change-payment-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .change-payment-btn .material-icons {
    font-size: 16px;
  }

  /* Textarea para notas administrativas */
  #payment-notes {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    transition: var(--transition);
  }

  #payment-notes:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
    outline: none;
  }


  