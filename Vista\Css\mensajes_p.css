/* Estilos específicos para la sección de mensajes (vista de padres) */

/* Variables */
:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Contenedor de chat */
.chat-container {
  display: flex;
  height: calc(100vh - 250px);
  min-height: 500px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-top: 20px;
}

/* Lista de contactos */
.contacts-list {
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  width: 350px;
  min-width: 350px;
  background-color: white;
}

.contacts-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.contacts-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--secondary-color);
  border-radius: 20px;
  padding: 8px 15px;
}

.search-box .material-icons {
  color: var(--text-light);
  font-size: 1.2rem;
  margin-right: 8px;
}

.search-box input {
  background: none;
  border: none;
  outline: none;
  width: 100%;
  font-size: 0.9rem;
  color: var(--text-color);
}

.contacts-body {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 100px);
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
  background-color: white;
}

.contact-item:hover {
  background-color: var(--secondary-color);
}

.contact-item.active {
  background-color: var(--primary-light);
}

.contact-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.contact-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contact-info {
  flex: 1;
  min-width: 0;
}

.contact-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-preview {
  font-size: 0.85rem;
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 10px;
  flex-shrink: 0;
}

.contact-time {
  font-size: 0.75rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.contact-badge {
  background-color: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Área de chat */
.chat-area {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
}

.chat-contact {
  display: flex;
  align-items: center;
}

.chat-contact .contact-avatar {
  margin-right: 15px;
}

.chat-contact .contact-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 5px;
}

.chat-contact .contact-status {
  font-size: 0.85rem;
  color: var(--success-color);
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.chat-action-btn {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.chat-action-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fb;
}

.chat-date {
  text-align: center;
  font-size: 0.85rem;
  color: var(--text-light);
  margin: 10px 0;
  position: relative;
}

.chat-date::before,
.chat-date::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 70px;
  height: 1px;
  background-color: var(--border-color);
}

.chat-date::before {
  left: calc(50% - 100px);
}

.chat-date::after {
  right: calc(50% - 100px);
}

.message {
  display: flex;
  margin-bottom: 15px;
}

.message.received {
  justify-content: flex-start;
}

.message.sent {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 12px 15px;
  border-radius: 10px;
  font-size: 0.95rem;
  line-height: 1.5;
  position: relative;
}

.message.received .message-bubble {
  background-color: white;
  color: var(--text-color);
  border-top-left-radius: 0;
  box-shadow: var(--shadow-sm);
}

.message.sent .message-bubble {
  background-color: var(--primary-color);
  color: white;
  border-top-right-radius: 0;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-light);
  margin-top: 5px;
  text-align: right;
}

.message.sent .message-time {
  color: var(--text-light);
}

/* Mensaje con archivo */
.file-message {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-message .material-icons {
  font-size: 2rem;
  color: var(--primary-color);
}

.file-info {
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: 3px;
}

.file-size {
  font-size: 0.8rem;
  color: var(--text-light);
}

.file-download-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
}

.message.received .file-download-btn {
  color: var(--primary-color);
}

.message.sent .file-download-btn {
  color: white;
}

/* Área de entrada de chat */
.chat-input-area {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  background-color: white;
  width: 100%;
}

.chat-tool-btn {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.chat-tool-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.chat-input {
  flex: 1;
  margin: 0 15px;
}

.chat-input input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 0.95rem;
  color: var(--text-color);
  outline: none;
  transition: var(--transition);
}

.chat-input input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.chat-send-btn {
  background-color: var(--primary-color);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: var(--transition);
}

.chat-send-btn:hover {
  background-color: #1e40af;
}

/* Responsive */
@media (max-width: 992px) {
  .chat-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .contacts-list {
    display: none;
  }

  .contacts-list.active {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
  }
}