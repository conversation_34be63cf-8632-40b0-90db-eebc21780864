/* Estilos específicos para estudiantes_m.html - Vista de profesor */

/* Header del curso con color azul para profesores */
.course-header {
    background-color: #2196f3 !important;
}

/* Sección de filtros y acciones */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0;
    padding: 40px 32px;
    margin-bottom: 24px;
    border-bottom: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    position: relative;
}

.filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2196f3 0%, #21cbf3 100%);
}

.filter-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 800px;
    min-width: 400px;
}

.search-box .material-icons {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #2196f3;
    font-size: 22px;
    transition: all 0.3s ease;
}

.search-box:hover .material-icons {
    color: #1976d2;
    transform: translateY(-50%) scale(1.1);
}

.search-box input {
    width: 100%;
    padding: 18px 24px 18px 56px;
    border: 2px solid transparent;
    border-radius: 25px;
    font-size: 15px;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #e3f2fd, #f3e5f5) border-box;
    transition: all 0.4s ease;
    font-weight: 400;
    color: #333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-box input:focus {
    outline: none;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #2196f3, #9c27b0) border-box;
    box-shadow: 0 8px 30px rgba(33, 150, 243, 0.15);
    transform: translateY(-2px);
}

.search-box input::placeholder {
    color: #9e9e9e;
    font-weight: 400;
}

.filter-actions {
    display: flex;
    gap: 12px;
    margin-left: auto;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 16px 28px;
    border: none;
    border-radius: 15px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.primary-btn {
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    color: white;
}

.primary-btn:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1e88e5 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 30px rgba(33, 150, 243, 0.4);
}

.secondary-btn {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    color: white;
}

.secondary-btn:hover {
    background: linear-gradient(135deg, #388e3c 0%, #43a047 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 30px rgba(76, 175, 80, 0.4);
}

.action-btn .material-icons {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.action-btn:hover .material-icons {
    transform: scale(1.1);
}

/* Sección principal de estudiantes */
.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f5f5f5;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.student-count {
    font-size: 14px;
    color: #666;
    background-color: #f5f5f5;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Grid de estudiantes - diseño simplificado */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.student-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #e3f2fd;
}

.student-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto 16px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #e3f2fd;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.student-avatar::before {
    content: "📷 Foto del estudiante";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    z-index: 1;
}

.student-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.student-grade {
    font-size: 14px;
    color: #666;
    margin: 0 0 16px 0;
}

/* Botón de eliminar en la esquina superior derecha */
.student-actions {
    position: absolute;
    top: 12px;
    right: 12px;
}

.student-action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-action-btn.delete-btn {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.student-action-btn.delete-btn:hover {
    background-color: #f44336;
    color: white;
    transform: scale(1.1);
}

.student-action-btn .material-icons {
    font-size: 18px;
}

/* Paginación */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 32px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
    border-color: #2196f3;
}

.pagination-btn.active {
    background-color: #2196f3;
    color: white;
    border-color: #2196f3;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modales */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 900px;
    width: 95%;
    max-height: 85vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.modal-overlay.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 2px solid #f0f0f0;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: white;
}

.modal-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 32px;
    flex: 1;
    overflow-y: auto;
}

/* Estilos para botones de acción */
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.btn-secondary {
    padding: 12px 24px;
    border: 2px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    border-color: #2196f3;
    color: #2196f3;
}

.btn-danger {
    padding: 12px 24px;
    border: none;
    background: #f44336;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: #d32f2f;
    transform: translateY(-1px);
}

/* Estilos específicos para el modal de agregar estudiantes */
.search-students-section {
    margin-bottom: 24px;
}

.search-students-section .search-box {
    margin-bottom: 24px;
    max-width: none;
}

.search-students-section .search-box input {
    font-size: 16px;
    padding: 16px 20px 16px 56px;
    border-radius: 12px;
    border: 2px solid #e3f2fd;
    background-color: #fafafa;
}

.search-students-section .search-box input:focus {
    background-color: white;
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.search-students-section .search-box .material-icons {
    left: 20px;
    font-size: 24px;
    color: #2196f3;
}

.available-students-list {
    max-height: 400px;
    overflow-y: auto;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    background-color: #fafafa;
    padding: 16px;
}

.student-search-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.student-search-item:last-child {
    margin-bottom: 0;
}

.student-search-item:hover {
    border-color: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.student-search-item .student-avatar {
    width: 60px;
    height: 60px;
    margin-right: 16px;
    flex-shrink: 0;
}

.student-search-item .student-info {
    flex: 1;
    margin-right: 16px;
}

.student-search-item .student-info h4 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.student-search-item .student-info .student-grade {
    margin: 0;
    font-size: 14px;
    color: #666;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.add-student-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.add-student-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.add-student-btn .material-icons {
    font-size: 18px;
}

/* Scrollbar personalizada para la lista de estudiantes */
.available-students-list::-webkit-scrollbar {
    width: 8px;
}

.available-students-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.available-students-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.available-students-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }

    .students-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }

    .student-card {
        padding: 16px;
    }

    .modal-content {
        width: 98%;
        max-height: 90vh;
    }

    .modal-header {
        padding: 20px 24px;
    }

    .modal-body {
        padding: 24px;
    }

    .student-search-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .student-search-item .student-avatar {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .student-search-item .student-info {
        margin-right: 0;
        margin-bottom: 8px;
    }
}
