document.addEventListener("DOMContentLoaded", () => {
    console.log("JavaScript contenido_c.js cargado correctamente")

    // La expansión de semanas se maneja en curso.js

    // Manejar clics en los elementos de contenido
    const contentItems = document.querySelectorAll(".content-details")
    console.log("Elementos de contenido encontrados:", contentItems.length)

    contentItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        const id = item.id
        console.log("Clic en elemento con ID:", id)

        if (id === "anuncio-bienvenida") {
          console.log("Abriendo modal de anuncio")
          document.getElementById("announcement-modal").classList.add("active")
        } else if (id === "tarea-sumas-restas") {
          console.log("Abriendo modal de tarea")
          document.getElementById("assignment-view").classList.add("active")
        } else if (id === "video-fracciones") {
          console.log("Abriendo modal de video")
          document.getElementById("video-modal").classList.add("active")
        } else if (id === "enlace-recursos") {
          console.log("Abriendo modal de enlace")
          document.getElementById("link-modal").classList.add("active")
        } else if (id === "documento-teoria") {
          console.log("Abriendo modal de documento")
          document.getElementById("document-modal").classList.add("active")
        } else if (id === "presentacion-fracciones") {
          console.log("Abriendo modal de presentación")
          document.getElementById("presentation-modal").classList.add("active")
        } else if (id === "participacion-clase") {
          console.log("Abriendo modal de participación")
          document.getElementById("participation-modal").classList.add("active")
        } else if (id === "examen-fracciones") {
          console.log("Abriendo modal de examen")
          document.getElementById("exam-modal").classList.add("active")
        }
      })
    })
  
    // Cerrar modales
    const closeButtons = document.querySelectorAll(".modal-close-btn, #close-assignment-btn, #close-video-btn, #close-link-btn, #close-document-btn, #close-presentation-btn, #close-participation-btn, #close-exam-btn")
    closeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
  
    // Cerrar modales al hacer clic fuera del contenido
    const modals = document.querySelectorAll(".modal-overlay")
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })

    // Manejar la subida de archivos
    const fileUploadArea = document.querySelector(".file-upload-area")
    let selectedFiles = []

    if (fileUploadArea) {
      fileUploadArea.addEventListener("click", () => {
        // Crear un input de archivo oculto
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = ".pdf,.jpg,.jpeg,.png"
        fileInput.multiple = true

        // Simular clic en el input
        fileInput.click()

        // Manejar la selección de archivos
        fileInput.addEventListener("change", (e) => {
          const files = e.target.files
          if (files.length > 0) {
            selectedFiles = Array.from(files)
            displaySelectedFiles(selectedFiles)
          }
        })
      })

      // Permitir arrastrar y soltar archivos
      fileUploadArea.addEventListener("dragover", (e) => {
        e.preventDefault()
        fileUploadArea.style.backgroundColor = "var(--primary-light)"
        fileUploadArea.style.borderColor = "var(--primary-color)"
      })

      fileUploadArea.addEventListener("dragleave", () => {
        fileUploadArea.style.backgroundColor = "white"
        fileUploadArea.style.borderColor = "var(--border-color)"
      })

      fileUploadArea.addEventListener("drop", (e) => {
        e.preventDefault()
        fileUploadArea.style.backgroundColor = "white"
        fileUploadArea.style.borderColor = "var(--border-color)"

        const files = e.dataTransfer.files
        if (files.length > 0) {
          selectedFiles = Array.from(files)
          displaySelectedFiles(selectedFiles)
        }
      })
    }

    // Función para mostrar archivos seleccionados
    function displaySelectedFiles(files) {
      const fileUploadText = document.querySelector(".file-upload-text")
      const fileUploadHint = document.querySelector(".file-upload-hint")

      if (files.length > 0) {
        let fileList = "<div class='selected-files'>"
        fileList += "<h4>Archivos seleccionados:</h4>"

        files.forEach((file, index) => {
          const fileSize = (file.size / 1024 / 1024).toFixed(2) // MB
          fileList += `
            <div class='file-item'>
              <span class='file-name'>${file.name}</span>
              <span class='file-size'>(${fileSize} MB)</span>
              <button class='remove-file-btn' data-index='${index}'>
                <span class='material-icons'>close</span>
              </button>
            </div>
          `
        })

        fileList += "</div>"
        fileUploadText.innerHTML = fileList
        fileUploadHint.style.display = "none"

        // Agregar eventos para remover archivos
        const removeButtons = document.querySelectorAll(".remove-file-btn")
        removeButtons.forEach(btn => {
          btn.addEventListener("click", (e) => {
            e.stopPropagation()
            const index = parseInt(btn.dataset.index)
            selectedFiles.splice(index, 1)

            if (selectedFiles.length === 0) {
              resetFileUploadArea()
            } else {
              displaySelectedFiles(selectedFiles)
            }
          })
        })
      }
    }

    // Función para resetear el área de subida
    function resetFileUploadArea() {
      const fileUploadText = document.querySelector(".file-upload-text")
      const fileUploadHint = document.querySelector(".file-upload-hint")

      fileUploadText.innerHTML = "Arrastra y suelta archivos aquí o haz clic para seleccionar archivos"
      fileUploadHint.style.display = "block"
      selectedFiles = []
    }

    // Manejar el botón de entregar tarea
    const submitBtn = document.querySelector(".submission-actions .primary-btn")
    if (submitBtn) {
      submitBtn.addEventListener("click", () => {
        const comments = document.querySelector(".text-submission-textarea").value

        if (selectedFiles.length === 0 && comments.trim() === "") {
          alert("Por favor, selecciona al menos un archivo o añade comentarios antes de entregar la tarea.")
          return
        }

        // Simular envío de archivos
        if (selectedFiles.length > 0) {
          console.log("Archivos a enviar:", selectedFiles)
          // Aquí iría la lógica real para enviar los archivos al servidor
        }

        if (comments.trim() !== "") {
          console.log("Comentarios:", comments)
          // Aquí iría la lógica para enviar los comentarios
        }

        alert("Tarea enviada correctamente")
        resetFileUploadArea()
        document.querySelector(".text-submission-textarea").value = ""
        document.getElementById("assignment-view").classList.remove("active")
      })
    }

    // Manejar funcionalidad específica para exámenes
    const examSubmitBtn = document.querySelector("#exam-modal .submission-actions .primary-btn")
    if (examSubmitBtn) {
      examSubmitBtn.addEventListener("click", () => {
        const examComments = document.querySelector("#exam-modal .text-submission-textarea").value
        const examFiles = document.getElementById("exam-file-input").files

        if (examFiles.length === 0 && examComments.trim() === "") {
          alert("Por favor, selecciona al menos un archivo o añade comentarios antes de entregar el examen.")
          return
        }

        alert("Examen enviado correctamente")
        document.querySelector("#exam-modal .text-submission-textarea").value = ""
        document.getElementById("exam-modal").classList.remove("active")
      })
    }

    // Manejar funcionalidad para enlaces externos
    const linkButtons = document.querySelectorAll(".link-button")
    linkButtons.forEach(button => {
      button.addEventListener("click", (e) => {
        // El enlace se abre en una nueva pestaña por el target="_blank"
        console.log("Abriendo enlace externo:", button.href)
      })
    })

    // Manejar funcionalidad para descargas de documentos
    const downloadButtons = document.querySelectorAll(".download-button")
    downloadButtons.forEach(button => {
      button.addEventListener("click", (e) => {
        console.log("Descargando documento:", button.href)
      })
    })

    // Manejar funcionalidad para presentaciones en pantalla completa
    const fullscreenButtons = document.querySelectorAll(".presentation-button")
    fullscreenButtons.forEach(button => {
      button.addEventListener("click", () => {
        const iframe = document.querySelector("#presentation-modal iframe")
        if (iframe.requestFullscreen) {
          iframe.requestFullscreen()
        } else if (iframe.webkitRequestFullscreen) {
          iframe.webkitRequestFullscreen()
        } else if (iframe.msRequestFullscreen) {
          iframe.msRequestFullscreen()
        }
      })
    })

    // Agregar estilos CSS para los archivos seleccionados
    const style = document.createElement("style")
    style.textContent = `
      .selected-files {
        text-align: left;
        margin-top: 10px;
      }

      .selected-files h4 {
        margin-bottom: 10px;
        color: var(--text-color);
        font-size: 1rem;
      }

      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 6px;
        margin-bottom: 8px;
        border: 1px solid var(--border-color);
      }

      .file-name {
        font-weight: 500;
        color: var(--text-color);
        flex: 1;
      }

      .file-size {
        font-size: 0.85rem;
        color: var(--text-light);
        margin-left: 10px;
      }

      .remove-file-btn {
        background: none;
        border: none;
        color: var(--danger-color);
        cursor: pointer;
        padding: 2px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        margin-left: 10px;
      }

      .remove-file-btn:hover {
        background-color: var(--danger-color);
        color: white;
      }

      .remove-file-btn .material-icons {
        font-size: 16px;
      }
    `
    document.head.appendChild(style)
  })

  // Función global para descargar archivos
  function downloadFile(filename) {
    console.log("Descargando archivo:", filename)

    // Crear un enlace temporal para la descarga
    const link = document.createElement('a')
    link.href = `./archivos/${filename}`
    link.download = filename
    link.style.display = 'none'

    // Agregar al DOM, hacer clic y remover
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Mostrar mensaje de confirmación
    showDownloadMessage(filename)
  }

  // Función para abrir presentaciones
  function openPresentation(filename) {
    console.log("Abriendo presentación:", filename)

    // Simular apertura de presentación en nueva ventana
    const presentationUrl = `./archivos/${filename}`
    window.open(presentationUrl, '_blank', 'width=1024,height=768,scrollbars=yes,resizable=yes')

    // Mostrar mensaje informativo
    showMessage("Abriendo presentación en nueva ventana...")
  }

  // Función para mostrar mensajes de descarga
  function showDownloadMessage(filename) {
    // Crear elemento de notificación
    const notification = document.createElement('div')
    notification.className = 'download-notification'
    notification.innerHTML = `
      <div class="notification-content">
        <span class="material-icons">download_done</span>
        <span>Descargando: ${filename}</span>
      </div>
    `

    // Agregar estilos
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      animation: slideIn 0.3s ease-out;
    `

    // Agregar animación CSS
    if (!document.querySelector('#download-animation-styles')) {
      const animationStyles = document.createElement('style')
      animationStyles.id = 'download-animation-styles'
      animationStyles.textContent = `
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes slideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
      `
      document.head.appendChild(animationStyles)
    }

    // Mostrar notificación
    document.body.appendChild(notification)

    // Remover después de 3 segundos
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-out'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // Función para mostrar mensajes generales
  function showMessage(message) {
    const notification = document.createElement('div')
    notification.className = 'info-notification'
    notification.innerHTML = `
      <div class="notification-content">
        <span class="material-icons">info</span>
        <span>${message}</span>
      </div>
    `

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #2196f3;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      animation: slideIn 0.3s ease-out;
    `

    document.body.appendChild(notification)

    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-out'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 2000)
  }