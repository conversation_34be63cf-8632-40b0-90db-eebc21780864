<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Justificaciones de Asistencia - Escuela</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/justificaciones_asistencia.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Sidebar -->
        <aside class="sidebar collapsed">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                
                </div>
                <button class="sidebar-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>

            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Admin Sistema</h3>
                        <p>Administrador</p>
                    </div>
                </div>

                <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="inicio_a.html">
                            <span class="material-icons">dashboard</span>
                            <span>Inicio</span>
                        </a>
                    </li>
                    <li>
                        <a href="perfil_a.html">
                            <span class="material-icons">person</span>
                            <span>Perfil</span>
                        </a>
                    </li>
                    <li>
                        <a href="usuarios_a.html">
                            <span class="material-icons">people</span>
                            <span>Usuarios</span>
                        </a>
                    </li>
                    <li>
                        <a href="anuncios_admin.html">
                            <span class="material-icons">campaign</span>
                            <span>Anuncios</span>
                        </a>
                    </li>
                    <li>
                        <a href="admision_a.html">
                            <span class="material-icons">how_to_reg</span>
                            <span>Solicitudes de Admisión</span>
                        </a>
                    </li>
                    <li>
                        <a href="configuracion_admin.html">
                            <span class="material-icons">settings</span>
                            <span>Configuración</span>
                        </a>
                    </li>
                    <li class="separator"></li>
                    <li>
                        <a href="login.html" class="logout-link">
                            <span class="material-icons">logout</span>
                            <span>Cerrar Sesión</span>
                        </a>
                    </li>
                </ul>
            </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Gestión de Justificaciones de Asistencia</h1>
                    <nav class="breadcrumb">
                        <a href="usuarios_a.html">Usuarios</a>
                        <span class="separator">/</span>
                        <span class="current">Justificaciones de Asistencia</span>
                    </nav>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">5</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content-body">
                <!-- Teacher Info Header -->
                <div class="teacher-info-card">
                    <div class="teacher-avatar">
                        <img src="/placeholder.svg?height=60&width=60" alt="Carlos García">
                    </div>
                    <div class="teacher-details">
                        <h2 id="teacher-name">Carlos García</h2>
                        <p class="teacher-role">Maestro - Matemáticas</p>
                        <p class="teacher-grade">Grado Tutor: 5° Primaria</p>
                    </div>
                    <div class="teacher-stats">
                        <div class="stat-item">
                            <span class="stat-value">98%</span>
                            <span class="stat-label">Asistencia</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">2</span>
                            <span class="stat-label">Faltas</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">1</span>
                            <span class="stat-label">Tardanzas</span>
                        </div>
                    </div>
                </div>

                <!-- Filters and Actions -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="filter-group">
                            <label for="status-filter">Filtrar por estado:</label>
                            <select id="status-filter" class="filter-select">
                                <option value="all">Todas las solicitudes</option>
                                <option value="pendiente">Pendientes</option>
                                <option value="aprobada">Aprobadas</option>
                                <option value="rechazada">Rechazadas</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="type-filter">Filtrar por tipo:</label>
                            <select id="type-filter" class="filter-select">
                                <option value="all">Todos los tipos</option>
                                <option value="tardanza">Tardanza</option>
                                <option value="falta">Falta</option>
                                <option value="salida-anticipada">Salida Anticipada</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn-secondary" onclick="window.history.back()">
                            <span class="material-icons">arrow_back</span>
                            Volver
                        </button>
                    </div>
                </div>

                <!-- Justifications List -->
                <div class="justifications-container">
                    <!-- Solicitud pendiente -->
                    <div class="justification-card" data-status="pendiente" data-type="tardanza">
                        <div class="card-header">
                            <div class="date-info">
                                <span class="material-icons">event</span>
                                <span class="date">19/03/2025</span>
                            </div>
                            <div class="badges">
                                <span class="type-badge tardanza">Tardanza</span>
                                <span class="status-badge pendiente">Pendiente</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="reason-section">
                                <h4>Motivo de la justificación:</h4>
                                <p>Tráfico intenso en la ruta principal debido a obras en construcción</p>
                            </div>
                            <div class="actions-section">
                                <button class="btn-success" onclick="processJustification(this, 'aprobada')">
                                    <span class="material-icons">check</span>
                                    Aprobar
                                </button>
                                <button class="btn-danger" onclick="processJustification(this, 'rechazada')">
                                    <span class="material-icons">close</span>
                                    Rechazar
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Solicitud aprobada -->
                    <div class="justification-card" data-status="aprobada" data-type="salida-anticipada">
                        <div class="card-header">
                            <div class="date-info">
                                <span class="material-icons">event</span>
                                <span class="date">10/03/2025</span>
                            </div>
                            <div class="badges">
                                <span class="type-badge salida-anticipada">Salida Anticipada</span>
                                <span class="status-badge aprobada">Aprobada</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="reason-section">
                                <h4>Motivo de la justificación:</h4>
                                <p>Cita médica programada con especialista</p>
                            </div>
                            <div class="processed-info">
                                <span class="material-icons">check_circle</span>
                                <span>Aprobada el 10/03/2025 por Administrador</span>
                            </div>
                        </div>
                    </div>

                    <!-- Solicitud rechazada -->
                    <div class="justification-card" data-status="rechazada" data-type="falta">
                        <div class="card-header">
                            <div class="date-info">
                                <span class="material-icons">event</span>
                                <span class="date">05/03/2025</span>
                            </div>
                            <div class="badges">
                                <span class="type-badge falta">Falta</span>
                                <span class="status-badge rechazada">Rechazada</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="reason-section">
                                <h4>Motivo de la justificación:</h4>
                                <p>Enfermedad - Reposo médico</p>
                            </div>
                            <div class="processed-info rejected">
                                <span class="material-icons">cancel</span>
                                <span>Rechazada el 06/03/2025 - Documentación insuficiente</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/justificaciones_asistencia.js"></script>
</body>
</html>
