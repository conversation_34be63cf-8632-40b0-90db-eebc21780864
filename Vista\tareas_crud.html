<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - CRUD de Tareas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_crud.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                  </div>
                  <div class="user-details">
                      <h3>Carlos García</h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.html">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.html">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.html">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.html">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_m.html" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1>Matemáticas</h1>
                          <p>5° Primaria</p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <div class="schedule-day">
                              <span class="day-label">Lunes</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                          <div class="schedule-day">
                              <span class="day-label">Miércoles</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.html" class="course-tab">Contenido</a>
                  <a href="tareas_menu.html" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.html" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de CRUD de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Administración de Tareas y Exámenes</h2>
                      <div class="section-actions">
                          <div class="search-container">
                              <input type="text" class="search-input" id="task-search" placeholder="Buscar contenido...">
                              <span class="material-icons search-icon">search</span>
                          </div>
                          <button class="action-btn create-task-btn" id="create-task-btn">
                              <span class="material-icons">add</span>
                              <span>Nuevo Contenido</span>
                          </button>
                          <a href="tareas_menu.html" class="action-btn view-mode-btn">
                              <span class="material-icons">view_list</span>
                              <span>Cambiar Vista</span>
                          </a>
                      </div>
                  </div>
                  
                  <div class="tasks-table-container">
                      <table class="tasks-table">
                          <thead>
                              <tr>
                                  <th>Título</th>
                                  <th>Tipo</th>
                                  <th>Semana</th>
                                  <th>Fecha Límite</th>
                                  <th>Hora Límite</th>
                                  <th>Puntos</th>
                                  <th>Entregas</th>
                                  <th>Acciones</th>
                              </tr>
                          </thead>
                          <tbody id="tasks-table-body">
                              <!-- Las filas se cargarán dinámicamente con JavaScript -->
                          </tbody>
                      </table>
                  </div>
                  
                  <div class="pagination">
                      <button class="pagination-btn" id="prev-page" disabled>
                          <span class="material-icons">chevron_left</span>
                      </button>
                      <div class="pagination-info">
                          Página <span id="current-page">1</span> de <span id="total-pages">1</span>
                      </div>
                      <button class="pagination-btn" id="next-page">
                          <span class="material-icons">chevron_right</span>
                      </button>
                  </div>
              </section>
          </div>
      </main>
  </div>
  
  <!-- Modal para crear/editar tarea -->
  <div id="task-form-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3 id="task-form-title">Nuevo Contenido</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form id="task-form">
                  <input type="hidden" id="task-id">

                  <div class="form-group">
                      <label for="content-type">Tipo de Contenido</label>
                      <select id="content-type" required>
                          <option value="">Seleccionar tipo</option>
                          <option value="tarea">Tarea</option>
                          <option value="examen">Examen</option>
                      </select>
                  </div>

                  <div class="form-group">
                      <label for="task-title">Título</label>
                      <input type="text" id="task-title" placeholder="Ej: Ejercicios de fracciones" required>
                  </div>

                  <div class="form-group">
                      <label for="task-week">Semana</label>
                      <select id="task-week" required>
                          <option value="">Seleccionar semana</option>
                          <option value="1">Semana 1: Introducción a las fracciones</option>
                          <option value="2">Semana 2: Operaciones con fracciones</option>
                          <option value="3">Semana 3: Fracciones equivalentes</option>
                          <option value="4">Semana 4: Problemas con fracciones</option>
                      </select>
                  </div>

                  <div class="form-group">
                      <label for="task-description">Contenido Completo <span class="required">*</span></label>
                      <div class="rich-text-toolbar">
                          <button type="button" class="toolbar-btn" data-command="bold" title="Negrita">
                              <strong>B</strong>
                          </button>
                          <button type="button" class="toolbar-btn" data-command="italic" title="Cursiva">
                              <em>I</em>
                          </button>
                          <button type="button" class="toolbar-btn" data-command="underline" title="Subrayado">
                              <u>U</u>
                          </button>
                          <button type="button" class="toolbar-btn" data-command="strikeThrough" title="Tachado">
                              <s>T</s>
                          </button>
                          <div class="toolbar-separator"></div>
                          <button type="button" class="toolbar-btn" data-command="insertUnorderedList" title="Lista con viñetas">
                              <span class="material-icons">format_list_bulleted</span>
                          </button>
                          <button type="button" class="toolbar-btn" data-command="insertOrderedList" title="Lista numerada">
                              <span class="material-icons">format_list_numbered</span>
                          </button>
                          <button type="button" class="toolbar-btn" data-command="createLink" title="Insertar enlace">
                              <span class="material-icons">link</span>
                          </button>
                          <button type="button" class="toolbar-btn" id="insert-image-btn" title="Insertar imagen">
                              <span class="material-icons">image</span>
                          </button>
                      </div>
                      <div id="task-description" class="rich-text-editor" contenteditable="true" placeholder="Escribe el contenido completo aquí..."></div>
                      <input type="file" id="image-upload" accept="image/*" style="display: none;">
                  </div>

                  <div class="form-row">
                      <div class="form-group">
                          <label for="task-due-date">Fecha límite</label>
                          <input type="date" id="task-due-date" required>
                      </div>

                      <div class="form-group">
                          <label for="task-due-time">Hora límite</label>
                          <input type="time" id="task-due-time" required>
                      </div>
                  </div>

                  <div class="form-group">
                      <label for="task-points">Puntos</label>
                      <input type="number" id="task-points" min="1" max="100" placeholder="10" required>
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary" id="save-task-btn">Guardar</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal de confirmación para eliminar -->
  <div id="delete-confirm-modal" class="modal-overlay">
      <div class="modal-content confirmation-modal">
          <div class="modal-header">
              <h3>Confirmar Eliminación</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <div class="confirmation-message">
                  <p id="delete-confirmation-text">¿Desea eliminar este contenido?</p>
              </div>
              
              <div class="form-actions">
                  <button class="btn-secondary modal-close-btn">Cancelar</button>
                  <button class="btn-danger" id="confirm-delete-btn">Eliminar</button>
              </div>
          </div>
      </div>
  </div>

  <!-- Modal para visualizar tarea -->
  <div id="task-view-modal" class="modal-overlay">
      <div class="modal-content assignment-container">
          <div class="assignment-header">
              <h1 class="assignment-title" id="task-view-title">
                  <span class="material-icons">assignment</span>
                  Tarea: Ejercicios de Fracciones
              </h1>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>

          <div class="assignment-content">
              <div class="assignment-section">
                  <h2 class="assignment-section-title" id="view-content-description-title">Descripción del contenido</h2>
                  <div id="view-task-description" class="assignment-description">
                      <!-- Contenido de la descripción se cargará aquí -->
                  </div>
                  <div class="task-image-section" id="task-image-section" style="display: none;">
                      <img id="view-task-image" src="" alt="Imagen del contenido" class="assignment-image">
                  </div>
              </div>

              <div class="assignment-section">
                  <div class="assignment-meta">
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">event</span>
                              Fecha de entrega
                          </div>
                          <div class="meta-value" id="view-task-due-date">17/3/2025, 11:59 PM</div>
                      </div>

                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">grade</span>
                              Puntos
                          </div>
                          <div class="meta-value" id="view-task-points">10 puntos máximos</div>
                      </div>

                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">person</span>
                              Asignado por
                          </div>
                          <div class="meta-value">Prof. Carlos García</div>
                      </div>
                  </div>
              </div>

              <div class="assignment-section">
                  <div class="submission-section">
                      <h2 class="submission-title" id="view-content-options-title">Estado de la tarea</h2>
                      <div class="task-status-info">
                          <div class="status-item">
                              <span class="material-icons status-icon">schedule</span>
                              <div class="status-content">
                                  <span class="status-label">Estado actual</span>
                                  <span class="status-value">Activa - Los estudiantes pueden entregar hasta el 17/3/2025</span>
                              </div>
                          </div>
                      </div>
                      <div class="submission-options">
                          <div class="form-actions">
                              <button class="btn-secondary modal-close-btn">Cerrar</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/tareas_crud.js"></script>
</body>
</html>
