<?php
session_start();

// Obtener mensaje de logout exitoso si existe
$logoutSuccess = $_SESSION['logout_success'] ?? '';
unset($_SESSION['logout_success']);

// Si ya está autenticado Y no es un logout reciente, redirigir según su rol
if (isset($_SESSION['usuario_id']) && isset($_SESSION['rol']) && empty($logoutSuccess)) {
    switch ($_SESSION['rol']) {
        case 'administrador':
            header('Location: inicio_a.php');
            break;
        case 'maestro':
            header('Location: inicio_m.php');
            break;
        case 'padre':
            header('Location: inicio_p.php');
            break;
        case 'estudiante':
            header('Location: inicio_e.php');
            break;
    }
    exit();
}

// Obtener mensaje de error si existe
$errorLogin = $_SESSION['error_login'] ?? '';
unset($_SESSION['error_login']);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Escuela - Intranet</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/intranet.css">
</head>
<body>
    <div class="login-container">
        <!-- Panel izquierdo con branding -->
        <div class="login-left">
            <div class="brand-container">
                <div class="logo-circle">
                    <div class="logo-text">SCB</div>
                </div>
                <h2 class="brand-name">SAGRADO CORAZÓN DE BELÉN</h2>
                <p class="brand-tagline">Preschool - Elementary School</p>
            </div>
        </div>

        <!-- Panel derecho con formulario -->
        <div class="login-right">
            <div class="login-form-container">
                <div class="welcome-header">
                    <h1>Bienvenido</h1>
                    <p class="welcome-subtitle">Inicia sesión en tu cuenta para continuar</p>
                </div>

                <?php if (!empty($errorLogin)): ?>
                    <div class="error-message" style="background-color: #fee; border: 1px solid #fcc; color: #c33; padding: 10px; border-radius: 5px; margin-bottom: 15px; text-align: center;">
                        <?php echo htmlspecialchars($errorLogin); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($logoutSuccess)): ?>
                    <div class="success-message" style="background-color: #efe; border: 1px solid #cfc; color: #3c3; padding: 10px; border-radius: 5px; margin-bottom: 15px; text-align: center;">
                        <?php echo htmlspecialchars($logoutSuccess); ?>
                    </div>
                <?php endif; ?>

                <form id="login-form" class="login-form" method="POST" action="../Controlador/AuthController.php?action=login">
                    <div class="form-group">
                        <input type="text" id="usuario" name="usuario" placeholder="Nombre de usuario" required>
                    </div>

                    <div class="form-group">
                        <input type="password" id="password" name="password" placeholder="Contraseña" required>
                    </div>

                    <div class="form-options">
                        <a href="#" class="forgot-password">Olvistaste tu clave, comunicate con secretaria</a>
                    </div>

                    <button type="submit" class="login-btn">INICIAR SESIÓN</button>
                </form>
            </div>
        </div>
    </div>

    <script src="./Js/intranet.js"></script>
</body>
</html>