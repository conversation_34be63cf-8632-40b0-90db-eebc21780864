<?php
// Archivo de prueba para verificar las inscripciones
require_once 'Modelo/Conexion.php';
require_once 'Controlador/InscripcionController.php';

try {
    $conexion = Conexion::getConexion();
    
    // Verificar que las tablas existen
    echo "<h2>Verificando estructura de base de datos:</h2>";
    
    $tablas = ['inscripciones', 'estudiantes', 'cursos'];
    foreach ($tablas as $tabla) {
        $query = "SHOW TABLES LIKE '$tabla'";
        $stmt = $conexion->prepare($query);
        $stmt->execute();
        $existe = $stmt->fetchColumn();
        
        if ($existe) {
            echo "✅ Tabla '$tabla' existe<br>";
            
            // Contar registros
            $countQuery = "SELECT COUNT(*) FROM $tabla";
            $countStmt = $conexion->prepare($countQuery);
            $countStmt->execute();
            $count = $countStmt->fetchColumn();
            echo "&nbsp;&nbsp;&nbsp;→ $count registros<br>";
        } else {
            echo "❌ Tabla '$tabla' NO existe<br>";
        }
    }
    
    echo "<h2>Verificando inscripciones:</h2>";
    
    // Mostrar inscripciones activas
    $queryInscripciones = "SELECT i.*, 
                                  CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as estudiante_nombre,
                                  c.nombre as curso_nombre
                           FROM inscripciones i
                           JOIN estudiantes e ON i.estudiante_id = e.id
                           JOIN personas p ON e.persona_id = p.id
                           JOIN cursos c ON i.curso_id = c.id
                           WHERE i.activo = 1
                           ORDER BY c.nombre, p.apellido_paterno";
    
    $stmt = $conexion->prepare($queryInscripciones);
    $stmt->execute();
    $inscripciones = $stmt->fetchAll();
    
    if (empty($inscripciones)) {
        echo "⚠️ No hay inscripciones activas<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Estudiante</th><th>Curso</th><th>Fecha Inscripción</th></tr>";
        foreach ($inscripciones as $inscripcion) {
            echo "<tr>";
            echo "<td>{$inscripcion['id']}</td>";
            echo "<td>{$inscripcion['estudiante_nombre']}</td>";
            echo "<td>{$inscripcion['curso_nombre']}</td>";
            echo "<td>{$inscripcion['fecha_inscripcion']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>Verificando controlador:</h2>";
    
    // Probar el controlador
    $controller = new InscripcionController();
    
    // Obtener primer curso disponible
    $queryCurso = "SELECT id, nombre FROM cursos WHERE activo = 1 LIMIT 1";
    $stmt = $conexion->prepare($queryCurso);
    $stmt->execute();
    $curso = $stmt->fetch();
    
    if ($curso) {
        echo "🔍 Probando con curso: {$curso['nombre']} (ID: {$curso['id']})<br>";
        
        $inscritos = $controller->obtenerInscritos($curso['id']);
        echo "✅ Estudiantes inscritos: " . count($inscritos) . "<br>";
        
        $disponibles = $controller->obtenerDisponibles($curso['id']);
        echo "✅ Estudiantes disponibles: " . count($disponibles) . "<br>";
        
        $total = $controller->contarInscritos($curso['id']);
        echo "✅ Total contado: $total<br>";
        
    } else {
        echo "❌ No hay cursos disponibles para probar<br>";
    }
    
    echo "<h2>Estado: ✅ SISTEMA FUNCIONANDO</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ ERROR:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
