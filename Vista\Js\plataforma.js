document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const menuToggle = document.getElementById("menu-toggle") || document.querySelector(".sidebar-toggle")
  const sidebar = document.querySelector(".sidebar")
  const plataformaContainer = document.querySelector(".plataforma-container")
  const courseHeader = document.querySelector(".course-header")
  const contentHeader = document.querySelector(".content-header")

  // Detectar si es un dispositivo móvil
  const isMobile = window.innerWidth <= 992

  // Si hay un encabezado de curso, añadir la clase para ocultar el botón de menú móvil
  if (courseHeader) {
    document.body.classList.add("has-course-header")
    plataformaContainer.classList.add("has-course-header")
  }

  // Eliminar cualquier botón de menú móvil que pueda existir
  const mobileMenuBtns = document.querySelectorAll(".mobile-menu-btn")
  if (mobileMenuBtns.length > 0) {
    mobileMenuBtns.forEach(btn => btn.remove())
  }

  // Función para alternar el estado del menú lateral
  if (menuToggle) {
    menuToggle.addEventListener("click", () => {
      if (isMobile) {
        sidebar.classList.toggle("active")
      } else {
        sidebar.classList.toggle("collapsed")
      }
    })
  }

  // En dispositivos móviles, colapsar el menú por defecto
  if (isMobile) {
    sidebar.classList.add("collapsed")

    // Añadir evento para cerrar el menú al hacer clic fuera de él
    document.addEventListener("click", (e) => {
      if (
        !sidebar.contains(e.target) &&
        !menuToggle.contains(e.target)
      ) {
        sidebar.classList.remove("active")
      }
    })
  }

  // Actualizar fecha actual
  const dateElements = document.querySelectorAll(".current-date")
  if (dateElements.length > 0) {
    const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
    const today = new Date()
    dateElements.forEach((element) => {
      element.textContent = today.toLocaleDateString("es-ES", options)
    })
  }

  // Animación para las tarjetas de actividad
  const activityCards = document.querySelectorAll(".activity-card")
  activityCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`
    card.classList.add("fade-in")
  })
})
