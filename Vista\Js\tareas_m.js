document.addEventListener("DOMContentLoaded", () => {
    // Datos de ejemplo para estudiantes
    const students = [
      { id: 1, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 2, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 3, name: "<PERSON><PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 4, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 5, name: "<PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 6, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 7, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 8, name: "<PERSON> Flores", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 9, name: "<PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 10, name: "<PERSON><PERSON><PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
    ]
  
    // Datos de ejemplo para tareas
    const tasks = [
      {
        id: 1,
        title: "Ejercicios de Fracciones",
        description: "Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.",
        dueDate: "2025-03-18",
        points: 10,
        weekId: 1,
        weekTitle: "Semana 1: Introducción a las fracciones",
        weekDates: "15/03/2025 - 21/03/2025",
        status: "active",
      },
      {
        id: 2,
        title: "Representación gráfica de fracciones",
        description: "Representar gráficamente las fracciones indicadas.",
        dueDate: "2025-03-10",
        points: 10,
        weekId: 1,
        weekTitle: "Semana 1: Introducción a las fracciones",
        weekDates: "15/03/2025 - 21/03/2025",
        status: "graded",
      },
      {
        id: 3,
        title: "Multiplicación de fracciones",
        description: "Ejercicios de multiplicación de fracciones.",
        dueDate: "2025-03-25",
        points: 15,
        weekId: 2,
        weekTitle: "Semana 2: Operaciones con fracciones",
        weekDates: "22/03/2025 - 28/03/2025",
        status: "active",
      },
      {
        id: 4,
        title: "Examen: Evaluación de fracciones",
        description: "Examen sobre conceptos básicos y operaciones con fracciones.",
        dueDate: "2025-03-28",
        points: 20,
        weekId: 2,
        weekTitle: "Semana 2: Operaciones con fracciones",
        weekDates: "22/03/2025 - 28/03/2025",
        status: "active",
        type: "exam",
      },
    ]
  
    // Datos de ejemplo para entregas
    const submissions = {
      1: [
        // Tarea ID 1
        {
          studentId: 1,
          status: "submitted",
          submissionDate: "2025-03-17T10:45:00",
          grade: 8,
          maxGrade: 10,
          isGraded: true,
          feedback:
            "Buen trabajo Ana. Los ejercicios están bien resueltos en general. En el ejercicio 5 te faltó simplificar la fracción final, y en el 7 hay un pequeño error en el proceso. Revisa las correcciones.",
          files: [
            {
              name: "Ejercicios_Fracciones_AnaGarcia.pdf",
              size: "1.2 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments:
            "Profesor, adjunto mi tarea de ejercicios de fracciones. Tuve algunas dudas en los ejercicios 5 y 7, pero intenté resolverlos lo mejor posible.",
        },
        {
          studentId: 2,
          status: "late",
          submissionDate: "2025-03-18T23:30:00",
          grade: null,
          maxGrade: 10,
          isGraded: false,
          files: [
            {
              name: "Ejercicios_Fracciones_CarlosRodriguez.pdf",
              size: "980 KB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Disculpe la tardanza profesor, tuve problemas con mi conexión a internet.",
        },
        {
          studentId: 4,
          status: "submitted",
          submissionDate: "2025-03-16T14:20:00",
          grade: 9,
          maxGrade: 10,
          isGraded: true,
          feedback: "Excelente trabajo Diego. Todos los ejercicios están correctamente resueltos y bien presentados.",
          files: [
            {
              name: "Ejercicios_Fracciones_DiegoLopez.pdf",
              size: "1.5 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Aquí está mi tarea profesor.",
        },
        {
          studentId: 5,
          status: "submitted",
          submissionDate: "2025-03-17T16:15:00",
          grade: 7,
          maxGrade: 10,
          isGraded: true,
          feedback:
            "Buen intento Valentina, pero hay varios errores en los ejercicios 3, 6 y 8. Revisa las operaciones con denominadores diferentes.",
          files: [
            {
              name: "Tarea_Fracciones_ValentinaTorres.pdf",
              size: "1.1 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Profesor, aquí está mi tarea. Tuve dificultades con algunos ejercicios.",
        },
        {
          studentId: 7,
          status: "late",
          submissionDate: "2025-03-19T08:10:00",
          grade: null,
          maxGrade: 10,
          isGraded: false,
          files: [
            {
              name: "Ejercicios_Isabella.pdf",
              size: "1.3 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Disculpe la tardanza, estuve enferma.",
        },
        // Los demás estudiantes no han entregado
      ],
      2: [
        // Tarea ID 2
        // Datos de ejemplo para la tarea 2
      ],
      3: [
        // Tarea ID 3
        // Datos de ejemplo para la tarea 3
      ],
      4: [
        // Examen ID 4
        {
          studentId: 1,
          status: "submitted",
          submissionDate: "2025-03-27T14:30:00",
          grade: 18,
          maxGrade: 20,
          isGraded: true,
          feedback: "Excelente trabajo Ana. Dominas muy bien los conceptos de fracciones. Solo un pequeño error en la pregunta 7.",
          files: [
            {
              name: "Examen_Fracciones_AnaGarcia.pdf",
              size: "2.1 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Profesor, aquí está mi examen. Espero haber respondido bien todas las preguntas.",
        },
        {
          studentId: 4,
          status: "submitted",
          submissionDate: "2025-03-27T16:45:00",
          grade: 16,
          maxGrade: 20,
          isGraded: true,
          feedback: "Buen trabajo Diego. Tienes una buena comprensión de las fracciones, pero necesitas practicar más las operaciones complejas.",
          files: [
            {
              name: "Examen_Fracciones_DiegoLopez.pdf",
              size: "1.8 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Aquí está mi examen profesor.",
        },
        {
          studentId: 5,
          status: "submitted",
          submissionDate: "2025-03-28T10:15:00",
          grade: null,
          maxGrade: 20,
          isGraded: false,
          files: [
            {
              name: "Examen_ValentinaTorres.pdf",
              size: "1.9 MB",
              type: "pdf",
              url: "#",
            },
          ],
          comments: "Profesor, adjunto mi examen. Algunas preguntas me resultaron difíciles.",
        },
        // Los demás estudiantes aún no han entregado
      ],
    }
  
    // Elementos DOM
    const viewTaskBtns = document.querySelectorAll(".task-action-btn.view-btn")
    const submissionsBtns = document.querySelectorAll(".task-action-btn.submissions-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const taskViewModal = document.getElementById("task-view-modal")
    const submissionsModal = document.getElementById("submissions-modal")
    const studentSubmissionModal = document.getElementById("student-submission-modal")
    const searchInput = document.querySelector(".search-input")
    const submissionFilter = document.getElementById("submission-filter")
    const submissionsList = document.querySelector(".submissions-list")
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
    }
  
    function setupEventListeners() {
      // Event listeners para botones de ver tarea
      viewTaskBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          openTaskViewModal(btn.dataset.taskId)
        })
      })
  
      // Event listeners para botones de ver entregas
      submissionsBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          openSubmissionsModal(btn.dataset.taskId)
        })
      })
  
      // Event listeners para cerrar modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })
  
      // Event listener para búsqueda
      if (searchInput) {
        searchInput.addEventListener("input", filterTasks)
      }
  
      // Event listener para filtro de entregas
      if (submissionFilter) {
        submissionFilter.addEventListener("change", filterSubmissions)
      }
  
      // Event listener para el botón de guardar calificación
      document.addEventListener("click", (e) => {
        if (e.target.classList.contains("save-grade-btn")) {
          saveGrade(e.target.dataset.studentId, e.target.dataset.taskId)
        }
      })
  
      // Event listener para ver entrega de estudiante
      document.addEventListener("click", (e) => {
        if (e.target.closest(".submission-action-btn.view-btn")) {
          const btn = e.target.closest(".submission-action-btn.view-btn")
          openStudentSubmissionModal(btn.dataset.studentId, btn.dataset.taskId)
        }
      })
    }
  
    function openTaskViewModal(taskId) {
      // Buscar la tarea por ID
      const task = tasks.find((t) => t.id == taskId)
  
      if (!task) return
  
      // Actualizar el contenido del modal
      if (taskViewModal) {
        const modalHeader = taskViewModal.querySelector(".assignment-title")
        const taskDescription = taskViewModal.querySelector(".assignment-description")
        const taskDueDate = taskViewModal.querySelector(".meta-value")
        const taskPoints = taskViewModal.querySelectorAll(".meta-value")[1]
        const taskSubmissionsCount = taskViewModal.querySelectorAll(".meta-value")[2]

        // Determinar si es tarea o examen
        const taskType = task.type === "exam" ? "Examen" : "Tarea"
        modalHeader.innerHTML = `<span class="material-icons">assignment</span>${taskType}: ${task.title.replace(/^(Tarea:|Examen:)\s*/, "")}`
        taskDescription.textContent = task.description
  
        // Formatear fecha
        const dueDate = new Date(task.dueDate)
        taskDueDate.textContent = `${dueDate.toLocaleDateString("es-ES")}, 11:59 PM`
  
        taskPoints.textContent = `${task.points} puntos máximos`
  
        // Contar entregas y actualizar estado de entrega
        const taskSubmissions = submissions[taskId] || []
        const statusValue = taskViewModal.querySelector(".status-value")
        if (statusValue) {
          statusValue.textContent = `Activa - Los estudiantes pueden entregar hasta el ${dueDate.toLocaleDateString("es-ES")}`
        }

        taskViewModal.classList.add("active")
      }
    }
  
    function openSubmissionsModal(taskId) {
      // Buscar la tarea por ID
      const task = tasks.find((t) => t.id == taskId)
  
      if (!task) return
  
      // Actualizar el título del modal
      const modalHeader = submissionsModal.querySelector(".modal-header h3")
      modalHeader.textContent = `Entregas: ${task.title}`
  
      // Obtener las entregas de esta tarea
      const taskSubmissions = submissions[taskId] || []
  
      // Calcular estadísticas
      const submitted = taskSubmissions.filter((s) => s.status === "submitted").length
      const late = taskSubmissions.filter((s) => s.status === "late").length
      const pending = students.length - submitted - late
      const graded = taskSubmissions.filter((s) => s.isGraded).length
  
      // Actualizar estadísticas
      const statItems = submissionsModal.querySelectorAll(".stat-item .stat-value")
      statItems[0].textContent = submitted + late // Total entregadas
      statItems[1].textContent = late // Tardías
      statItems[2].textContent = pending // Pendientes
      statItems[3].textContent = graded // Calificadas
  
      // Limpiar la lista de entregas
      submissionsList.innerHTML = ""
  
      // Generar la lista de entregas
      students.forEach((student) => {
        // Buscar si el estudiante ha entregado
        const submission = taskSubmissions.find((s) => s.studentId === student.id)
  
        // Crear el elemento de entrega
        const submissionItem = document.createElement("div")
  
        if (submission) {
          // El estudiante ha entregado
          submissionItem.className = `submission-item ${submission.status}`
  
          let statusText = ""
          let statusIcon = ""
  
          if (submission.status === "submitted") {
            statusText = "Entregada"
            statusIcon = "check_circle"
          } else if (submission.status === "late") {
            statusText = "Tardía"
            statusIcon = "schedule"
          }
  
          // Formatear fecha de entrega
          const submissionDate = new Date(submission.submissionDate)
          const formattedDate = `${submissionDate.toLocaleDateString("es-ES")}, ${submissionDate.toLocaleTimeString("es-ES", { hour: "2-digit", minute: "2-digit" })}`
  
          submissionItem.innerHTML = `
            <div class="student-info">
              <div class="student-avatar">
                <img src="${student.avatar}" alt="${student.name}">
              </div>
              <div class="student-details">
                <span class="student-name">${student.name}</span>
              </div>
            </div>
            <div class="submission-details">
              <div class="submission-status ${submission.status === "submitted" ? "on-time" : "late"}">
                <span class="material-icons">${statusIcon}</span>
                <span>${statusText}</span>
              </div>
              <div class="submission-date">${formattedDate}</div>
            </div>
            <div class="submission-grade ${submission.isGraded ? "" : "pending"}">
              ${
                submission.isGraded
                  ? `<span class="grade-value">${submission.grade}</span>
                 <span class="grade-max">/${submission.maxGrade}</span>`
                  : "<span>Sin calificar</span>"
              }
            </div>
            <div class="submission-actions">
              <button class="submission-action-btn view-btn" data-student-id="${student.id}" data-task-id="${taskId}">
                <span class="material-icons">visibility</span>
                Ver entrega
              </button>
            </div>
          `
        } else {
          // El estudiante no ha entregado
          submissionItem.className = "submission-item pending"
  
          submissionItem.innerHTML = `
            <div class="student-info">
              <div class="student-avatar">
                <img src="${student.avatar}" alt="${student.name}">
              </div>
              <div class="student-details">
                <span class="student-name">${student.name}</span>
              </div>
            </div>
            <div class="submission-details">
              <div class="submission-status pending">
                <span class="material-icons">hourglass_empty</span>
                <span>Pendiente</span>
              </div>
              <div class="submission-date">Fecha límite: ${new Date(task.dueDate).toLocaleDateString("es-ES")}</div>
            </div>
            <div class="submission-grade pending">
              <span>Sin entregar</span>
            </div>
            <div class="submission-actions">
              <button class="submission-action-btn disabled" disabled>
                <span class="material-icons">visibility_off</span>
                Sin entrega
              </button>
            </div>
          `
        }
  
        submissionsList.appendChild(submissionItem)
      })
  
      // Mostrar el modal
      submissionsModal.classList.add("active")
    }
  
    function openStudentSubmissionModal(studentId, taskId) {
      // Buscar la tarea y la entrega
      const task = tasks.find((t) => t.id == taskId)
      const student = students.find((s) => s.id == studentId)
      const taskSubmissions = submissions[taskId] || []
      const submission = taskSubmissions.find((s) => s.studentId == studentId)
  
      if (!task || !student || !submission) return
  
      // Actualizar el contenido del modal
      if (studentSubmissionModal) {
        const modalHeader = studentSubmissionModal.querySelector(".modal-header h3")
        const studentName = studentSubmissionModal.querySelector(".student-profile h4")
        const studentAvatar = studentSubmissionModal.querySelector(".student-avatar.large img")
        const submissionDate = studentSubmissionModal.querySelector(".meta-value")
        const submissionStatus = studentSubmissionModal.querySelector(".submission-status")
        const submissionFiles = studentSubmissionModal.querySelector(".submission-files")
        const studentComments = studentSubmissionModal.querySelector(".comment-text")
        const gradeInput = studentSubmissionModal.querySelector("#grade-points")
        const feedbackInput = studentSubmissionModal.querySelector("#grade-feedback")
        const saveGradeBtn = studentSubmissionModal.querySelector(".save-grade-btn")

        modalHeader.textContent = `Entrega: ${task.title}`
        studentName.textContent = student.name
        studentAvatar.src = student.avatar
        studentAvatar.alt = student.name
  
        // Formatear fecha de entrega
        const submissionDateTime = new Date(submission.submissionDate)
        submissionDate.textContent = `${submissionDateTime.toLocaleDateString("es-ES")}, ${submissionDateTime.toLocaleTimeString("es-ES", { hour: "2-digit", minute: "2-digit" })}`
  
        // Actualizar estado
        if (submission.status === "submitted") {
          submissionStatus.textContent = "A tiempo"
          submissionStatus.className = "submission-status on-time"
        } else if (submission.status === "late") {
          submissionStatus.textContent = "Tardía"
          submissionStatus.className = "submission-status late"
        }
  
        // Actualizar archivos
        submissionFiles.innerHTML = ""
        submission.files.forEach((file) => {
          const fileItem = document.createElement("div")
          fileItem.className = "file-item"
  
          let fileIconClass = ""
          if (file.type === "pdf") {
            fileIconClass = "pdf"
          } else if (file.type === "doc" || file.type === "docx") {
            fileIconClass = "doc"
          } else if (file.type === "jpg" || file.type === "png") {
            fileIconClass = "img"
          }
  
          fileItem.innerHTML = `
            <div class="file-icon ${fileIconClass}">
              <span class="material-icons">picture_as_pdf</span>
            </div>
            <div class="file-details">
              <span class="file-name">${file.name}</span>
              <span class="file-size">${file.size}</span>
            </div>
            <div class="file-actions">
              <button class="file-action-btn download-btn" title="Descargar">
                <span class="material-icons">download</span>
              </button>
              <button class="file-action-btn preview-btn" title="Vista previa">
                <span class="material-icons">visibility</span>
              </button>
            </div>
          `
  
          submissionFiles.appendChild(fileItem)
        })
  
        // Actualizar comentarios
        studentComments.innerHTML = `<p>${submission.comments || "Sin comentarios"}</p>`
  
        // Actualizar calificación
        gradeInput.value = submission.grade || ""
        gradeInput.max = submission.maxGrade
        feedbackInput.value = submission.feedback || ""
  
        // Actualizar botón de guardar
        saveGradeBtn.dataset.studentId = studentId
        saveGradeBtn.dataset.taskId = taskId
  
        studentSubmissionModal.classList.add("active")
      }
    }
  
    function saveGrade(studentId, taskId) {
      // Obtener los valores del formulario
      const gradeInput = document.getElementById("grade-points")
      const feedbackInput = document.getElementById("grade-feedback")
  
      const grade = Number.parseInt(gradeInput.value)
      const feedback = feedbackInput.value
  
      // Validar la calificación
      if (isNaN(grade) || grade < 0 || grade > Number.parseInt(gradeInput.max)) {
        alert(`La calificación debe ser un número entre 0 y ${gradeInput.max}`)
        return
      }
  
      // Actualizar la calificación en los datos
      const taskSubmissions = submissions[taskId] || []
      const submission = taskSubmissions.find((s) => s.studentId == studentId)
  
      if (submission) {
        submission.grade = grade
        submission.feedback = feedback
        submission.isGraded = true
  
        // Mostrar mensaje de éxito
        const successMessage = document.createElement("div")
        successMessage.className = "success-message"
        successMessage.innerHTML = `
          <span class="material-icons">check_circle</span>
          <span>Calificación guardada correctamente</span>
        `
  
        // Agregar el mensaje al modal
        const modalContent = document.querySelector("#student-submission-modal .modal-content")
        modalContent.insertBefore(successMessage, modalContent.firstChild)
  
        // Eliminar el mensaje después de 3 segundos
        setTimeout(() => {
          successMessage.remove()
        }, 3000)
      }
    }
  
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  
    function filterTasks() {
      const searchTerm = searchInput.value.toLowerCase()
      const taskCards = document.querySelectorAll(".task-card")
  
      taskCards.forEach((card) => {
        const title = card.querySelector(".task-title").textContent.toLowerCase()
        const description = card.querySelector(".task-description").textContent.toLowerCase()
  
        if (title.includes(searchTerm) || description.includes(searchTerm)) {
          card.style.display = "block"
        } else {
          card.style.display = "none"
        }
      })
    }
  
    function filterSubmissions() {
      if (!submissionFilter) return
  
      const filterValue = submissionFilter.value
      const submissionItems = document.querySelectorAll(".submission-item")
  
      submissionItems.forEach((item) => {
        if (filterValue === "all") {
          item.style.display = "flex"
        } else if (
          filterValue === "submitted" &&
          (item.classList.contains("submitted") || item.classList.contains("late"))
        ) {
          item.style.display = "flex"
        } else if (filterValue === "late" && item.classList.contains("late")) {
          item.style.display = "flex"
        } else if (filterValue === "pending" && item.classList.contains("pending")) {
          item.style.display = "flex"
        } else if (filterValue === "graded" && !item.querySelector(".submission-grade.pending")) {
          item.style.display = "flex"
        } else {
          item.style.display = "none"
        }
      })
    }
  })
    