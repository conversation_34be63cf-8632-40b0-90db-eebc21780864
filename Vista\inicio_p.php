<?php
require_once '../Controlador/AuthController.php';

// Proteger la página - solo padres
AuthController::proteger<PERSON><PERSON>na(['padre']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información específica del padre
$infoPadre = $usuarioActual['informacion_rol'];
$tipoApoderado = $infoPadre['tipo_apoderado'] ?? 'apoderado';
$tipoApoderadoTexto = ucfirst(str_replace('_', ' ', $tipoApoderado));
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Portal de Padres</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/padres.css">
  <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
  <div class="plataforma-container">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar padre'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                      <?php if ($fotoPerfilUrl): ?>
                          <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                      <p><?php echo htmlspecialchars($tipoApoderadoTexto); ?></p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li class="active">
                          <a href="inicio_p.php">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_p.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li>
                          <a href="anuncios_p.html">
                              <span class="material-icons">campaign</span>
                              <span>Anuncios</span>
                          </a>
                      </li>
                      <li>
                          <a href="calificaciones_hijo.html">
                              <span class="material-icons">grade</span>
                              <span>Calificaciones</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_p.html">
                              <span class="material-icons">chat</span>
                              <span>Chat</span>
                          </a>
                      </li>
                      <li>
                          <a href="cuotas.html">
                              <span class="material-icons">payments</span>
                              <span>Control de pagos</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="../Controlador/AuthController.php?action=logout">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <header class="content-header">
              <div class="header-left">
                  <h1>Portal de Padres</h1>
                  <p class="current-date">Lunes, 22 de marzo de 2025</p>
              </div>
              <div class="header-right">
                  <div class="notifications">
                      <button class="notification-btn">
                          <span class="material-icons">notifications</span>
                          <span class="notification-badge">3</span>
                      </button>
                  </div>
              </div>
          </header>
          
          <div class="content-body">
              <!-- Selector de estudiante -->
              <div class="student-selector">
                  <label for="student-select">Estudiante:</label>
                  <select id="student-select">
                      <option value="1" selected>Juan Pérez González - 5° Primaria A</option>
                      <option value="2">Ana Pérez González - 3° Primaria B</option>
                  </select>
              </div>
              
              <!-- Resumen del estudiante -->
              <section class="dashboard-section">
                  <div class="student-summary">
                      <div class="student-profile">
                          <div class="student-avatar">
                              <img src="/placeholder.svg?height=100&width=100" alt="Foto del estudiante">
                          </div>
                          <div class="student-info">
                              <h3>Juan Pérez González</h3>
                              <p>5° Primaria</p>
                              <p>Año escolar 2025</p>
                          </div>
                      </div>
                      
                      <div class="student-stats">
                          <div class="stat-card">
                              <div class="stat-icon">
                                  <span class="material-icons">grade</span>
                              </div>
                              <div class="stat-info">
                                  <h3>Promedio General</h3>
                                  <div class="stat-value">16.5</div>
                              </div>
                          </div>
                          
                          <div class="stat-card">
                              <div class="stat-icon">
                                  <span class="material-icons">assignment_turned_in</span>
                              </div>
                              <div class="stat-info">
                                  <h3>Tareas Completadas</h3>
                                  <div class="stat-value">85%</div>
                              </div>
                          </div>
                          
                          <div class="stat-card">
                              <div class="stat-icon">
                                  <span class="material-icons">event_available</span>
                              </div>
                              <div class="stat-info">
                                  <h3>Asistencia</h3>
                                  <div class="stat-value">98%</div>
                              </div>
                          </div>
                          
                          <div class="stat-card">
                              <div class="stat-icon">
                                  <span class="material-icons">payments</span>
                              </div>
                              <div class="stat-info">
                                  <h3>Estado de Pagos</h3>
                                  <div class="stat-value">Al día</div>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
              
              <!-- Anuncios recientes -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Anuncios Recientes</h2>
                      <a href="anuncios_p.html" class="view-all">Ver todos</a>
                  </div>
                  
                  <div class="announcements-list">
                      <div class="announcement-item">
                          <div class="announcement-icon">
                              <span class="material-icons">campaign</span>
                          </div>
                          <div class="announcement-content">
                              <h3>Reunión de Padres de Familia</h3>
                              <p>Se convoca a todos los padres de familia a la reunión bimestral que se llevará a cabo el día viernes 28 de marzo a las 18:00 horas en el auditorio de la institución.</p>
                              <div class="announcement-meta">
                                  <span>Publicado: 20/03/2025</span>
                                  <span>Por: Dirección Académica</span>
                              </div>
                          </div>
                      </div>
                      
                      <div class="announcement-item">
                          <div class="announcement-icon">
                              <span class="material-icons">campaign</span>
                          </div>
                          <div class="announcement-content">
                              <h3>Entrega de Libretas de Notas</h3>
                              <p>La entrega de libretas de notas correspondientes al segundo bimestre se realizará el día sábado 29 de marzo de 9:00 a 13:00 horas. Es indispensable estar al día en los pagos.</p>
                              <div class="announcement-meta">
                                  <span>Publicado: 18/03/2025</span>
                                  <span>Por: Secretaría Académica</span>
                              </div>
                          </div>
                      </div>
                      
                      <div class="announcement-item">
                          <div class="announcement-icon">
                              <span class="material-icons">campaign</span>
                          </div>
                          <div class="announcement-content">
                              <h3>Vacunación Escolar</h3>
                              <p>El Ministerio de Salud realizará una campaña de vacunación en nuestra institución el día 2 de abril. Se solicita enviar la autorización firmada antes del 30 de marzo.</p>
                              <div class="announcement-meta">
                                  <span>Publicado: 15/03/2025</span>
                                  <span>Por: Departamento de Salud Escolar</span>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
              
              <!-- Tareas pendientes -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Tareas Pendientes</h2>
                  </div>
                  
                  <div class="tasks-list">
                      <div class="task-item">
                          <div class="task-status pending"></div>
                          <div class="task-content">
                              <h3>Problemas de matemáticas</h3>
                              <p>Resolver los problemas de la página 25 del libro de texto.</p>
                              <div class="task-meta">
                                  <span class="task-course">Matemáticas</span>
                                  <span class="task-date">Fecha límite: 25/03/2025</span>
                              </div>
                          </div>
                      </div>
                      
                      <div class="task-item">
                          <div class="task-status pending"></div>
                          <div class="task-content">
                              <h3>Proyecto de ciencias</h3>
                              <p>Preparar maqueta sobre el sistema solar.</p>
                              <div class="task-meta">
                                  <span class="task-course">Ciencias</span>
                                  <span class="task-date">Fecha límite: 30/03/2025</span>
                              </div>
                          </div>
                      </div>
                      
                      <div class="task-item">
                          <div class="task-status pending"></div>
                          <div class="task-content">
                              <h3>Resumen de lectura</h3>
                              <p>Leer el capítulo 5 del libro "El principito" y hacer un resumen.</p>
                              <div class="task-meta">
                                  <span class="task-course">Lenguaje</span>
                                  <span class="task-date">Fecha límite: 27/03/2025</span>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
          </div>
      </main>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/padres.js"></script>
</body>
</html>