<?php
require_once '../Controlador/AuthController.php';
require_once '../Controlador/PerfilController.php';

// Proteger la página - solo padres
AuthController::proteger<PERSON>agina(['padre']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener datos completos del perfil
$perfilController = new PerfilController();
$datosPerfil = $perfilController->obtenerPerfilCompleto();
$perfil = $datosPerfil['success'] ? $datosPerfil : null;

// Validar que los datos básicos existan
if ($perfil && !isset($perfil['datos_basicos'])) {
    $perfil = null;
}

// Obtener información específica del padre
$infoPadre = $usuarioActual['informacion_rol'];
$tipoApoderado = $infoPadre['tipo_apoderado'] ?? 'apoderado';
$tipoApoderadoTexto = ucfirst(str_replace('_', ' ', $tipoApoderado));

// Obtener información de los hijos
$hijos = [];
if ($perfil) {
    try {
        $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno 
                FROM estudiantes e 
                INNER JOIN personas p ON e.persona_id = p.id 
                INNER JOIN padre_estudiante pe ON e.id = pe.estudiante_id 
                INNER JOIN padres pa ON pe.padre_id = pa.id 
                INNER JOIN personas pp ON pa.persona_id = pp.id 
                WHERE pp.usuario_id = :usuario_id";
        
        $stmt = $perfilController->usuario->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioActual['id']);
        $stmt->execute();
        $hijos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error obteniendo hijos: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Perfil de Padre</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/perfil.css">
    <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar padre'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                        <?php if ($fotoPerfilUrl): ?>
                            <img src="../Controlador/ImagenController.php?usuario_id=<?php echo $usuarioActual['id']; ?>" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                        <p><?php echo htmlspecialchars($tipoApoderadoTexto); ?></p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_p.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="perfil_p.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="anuncios_p.html">
                                <span class="material-icons">campaign</span>
                                <span>Anuncios</span>
                            </a>
                        </li>
                        <li>
                            <a href="calificaciones_hijo.html">
                                <span class="material-icons">grade</span>
                                <span>Calificaciones</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_p.html">
                                <span class="material-icons">chat</span>
                                <span>Chat</span>
                            </a>
                        </li>
                        <li>
                            <a href="cuotas.html">
                                <span class="material-icons">payments</span>
                                <span>Control de pagos</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../Controlador/AuthController.php?action=logout">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mi Perfil</h1>
                    <p class="current-date"><?php echo date('l, j \d\e F \d\e Y'); ?></p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <div class="profile-container">
                    <!-- Sección de perfil principal -->
                    <section class="profile-main">
                        <div class="profile-header">
                            <div class="profile-avatar-container">
                                <div class="profile-avatar">
                                    <?php if ($fotoPerfilUrl): ?>
                                        <img src="../Controlador/ImagenController.php?usuario_id=<?php echo $usuarioActual['id']; ?>" alt="Foto de perfil">
                                    <?php else: ?>
                                        <div class="default-avatar padre" data-initials="<?php echo htmlspecialchars($iniciales); ?>"></div>
                                    <?php endif; ?>
                                </div>
                                <button class="change-avatar-btn" id="change-avatar-btn">
                                    <span class="material-icons">photo_camera</span>
                                    <span>Cambiar foto</span>
                                </button>
                                <input type="file" id="foto-input" accept="image/*" style="display: none;">
                            </div>
                            <div class="profile-info">
                                <h2><?php echo htmlspecialchars($nombreCompleto); ?></h2>
                                <p class="profile-role"><?php echo htmlspecialchars($tipoApoderadoTexto); ?> de familia</p>
                                <?php if (!empty($hijos)): ?>
                                    <p class="profile-grade">
                                        <?php 
                                        $hijosTexto = [];
                                        foreach ($hijos as $hijo) {
                                            $nombreHijo = $hijo['nombres'] . ' ' . $hijo['apellido_paterno'];
                                            $grado = ucfirst(str_replace('-', ' ', $hijo['grado_actual']));
                                            $hijosTexto[] = $nombreHijo . ' (' . $grado . ')';
                                        }
                                        echo 'Padre de: ' . implode(' y ', $hijosTexto);
                                        ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de información personal -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">person</span>
                                Información Personal
                            </h3>
                            <button class="edit-section-btn" data-section="personal">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre completo</div>
                                    <div class="info-value"><?php echo htmlspecialchars($nombreCompleto); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Fecha de nacimiento</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['datos_basicos']['fecha_nacimiento']) {
                                            echo date('d/m/Y', strtotime($perfil['datos_basicos']['fecha_nacimiento']));
                                        } else {
                                            echo 'No especificada';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Edad</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['edad']) {
                                            echo $perfil['edad'] . ' años';
                                        } else {
                                            echo 'No especificada';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Sexo</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['datos_basicos']['sexo']) {
                                            echo ucfirst($perfil['datos_basicos']['sexo']);
                                        } else {
                                            echo 'No especificado';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Dirección</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['datos_basicos']['direccion']) {
                                            echo htmlspecialchars($perfil['datos_basicos']['direccion']);
                                        } else {
                                            echo 'No especificada';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['datos_basicos']['telefono']) {
                                            echo htmlspecialchars($perfil['datos_basicos']['telefono']);
                                        } else {
                                            echo 'No especificado';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">DNI</div>
                                    <div class="info-value">
                                        <?php 
                                        if ($perfil && $perfil['datos_basicos']['dni']) {
                                            echo htmlspecialchars($perfil['datos_basicos']['dni']);
                                        } else {
                                            echo 'No especificado';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de cuenta -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">account_circle</span>
                                Información de Cuenta
                            </h3>
                            <button class="edit-section-btn" data-section="account">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre de usuario</div>
                                    <div class="info-value"><?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Correo electrónico</div>
                                    <div class="info-value"><?php echo htmlspecialchars($usuarioActual['email']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Rol</div>
                                    <div class="info-value"><?php echo htmlspecialchars($tipoApoderadoTexto); ?></div>
                                </div>
                                <?php if ($perfil && isset($perfil['informacion_rol']['tipo_apoderado'])): ?>
                                <div class="info-item">
                                    <div class="info-label">Tipo de apoderado</div>
                                    <div class="info-value"><?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $perfil['informacion_rol']['tipo_apoderado']))); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>

                    <!-- Sección de hijos -->
                    <?php if (!empty($hijos)): ?>
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">family_restroom</span>
                                Mis Hijos
                            </h3>
                        </div>
                        
                        <div class="section-content">
                            <div class="hijos-grid">
                                <?php foreach ($hijos as $hijo): ?>
                                <div class="hijo-card">
                                    <div class="hijo-info">
                                        <h4><?php echo htmlspecialchars($hijo['nombres'] . ' ' . $hijo['apellido_paterno']); ?></h4>
                                        <p><?php echo ucfirst(str_replace('-', ' ', $hijo['grado_actual'])); ?></p>
                                        <p>Año escolar: <?php echo $hijo['anio_escolar']; ?></p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </section>
                    <?php endif; ?>
                    
                    <!-- Sección de seguridad -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">security</span>
                                Seguridad
                            </h3>
                        </div>
                        
                        <div class="section-content">
                            <div class="security-actions">
                                <button class="change-password-btn">
                                    <span class="material-icons">lock</span>
                                    <span>Cambiar contraseña</span>
                                </button>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal para editar información personal -->
    <div class="modal-overlay" id="edit-personal-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información Personal</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form class="edit-form" id="personal-form">
                <div class="form-group">
                    <label for="nombres">Nombres</label>
                    <input type="text" id="nombres" name="nombres" value="<?php echo htmlspecialchars($perfil['datos_basicos']['nombres'] ?? ''); ?>" required>
                </div>
                <div class="form-group">
                    <label for="apellido_paterno">Apellido Paterno</label>
                    <input type="text" id="apellido_paterno" name="apellido_paterno" value="<?php echo htmlspecialchars($perfil['datos_basicos']['apellido_paterno'] ?? ''); ?>" required>
                </div>
                <div class="form-group">
                    <label for="apellido_materno">Apellido Materno</label>
                    <input type="text" id="apellido_materno" name="apellido_materno" value="<?php echo htmlspecialchars($perfil['datos_basicos']['apellido_materno'] ?? ''); ?>" required>
                </div>
                <div class="form-group">
                    <label for="dni">DNI</label>
                    <input type="text" id="dni" name="dni" value="<?php echo htmlspecialchars($perfil['datos_basicos']['dni'] ?? ''); ?>" maxlength="8">
                </div>
                <div class="form-group">
                    <label for="fecha_nacimiento">Fecha de Nacimiento</label>
                    <input type="date" id="fecha_nacimiento" name="fecha_nacimiento" value="<?php echo $perfil['datos_basicos']['fecha_nacimiento'] ?? ''; ?>">
                </div>
                <div class="form-group">
                    <label for="sexo">Sexo</label>
                    <select id="sexo" name="sexo">
                        <option value="">Seleccionar</option>
                        <option value="masculino" <?php echo ($perfil['datos_basicos']['sexo'] ?? '') === 'masculino' ? 'selected' : ''; ?>>Masculino</option>
                        <option value="femenino" <?php echo ($perfil['datos_basicos']['sexo'] ?? '') === 'femenino' ? 'selected' : ''; ?>>Femenino</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="direccion">Dirección</label>
                    <textarea id="direccion" name="direccion"><?php echo htmlspecialchars($perfil['datos_basicos']['direccion'] ?? ''); ?></textarea>
                </div>
                <div class="form-group">
                    <label for="telefono">Teléfono</label>
                    <input type="tel" id="telefono" name="telefono" value="<?php echo htmlspecialchars($perfil['datos_basicos']['telefono'] ?? ''); ?>">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="submit" class="btn-primary">Guardar Cambios</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para editar información de cuenta -->
    <div class="modal-overlay" id="edit-account-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información de Cuenta</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form class="edit-form" id="account-form">
                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($usuarioActual['email']); ?>" required>
                </div>
                <?php if ($perfil && isset($perfil['informacion_rol']['tipo_apoderado'])): ?>
                <div class="form-group">
                    <label for="tipo_apoderado">Tipo de Apoderado</label>
                    <select id="tipo_apoderado" name="tipo_apoderado">
                        <option value="padre" <?php echo ($perfil['informacion_rol']['tipo_apoderado'] ?? '') === 'padre' ? 'selected' : ''; ?>>Padre</option>
                        <option value="madre" <?php echo ($perfil['informacion_rol']['tipo_apoderado'] ?? '') === 'madre' ? 'selected' : ''; ?>>Madre</option>
                        <option value="tutor legal" <?php echo ($perfil['informacion_rol']['tipo_apoderado'] ?? '') === 'tutor legal' ? 'selected' : ''; ?>>Tutor Legal</option>
                    </select>
                </div>
                <?php endif; ?>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="submit" class="btn-primary">Guardar Cambios</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para cambiar contraseña -->
    <div class="modal-overlay" id="change-password-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cambiar Contraseña</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <form class="edit-form" id="password-form">
                <div class="form-group">
                    <label for="password_actual">Contraseña Actual</label>
                    <input type="password" id="password_actual" name="password_actual" required>
                </div>
                <div class="form-group">
                    <label for="password_nuevo">Nueva Contraseña</label>
                    <input type="password" id="password_nuevo" name="password_nuevo" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="password_confirmar">Confirmar Nueva Contraseña</label>
                    <input type="password" id="password_confirmar" name="password_confirmar" required minlength="6">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="submit" class="btn-primary">Cambiar Contraseña</button>
                </div>
            </form>
        </div>
    </div>

    <script src="./Js/perfil.js"></script>
</body>
</html>
