/* Estilos para la página de Más Información */

/* Hero Section */
.info-hero {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .info-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .info-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
  }
  
  /* Secciones generales */
  section {
    padding: 5rem 0;
  }
  
  section h2 {
    font-size: 2rem;
    color: var(--secondary-color);
    text-align: center;
    margin-bottom: 1rem;
  }
  
  .seccion-descripcion {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
  }
  
  /* Enlaces de Contacto */
  .enlaces-contacto {
    background-color: #f9f9f9;
  }
  
  .enlaces-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .enlace-card {
    background-color: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
  }
  
  .enlace-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  }
  
  .enlace-imagen {
    height: 200px;
    overflow: hidden;
  }
  
  .enlace-imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
  }
  
  .enlace-card:hover .enlace-imagen img {
    transform: scale(1.05);
  }
  
  .enlace-info {
    padding: 1.5rem;
  }
  
  .enlace-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.8rem;
    color: var(--secondary-color);
  }
  
  .enlace-info p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }
  
  .enlace-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: gap 0.3s;
  }
  
  .enlace-card:hover .enlace-btn {
    gap: 0.8rem;
  }
  
  /* Estilos específicos para cada tipo de enlace */
  .whatsapp .enlace-btn {
    color: #25d366;
  }
  
  .facebook .enlace-btn {
    color: #1877f2;
  }
  
  .telefono .enlace-btn {
    color: #ff9800;
  }
  
  .email .enlace-btn {
    color: #ea4335;
  }
  
  /* Preguntas Frecuentes */
  .preguntas-frecuentes {
    background-color: var(--white);
  }
  
  .accordion {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .accordion-item {
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    background-color: var(--white);
  }
  
  .accordion-header {
    padding: 1.5rem;
    background-color: #f8f8f8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .accordion-header:hover {
    background-color: #f0f0f0;
  }
  
  .accordion-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--secondary-color);
  }
  
  .accordion-header .material-icons {
    color: var(--primary-color);
    transition: transform 0.3s;
  }
  
  .accordion-item.active .accordion-header .material-icons {
    transform: rotate(180deg);
  }
  
  .accordion-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
  }
  
  .accordion-item.active .accordion-content {
    padding: 1.5rem;
    max-height: 500px;
  }
  
  .accordion-content p {
    margin: 0;
    line-height: 1.6;
    color: #555;
  }
  
  /* Responsive Styles */
  @media (max-width: 768px) {
    .info-hero {
      padding: 100px 0 40px;
    }
  
    .info-hero h1 {
      font-size: 2rem;
    }
  
    section {
      padding: 3rem 0;
    }
  
    .enlaces-grid {
      grid-template-columns: 1fr;
    }
  
    .accordion-header {
      padding: 1.2rem;
    }
  
    .accordion-item.active .accordion-content {
      padding: 1.2rem;
    }
  }
    