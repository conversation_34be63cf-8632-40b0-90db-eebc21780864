# Resumen: Implementación de Autenticación en Vistas de Tareas

## Problema Identificado

Las vistas de tareas (`tareas_m.php`, `tareas_crud.php`, `tareas_menu.php`) no estaban reconociendo que había un usuario maestro conectado, a diferencia de `contenido_m.php` que sí tenía implementada la lógica de autenticación y verificación de sesión.

## Solución Implementada

Se agregó la lógica de autenticación y verificación de sesión a las tres vistas de tareas, siguiendo el mismo patrón que `contenido_m.php`.

### Cambios Realizados

#### 1. **Vista/tareas_m.php**
- ✅ Agregada verificación de sesión de maestro
- ✅ Verificación de parámetro `curso_id` en URL
- ✅ Obtención de información del curso y maestro
- ✅ Validación de propiedad del curso
- ✅ Datos dinámicos del curso en el header
- ✅ Enlaces actualizados con `curso_id`
- ✅ Datos del maestro en el sidebar
- ✅ Variables JavaScript para el frontend

#### 2. **Vista/tareas_crud.php**
- ✅ Agregada verificación de sesión de maestro
- ✅ Verificación de parámetro `curso_id` en URL
- ✅ Obtención de información del curso y maestro
- ✅ Validación de propiedad del curso
- ✅ Datos dinámicos del curso en el header
- ✅ Enlaces actualizados con `curso_id`
- ✅ Datos del maestro en el sidebar
- ✅ Variables JavaScript para el frontend

#### 3. **Vista/tareas_menu.php**
- ✅ Agregada verificación de sesión de maestro
- ✅ Verificación de parámetro `curso_id` en URL
- ✅ Obtención de información del curso y maestro
- ✅ Validación de propiedad del curso
- ✅ Datos dinámicos del curso en el header
- ✅ Enlaces actualizados con `curso_id`
- ✅ Datos del maestro en el sidebar
- ✅ Variables JavaScript para el frontend

### Código de Autenticación Implementado

```php
<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

// Verificar que se proporcione el ID del curso
if (!isset($_GET['curso_id']) || empty($_GET['curso_id'])) {
    header('Location: cursos_m.php');
    exit;
}

$cursoId = (int)$_GET['curso_id'];
$maestroId = $_SESSION['usuario_id'];

require_once '../Controlador/CursoController.php';

$controller = new CursoController();

// Obtener información del curso
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados para mostrar el nombre del grado
$grados = $controller->obtenerGrados();
?>
```

### Características de Seguridad Implementadas

1. **Verificación de Sesión**: Solo usuarios logueados con rol 'maestro' pueden acceder
2. **Validación de Curso**: Verificación de que el curso pertenece al maestro
3. **Parámetros Requeridos**: Validación de parámetros obligatorios en URL
4. **Redirección Segura**: Redirección a páginas apropiadas en caso de error
5. **Datos Dinámicos**: Información real del curso y maestro en lugar de datos estáticos

### Datos Dinámicos Implementados

- **Header del Curso**: Nombre, grado, horarios reales
- **Sidebar**: Nombre y foto del maestro real
- **Navegación**: Enlaces con `curso_id` correcto
- **JavaScript**: Variables con datos del curso y maestro

### Archivos Modificados

1. `Vista/tareas_m.php` - Vista estándar de tareas
2. `Vista/tareas_crud.php` - Vista CRUD de tareas
3. `Vista/tareas_menu.php` - Menú de selección de vistas
4. `test_autenticacion_tareas.php` - Script de prueba (nuevo)

### Archivos de Soporte

- `Controlador/TareaController.php` - Controlador de tareas
- `api_tareas.php` - API REST para tareas
- `Vista/Js/tareas_m.js` - JavaScript para vista estándar
- `Vista/Js/tareas_crud.js` - JavaScript para vista CRUD
- `Vista/Css/tareas_m.css` - Estilos para vista estándar
- `Vista/Css/tareas_crud.css` - Estilos para vista CRUD

## Resultado

Ahora las tres vistas de tareas:

1. ✅ **Reconocen al usuario maestro conectado**
2. ✅ **Muestran información real del curso**
3. ✅ **Validan la propiedad del curso**
4. ✅ **Tienen navegación funcional**
5. ✅ **Mantienen la seguridad de la aplicación**
6. ✅ **Funcionan de manera consistente con el resto del sistema**

## Pruebas

Para probar la funcionalidad:

1. Ejecutar `test_autenticacion_tareas.php` para crear datos de prueba
2. Acceder a las vistas con el parámetro `curso_id` correcto
3. Verificar que se muestre la información del maestro y curso
4. Comprobar que la navegación funcione correctamente

## URLs de Acceso

- `Vista/tareas_menu.php?curso_id={ID_CURSO}`
- `Vista/tareas_m.php?curso_id={ID_CURSO}`
- `Vista/tareas_crud.php?curso_id={ID_CURSO}`

Donde `{ID_CURSO}` debe ser el ID de un curso que pertenezca al maestro logueado. 