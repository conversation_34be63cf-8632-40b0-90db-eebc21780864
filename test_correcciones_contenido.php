<?php
// Script de prueba para verificar las correcciones del contenido
session_start();

// Simular sesión de maestro para pruebas
$_SESSION['usuario_id'] = 2; // ID del maestro Juan <PERSON>
$_SESSION['rol'] = 'maestro';

echo "<h1>Pruebas de Correcciones de Contenido</h1>";

// Incluir el controlador
require_once 'Controlador/ContenidoController.php';

$controller = new ContenidoController();

echo "<h2>1. Verificar acceso a curso</h2>";
$cursoId = 1; // Asumiendo que existe un curso con ID 1
$acceso = $controller->verificarAccesoMaestro($cursoId, $_SESSION['usuario_id']);
echo "Acceso al curso $cursoId: " . ($acceso ? "SÍ" : "NO") . "<br>";

echo "<h2>2. Obtener semanas del curso</h2>";
$semanas = $controller->obtenerSemanasPorCurso($cursoId);
echo "Semanas encontradas: " . count($semanas) . "<br>";
foreach ($semanas as $semana) {
    echo "- Semana {$semana['numero_semana']}: {$semana['titulo']} (ID: {$semana['id']})<br>";
}

echo "<h2>3. Obtener contenido del curso</h2>";
$contenido = $controller->obtenerContenidoPorCurso($cursoId);
echo "Contenido encontrado: " . count($contenido) . "<br>";
foreach ($contenido as $item) {
    echo "- {$item['titulo']} (Tipo: {$item['tipo']}, ID: {$item['id']})<br>";
}

echo "<h2>4. Probar obtención de contenido por ID</h2>";
if (!empty($contenido)) {
    $primerContenido = $contenido[0];
    $contenidoPorId = $controller->obtenerContenidoPorId($primerContenido['id']);
    if ($contenidoPorId) {
        echo "Contenido obtenido por ID {$primerContenido['id']}: {$contenidoPorId['titulo']}<br>";
        echo "Descripción: " . substr($contenidoPorId['descripcion'], 0, 100) . "...<br>";
    } else {
        echo "No se pudo obtener el contenido por ID<br>";
    }
}

echo "<h2>5. Probar obtención de semana por ID</h2>";
if (!empty($semanas)) {
    $primerSemana = $semanas[0];
    $semanaPorId = $controller->obtenerSemanaPorId($primerSemana['id']);
    if ($semanaPorId) {
        echo "Semana obtenida por ID {$primerSemana['id']}: {$semanaPorId['titulo']}<br>";
        echo "Fechas: {$semanaPorId['fecha_inicio']} - {$semanaPorId['fecha_fin']}<br>";
    } else {
        echo "No se pudo obtener la semana por ID<br>";
    }
}

echo "<h2>6. Verificar sesiones duplicadas</h2>";
try {
    require_once 'Modelo/Conexion.php';
    $conexion = Conexion::getConexion();
    
    $query = "SELECT semana_id, curso_id, COUNT(*) as cantidad
              FROM sesiones 
              WHERE activo = 1
              GROUP BY semana_id, curso_id
              HAVING COUNT(*) > 1";
    $stmt = $conexion->prepare($query);
    $stmt->execute();
    $duplicadas = $stmt->fetchAll();
    
    if (empty($duplicadas)) {
        echo "✅ No hay sesiones duplicadas<br>";
    } else {
        echo "❌ Se encontraron sesiones duplicadas:<br>";
        foreach ($duplicadas as $dup) {
            echo "- Semana {$dup['semana_id']}, Curso {$dup['curso_id']}: {$dup['cantidad']} sesiones<br>";
        }
    }
} catch (Exception $e) {
    echo "Error al verificar sesiones: " . $e->getMessage() . "<br>";
}

echo "<h2>7. Mostrar todas las sesiones activas</h2>";
try {
    $query = "SELECT id, semana_id, curso_id, titulo, orden, activo, created_at
              FROM sesiones 
              WHERE activo = 1
              ORDER BY semana_id, curso_id, id";
    $stmt = $conexion->prepare($query);
    $stmt->execute();
    $sesiones = $stmt->fetchAll();
    
    echo "Sesiones activas: " . count($sesiones) . "<br>";
    foreach ($sesiones as $sesion) {
        echo "- ID: {$sesion['id']}, Semana: {$sesion['semana_id']}, Curso: {$sesion['curso_id']}, Título: {$sesion['titulo']}<br>";
    }
} catch (Exception $e) {
    echo "Error al mostrar sesiones: " . $e->getMessage() . "<br>";
}

echo "<h2>Pruebas completadas</h2>";
echo "<p>Si todas las pruebas pasan correctamente, las correcciones están funcionando.</p>";
?> 