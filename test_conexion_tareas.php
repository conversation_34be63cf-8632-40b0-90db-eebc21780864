<?php
// Script de prueba para verificar la conexión y el TareaController
require_once 'config.php';
require_once 'Modelo/Conexion.php';
require_once 'Controlador/TareaController.php';

echo "<h2>Prueba de Conexión y TareaController</h2>";

try {
    // Probar conexión
    echo "<h3>1. Probando conexión a la base de datos...</h3>";
    $conexion = Conexion::getConexion();
    echo "✅ Conexión exitosa a la base de datos<br>";
    
    // Probar TareaController
    echo "<h3>2. Probando TareaController...</h3>";
    $tareaController = new TareaController();
    echo "✅ TareaController creado exitosamente<br>";
    
    // Probar obtener semanas académicas (usando un curso_id de ejemplo)
    echo "<h3>3. Probando obtenerSemanasAcademicas...</h3>";
    $semanas = $tareaController->obtenerSemanasAcademicas(1); // Usando curso_id = 1 como ejemplo
    if ($semanas !== false) {
        echo "✅ obtenerSemanasAcademicas funcionó correctamente<br>";
        echo "Semanas encontradas: " . count($semanas) . "<br>";
        if (count($semanas) > 0) {
            echo "Primera semana: " . $semanas[0]['titulo'] . "<br>";
        }
    } else {
        echo "❌ Error en obtenerSemanasAcademicas<br>";
    }
    
    // Probar obtener tareas por curso
    echo "<h3>4. Probando obtenerTareasPorCurso...</h3>";
    $tareas = $tareaController->obtenerTareasPorCurso(1); // Usando curso_id = 1 como ejemplo
    if ($tareas !== false) {
        echo "✅ obtenerTareasPorCurso funcionó correctamente<br>";
        echo "Tareas encontradas: " . count($tareas) . "<br>";
    } else {
        echo "❌ Error en obtenerTareasPorCurso<br>";
    }
    
    echo "<h3>5. Prueba completada</h3>";
    echo "Si todos los pasos anteriores muestran ✅, la conexión está funcionando correctamente.";
    
} catch (Exception $e) {
    echo "<h3>❌ Error encontrado:</h3>";
    echo "Mensaje: " . $e->getMessage() . "<br>";
    echo "Archivo: " . $e->getFile() . "<br>";
    echo "Línea: " . $e->getLine() . "<br>";
}
?> 