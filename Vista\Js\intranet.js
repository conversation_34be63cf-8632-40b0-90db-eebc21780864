// JavaScript para la página de login
// El formulario ahora se envía directamente al servidor PHP

document.addEventListener("DOMContentLoaded", () => {
    // Agregar animación de carga opcional (sin interferir con el envío)
    const loginBtn = document.querySelector(".login-btn");

    if (loginBtn) {
        loginBtn.addEventListener("click", () => {
            // Pequeña animación visual mientras se procesa
            loginBtn.style.opacity = "0.8";
            setTimeout(() => {
                if (loginBtn) {
                    loginBtn.style.opacity = "1";
                }
            }, 200);
        });
    }
  
    // Añadimos una clase CSS para la animación de rotación
    const style = document.createElement("style")
    style.textContent = `
          .rotating {
              animation: rotate 1.5s linear infinite;
          }
          
          @keyframes rotate {
              from {
                  transform: rotate(0deg);
              }
              to {
                  transform: rotate(360deg);
              }
          }
      `
    document.head.appendChild(style)
  })
    