document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn")
    const changePasswordBtn = document.querySelector(".change-password-btn")
    const changeAvatarBtn = document.querySelector(".change-avatar-btn")
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn")
    const modals = document.querySelectorAll(".modal-overlay")
    const fotoInput = document.getElementById("foto-input")
  
    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const section = button.getAttribute("data-section")
        const modal = document.getElementById(`edit-${section}-modal`)
        if (modal) {
          modal.classList.add("active")
        }
      })
    })
  
    // Mostrar modal de cambio de contraseña
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener("click", () => {
        const modal = document.getElementById("change-password-modal")
        if (modal) {
          modal.classList.add("active")
        }
      })
    }
  
    // Manejar cambio de avatar
    if (changeAvatarBtn) {
      changeAvatarBtn.addEventListener("click", () => {
        fotoInput.click()
      })
    }

    // Manejar selección de archivo
    if (fotoInput) {
      fotoInput.addEventListener("change", (e) => {
        if (e.target.files && e.target.files[0]) {
          subirFotoPerfil(e.target.files[0])
        }
      })
    }
  
    // Cerrar modales con botones de cierre
    modalCloseButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
  
    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })
  
    // Manejar envío de formularios
    const forms = document.querySelectorAll(".edit-form")
    forms.forEach((form) => {
      form.addEventListener("submit", (e) => {
        e.preventDefault()
        const formId = form.id
        
        switch (formId) {
          case 'personal-form':
            actualizarInformacionPersonal(form)
            break
          case 'account-form':
            actualizarInformacionCuenta(form)
            break
          case 'password-form':
            cambiarContraseña(form)
            break
        }
      })
    })

    // Función para subir foto de perfil
    async function subirFotoPerfil(archivo) {
      const formData = new FormData()
      formData.append('foto', archivo)

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=subir_foto', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Foto de perfil actualizada correctamente', 'success')
          // Recargar la página para mostrar la nueva foto
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al subir la foto', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al subir la foto', 'error')
      }
    }

    // Función para actualizar información personal
    async function actualizarInformacionPersonal(form) {
      const formData = new FormData(form)

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=actualizar', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Información personal actualizada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          // Recargar la página para mostrar los cambios
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al actualizar la información', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al actualizar la información', 'error')
      }
    }

    // Función para actualizar información de cuenta
    async function actualizarInformacionCuenta(form) {
      const formData = new FormData(form)

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=actualizar', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Información de cuenta actualizada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          // Recargar la página para mostrar los cambios
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al actualizar la información', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al actualizar la información', 'error')
      }
    }

    // Función para cambiar contraseña
    async function cambiarContraseña(form) {
      const formData = new FormData(form)
      const passwordNuevo = formData.get('password_nuevo')
      const passwordConfirmar = formData.get('password_confirmar')

      // Validar que las contraseñas coincidan
      if (passwordNuevo !== passwordConfirmar) {
        mostrarNotificacion('Las contraseñas nuevas no coinciden', 'error')
        return
      }

      // Validar longitud mínima
      if (passwordNuevo.length < 6) {
        mostrarNotificacion('La contraseña debe tener al menos 6 caracteres', 'error')
        return
      }

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=cambiar_password', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Contraseña cambiada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          form.reset()
        } else {
          mostrarNotificacion(result.error || 'Error al cambiar la contraseña', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al cambiar la contraseña', 'error')
      }
    }

    // Función para cerrar modal
    function cerrarModal(modal) {
      if (modal) {
        modal.classList.remove("active")
      }
    }

    // Función para mostrar notificaciones
    function mostrarNotificacion(mensaje, tipo) {
      // Crear elemento de notificación
      const notificacion = document.createElement('div')
      notificacion.className = `notificacion ${tipo}`
      notificacion.innerHTML = `
        <span class="notificacion-mensaje">${mensaje}</span>
        <button class="notificacion-cerrar" onclick="this.parentElement.remove()">
          <span class="material-icons">close</span>
        </button>
      `

      // Agregar estilos CSS dinámicamente si no existen
      if (!document.getElementById('notificacion-styles')) {
        const styles = document.createElement('style')
        styles.id = 'notificacion-styles'
        styles.textContent = `
          .notificacion {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
          }
          .notificacion.success {
            background-color: #4caf50;
          }
          .notificacion.error {
            background-color: #f44336;
          }
          .notificacion-cerrar {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0;
            margin-left: auto;
          }
          .notificacion-mensaje {
            flex: 1;
          }
          @keyframes slideIn {
            from {
              transform: translateX(100%);
              opacity: 0;
            }
            to {
              transform: translateX(0);
              opacity: 1;
            }
          }
        `
        document.head.appendChild(styles)
      }

      // Agregar notificación al DOM
      document.body.appendChild(notificacion)

      // Remover automáticamente después de 5 segundos
      setTimeout(() => {
        if (notificacion.parentElement) {
          notificacion.remove()
        }
      }, 5000)
    }
  })
    