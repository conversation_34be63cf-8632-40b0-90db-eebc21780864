<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

// Verificar que se proporcione el ID del curso
if (!isset($_GET['curso_id']) || empty($_GET['curso_id'])) {
    header('Location: cursos_m.php');
    exit;
}

$cursoId = (int)$_GET['curso_id'];
$maestroId = $_SESSION['usuario_id'];

require_once '../Controlador/CursoController.php';

$controller = new CursoController();

// Obtener información del curso
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados para mostrar el nombre del grado
$grados = $controller->obtenerGrados();

// Obtener semanas académicas del curso
$querySemanas = "SELECT * FROM semanas_academicas WHERE curso_id = ? AND activo = 1 ORDER BY numero_semana";
$stmtSemanas = $conexion->prepare($querySemanas);
$stmtSemanas->execute([$cursoId]);
$semanas = $stmtSemanas->fetchAll();

// Obtener contenido del curso (consultas simplificadas)
$contenido = [];
$videoconferencias = [];

// Solo obtener contenido si hay semanas académicas
if (!empty($semanas)) {
    $queryContenido = "SELECT c.*, sa.numero_semana, sa.titulo as semana_titulo 
                       FROM contenido c 
                       JOIN sesiones s ON c.sesion_id = s.id 
                       JOIN semanas_academicas sa ON s.semana_id = sa.id 
                       WHERE s.curso_id = ? AND c.activo = 1 
                       ORDER BY sa.numero_semana, s.orden, c.created_at";
    $stmtContenido = $conexion->prepare($queryContenido);
    $stmtContenido->execute([$cursoId]);
    $contenido = $stmtContenido->fetchAll();
}

// Obtener videoconferencias activas
$queryVideoconferencias = "SELECT * FROM enlaces_videoconferencia 
                           WHERE curso_id = ? AND activo = 1 
                           ORDER BY fecha DESC, created_at DESC";
$stmtVideoconferencias = $conexion->prepare($queryVideoconferencias);
$stmtVideoconferencias->execute([$cursoId]);
$videoconferencias = $stmtVideoconferencias->fetchAll();

// Organizar contenido por semanas
$contenidoPorSemana = [];
foreach ($contenido as $item) {
    $semanaId = $item['numero_semana'];
    if (!isset($contenidoPorSemana[$semanaId])) {
        $contenidoPorSemana[$semanaId] = [];
    }
    $contenidoPorSemana[$semanaId][] = $item;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Contenido del Curso</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/contenido_m.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php if ($maestro && $maestro['foto_perfil']): ?>
                            <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                        <?php else: ?>
                            <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.php">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.php">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #2196f3;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_m.php" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1><?php echo htmlspecialchars($curso['nombre']); ?></h1>
                            <p><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <?php if (!empty($curso['horarios_procesados'])): ?>
                                <?php 
                                $dias = [
                                    'lunes' => 'Lunes',
                                    'martes' => 'Martes',
                                    'miercoles' => 'Miércoles',
                                    'jueves' => 'Jueves',
                                    'viernes' => 'Viernes'
                                ];
                                $contador = 0;
                                foreach ($curso['horarios_procesados'] as $dia => $horario): 
                                    if ($contador < 2): // Mostrar solo los primeros 2 horarios
                                ?>
                                    <div class="schedule-day">
                                        <span class="day-label"><?php echo $dias[$dia]; ?></span>
                                        <span class="day-time"><?php echo htmlspecialchars($horario); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                    $contador++;
                                endforeach; 
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab active">Contenido</a>
                    <a href="tareas_menu.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Tareas</a>
                    <a href="estudiantes_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Estudiantes</a>
                    <a href="calificaciones_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Calificaciones</a>
                    <a href="asistencias_estudiantes.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Asistencia</a>
                    <a href="mensajes_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de videoconferencia -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Videoconferencia</h2>
                        <div class="section-actions">
                            <?php if (empty($videoconferencias)): ?>
                                <button class="action-btn create-meet-btn">
                                    <span class="material-icons">add</span>
                                    Agregar enlace de videoconferencia
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!empty($videoconferencias)): ?>
                        <?php foreach ($videoconferencias as $videoconferencia): ?>
                            <div class="meet-section active-meet">
                                <div class="meet-info">
                                    <div class="meet-icon">
                                        <span class="material-icons">videocam</span>
                                    </div>
                                    <div class="meet-details">
                                        <h3><?php echo htmlspecialchars($videoconferencia['titulo']); ?></h3>
                                        <p><?php echo date('d/m/Y', strtotime($videoconferencia['fecha'])) . ' – ' . date('H:i', strtotime($videoconferencia['hora'])); ?></p>
                                        <div class="meet-actions">
                                            <a href="<?php echo htmlspecialchars($videoconferencia['url']); ?>" class="meet-link" target="_blank">
                                                <span class="material-icons">open_in_new</span>
                                                Unirse a la videoconferencia
                                            </a>
                                            <button class="meet-action-btn edit-btn" data-id="<?php echo $videoconferencia['id']; ?>">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="meet-action-btn delete-btn" data-id="<?php echo $videoconferencia['id']; ?>">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="meet-section no-meet">
                            <div class="meet-info">
                                <div class="meet-icon">
                                    <span class="material-icons">videocam_off</span>
                                </div>
                                <div class="meet-details">
                                    <h3>No hay videoconferencias programadas</h3>
                                    <p>Agrega una videoconferencia para comenzar</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </section>
                
                <!-- Contenido del curso -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Contenido del curso</h2>
                        <div class="section-actions">
                            <button class="action-btn create-folder-btn">
                                <span class="material-icons">create_new_folder</span>
                                Nueva carpeta
                            </button>
                            <button class="action-btn create-content-btn">
                                <span class="material-icons">add</span>
                                Nuevo contenido
                            </button>
                        </div>
                    </div>
                    
                    <div class="course-content">
                        <?php if (!empty($semanas)): ?>
                            <?php foreach ($semanas as $semana): ?>
                                <div class="week-container">
                                    <div class="week-header" data-week="<?php echo $semana['numero_semana']; ?>">
                                        <div class="week-title">
                                            <h3>Semana <?php echo $semana['numero_semana']; ?>: <?php echo htmlspecialchars($semana['titulo']); ?></h3>
                                            <span class="week-dates"><?php echo date('d/m/Y', strtotime($semana['fecha_inicio'])); ?> - <?php echo date('d/m/Y', strtotime($semana['fecha_fin'])); ?></span>
                                            <?php if (!empty($semana['descripcion'])): ?>
                                                <div class="week-description" style="color:#444; margin-top:4px; font-size:0.95em;">
                                                    <?php echo nl2br(htmlspecialchars($semana['descripcion'])); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="week-actions">
                                            <button class="week-action-btn edit-btn" data-id="<?php echo $semana['id']; ?>">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="week-action-btn delete-btn" data-id="<?php echo $semana['id']; ?>">
                                                <span class="material-icons">delete</span>
                                            </button>
                                            <span class="material-icons toggle-icon">expand_less</span>
                                        </div>
                                    </div>
                                    <div class="folder-content" id="week-<?php echo $semana['numero_semana']; ?>-content" style="display: block;">
                                        <?php 
                                        $contenidoSemana = isset($contenidoPorSemana[$semana['numero_semana']]) ? $contenidoPorSemana[$semana['numero_semana']] : [];
                                        if (!empty($contenidoSemana)): 
                                        ?>
                                            <?php foreach ($contenidoSemana as $item): ?>
                                                <div class="content-item">
                                                    <div class="content-icon <?php echo $item['tipo']; ?>">
                                                        <?php 
                                                        $iconos = [
                                                            'anuncio' => 'campaign',
                                                            'tarea' => 'assignment',
                                                            'examen' => 'quiz',
                                                            'documento' => 'picture_as_pdf',
                                                            'presentacion' => 'slideshow',
                                                            'enlace' => 'link',
                                                            'video' => 'play_circle',
                                                            'participacion' => 'groups'
                                                        ];
                                                        $icono = $iconos[$item['tipo']] ?? 'description';
                                                        ?>
                                                        <span class="material-icons"><?php echo $icono; ?></span>
                                                    </div>
                                                    <div class="content-details" id="contenido-<?php echo $item['id']; ?>">
                                                        <h4><?php echo htmlspecialchars($item['titulo']); ?></h4>
                                                        <div class="content-description" style="color:#222;">
                                                            <?php 
                                                            // Limpiar color y background-color del HTML y fragmentos
                                                            $desc = $item['descripcion'];
                                                            $desc = preg_replace('/<!--.*?-->/s', '', $desc); // Quitar fragmentos HTML
                                                            $desc = preg_replace('/color\s*:\s*[^;"\']+;?/i', '', $desc);
                                                            $desc = preg_replace('/background-color\s*:\s*[^;"\']+;?/i', '', $desc);
                                                            // Truncar a 120 caracteres sin cortar etiquetas HTML
                                                            $plain = strip_tags($desc);
                                                            $max = 120;
                                                            if (mb_strlen($plain) > $max) {
                                                                $trunc = mb_substr($plain, 0, $max);
                                                                $trunc .= '...';
                                                                echo '<span style="color:#222">' . nl2br(htmlspecialchars($trunc)) . '</span>';
                                                            } else {
                                                                echo '<span style="color:#222">' . $desc . '</span>';
                                                            }
                                                            ?>
                                                        </div>
                                                        <span class="content-date">
                                                            <?php if ($item['tipo'] === 'tarea' || $item['tipo'] === 'examen'): ?>
                                                                Fecha límite: <?php echo date('d/m/Y', strtotime($item['fecha_limite'])); ?>
                                                            <?php else: ?>
                                                                Publicado: <?php echo date('d/m/Y', strtotime($item['created_at'])); ?>
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                    <div class="content-actions">
                                                        <?php if ($item['tipo'] === 'tarea' || $item['tipo'] === 'examen'): ?>
                                                            <span class="task-status">0 pendientes</span>
                                                        <?php elseif ($item['tipo'] === 'participacion'): ?>
                                                            <span class="participation-status">0 calificados</span>
                                                        <?php endif; ?>
                                                        <button class="content-action-btn view-btn" data-id="<?php echo $item['id']; ?>">
                                                            <span class="material-icons">visibility</span>
                                                        </button>
                                                        <button class="content-action-btn edit-btn" data-id="<?php echo $item['id']; ?>">
                                                            <span class="material-icons">edit</span>
                                                        </button>
                                                        <button class="content-action-btn delete-btn" data-id="<?php echo $item['id']; ?>">
                                                            <span class="material-icons">delete</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="empty-content">
                                                <span class="material-icons">inbox</span>
                                                <p>Aún no se ha subido ningún contenido</p>
                                            </div>
                                        <?php endif; ?>
                                        <div class="add-content-btn">
                                            <span class="material-icons">add_circle</span>
                                            <span>Agregar contenido a esta carpeta</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-weeks">
                                <div class="no-weeks-icon">
                                    <span class="material-icons">folder_open</span>
                                </div>
                                <h3>No hay semanas académicas creadas</h3>
                                <p>Crea la primera semana académica para comenzar a organizar el contenido del curso.</p>
                                <button class="create-week-btn">
                                    <span class="material-icons">add</span>
                                    Crear primera semana
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para crear/editar carpeta -->
    <div id="folder-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="folder-modal-title">Crear Nueva Carpeta</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="folder-form">
                    <input type="hidden" id="folder-id" value="">
                    <input type="hidden" id="curso-id" value="<?php echo $cursoId; ?>">
                    
                    <div class="form-group">
                        <label for="folder-title">Título de la carpeta</label>
                        <input type="text" id="folder-title" required placeholder="Ej: Semana 1: Introducción">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="folder-start-date">Fecha de inicio</label>
                            <input type="date" id="folder-start-date" required>
                        </div>
                        <div class="form-group">
                            <label for="folder-end-date">Fecha de fin</label>
                            <input type="date" id="folder-end-date" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="folder-description">Descripción (opcional)</label>
                        <textarea id="folder-description" rows="3" placeholder="Descripción breve de los contenidos de esta carpeta"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Carpeta</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para crear/editar contenido -->
    <div id="content-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="content-modal-title">Crear Nuevo Contenido</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="content-form">
                    <input type="hidden" id="content-id" value="">
                    <input type="hidden" id="curso-id-content" value="<?php echo $cursoId; ?>">
                    
                    <div class="form-group">
                        <label for="content-type">Tipo de contenido</label>
                        <select id="content-type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="anuncio">Anuncio</option>
                            <option value="tarea">Tarea</option>
                            <option value="examen">Examen</option>
                            <option value="documento">Documento</option>
                            <option value="presentacion">Presentación</option>
                            <option value="enlace">Enlace</option>
                            <option value="video">Video</option>
                            <option value="participacion">Participación</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="content-section">Sección</label>
                        <select id="content-section" required>
                            <option value="">Seleccionar sección</option>
                            <?php foreach ($semanas as $semana): ?>
                                <option value="<?php echo $semana['id']; ?>">Semana <?php echo $semana['numero_semana']; ?>: <?php echo htmlspecialchars($semana['titulo']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="content-title">Título</label>
                        <input type="text" id="content-title" required placeholder="Ej: Tarea: Ejercicios de fracciones / Examen: Evaluación de fracciones">
                    </div>
                    
                    <div class="form-group">
                        <label for="content-description">Contenido Completo *</label>
                        <div class="rich-text-editor">
                            <div class="editor-toolbar">
                                <button type="button" class="toolbar-btn" data-command="bold" title="Negrita">
                                    <strong>B</strong>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="italic" title="Cursiva">
                                    <em>I</em>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="underline" title="Subrayado">
                                    <u>U</u>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="strikeThrough" title="Tachado">
                                    <s>T</s>
                                </button>
                                <div class="toolbar-separator"></div>
                                <button type="button" class="toolbar-btn" data-command="insertUnorderedList" title="Lista con viñetas">
                                    <span class="material-icons">format_list_bulleted</span>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="insertOrderedList" title="Lista numerada">
                                    <span class="material-icons">format_list_numbered</span>
                                </button>
                                <div class="toolbar-separator"></div>
                                <button type="button" class="toolbar-btn" data-command="createLink" title="Insertar enlace">
                                    <span class="material-icons">link</span>
                                </button>
                                <button type="button" class="toolbar-btn" id="insert-image-btn" title="Insertar imagen">
                                    <span class="material-icons">image</span>
                                </button>
                            </div>
                            <div id="content-description" class="editor-content" contenteditable="true" placeholder="Escribe el contenido completo aquí..." dir="ltr"></div>
                        </div>
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                    </div>
                    
                    <!-- Campos específicos para tareas y exámenes -->
                    <div id="task-fields" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="task-due-date">Fecha límite</label>
                                <input type="date" id="task-due-date">
                            </div>
                            <div class="form-group">
                                <label for="task-due-time">Hora límite</label>
                                <input type="time" id="task-due-time">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="task-points">Puntos</label>
                            <input type="number" id="task-points" min="0" max="100" value="10">
                        </div>
                        

                    </div>
                    
                    <!-- Campos específicos para documentos y presentaciones -->
                    <div id="file-fields" style="display: none;">
                        <div class="form-group">
                            <label for="content-file">Archivo</label>
                            <div class="file-upload">
                                <input type="file" id="content-file">
                                <div class="file-upload-btn">
                                    <span class="material-icons">upload_file</span>
                                    Seleccionar archivo
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Campos específicos para enlaces y videos -->
                    <div id="link-fields" style="display: none;">
                        <div class="form-group">
                            <label for="content-url">URL</label>
                            <input type="url" id="content-url" placeholder="https://...">
                        </div>
                    </div>

                    <!-- Campos específicos para participación -->
                    <div id="participation-fields" style="display: none;">
                        <div class="form-group">
                            <label>Lista de Estudiantes</label>
                            <div class="students-list">
                                <div class="student-item">
                                    <span class="student-name">Ana García López</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">Carlos Mendoza Silva</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">María Rodríguez Torres</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">José Fernández Cruz</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">Lucía Vargas Morales</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Contenido</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para crear/editar videoconferencia -->
    <div id="meet-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="meet-modal-title">Crear Videoconferencia</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="meet-form">
                    <input type="hidden" id="meet-id" value="">
                    <input type="hidden" id="curso-id-meet" value="<?php echo $cursoId; ?>">
                    <div class="form-group">
                        <label for="meet-title">Título</label>
                        <input type="text" id="meet-title" required placeholder="Ej: Clase en vivo: Operaciones con fracciones">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="meet-date">Fecha</label>
                            <input type="date" id="meet-date" required>
                        </div>
                        <div class="form-group">
                            <label for="meet-time">Hora</label>
                            <input type="time" id="meet-time" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="meet-url">URL de la reunión</label>
                        <input type="url" id="meet-url" required placeholder="https://...">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Videoconferencia</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal de confirmación para eliminar -->
    <div id="delete-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3 id="delete-modal-title">Eliminar Elemento</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="delete-modal-message">¿Está seguro que desea eliminar este elemento? Esta acción no se puede deshacer.</p>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-btn">Eliminar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal para ver anuncio -->
    <div id="announcement-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Anuncio: Bienvenidos al curso de matemáticas</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="announcement-meta">
                    Publicado: 15/03/2025 por Prof. Carlos García
                </div>
                <div class="announcement-text">
                    <p>¡Bienvenidos al curso de matemáticas de 5° grado!</p>
                    <p>En este curso aprenderemos sobre fracciones, decimales, geometría y muchos temas más. Es importante que revisen el material de clase regularmente y completen las tareas asignadas.</p>
                    <p>Objetivos del curso:</p>
                    <ul>
                        <li>Comprender y aplicar operaciones con fracciones</li>
                        <li>Resolver problemas matemáticos de la vida cotidiana</li>
                        <li>Desarrollar habilidades de razonamiento lógico</li>
                        <li>Aprender conceptos básicos de geometría</li>
                    </ul>
                    <p>¡Espero que disfruten el curso y aprendan mucho!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para vista previa de documentos -->
    <div id="document-preview-modal" class="modal-overlay">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 id="document-title">Vista Previa del Documento</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="document-preview-container">
                    <div class="document-info">
                        <div class="document-meta">
                            <span class="document-type">Documento PDF</span>
                            <span class="document-size">2.5 MB</span>
                            <span class="document-date">Publicado: 16/03/2025</span>
                        </div>
                    </div>
                    <div class="document-content">
                        <div class="document-description-enhanced">
                            <div class="description-content">
                                <p>Guía completa con ejercicios prácticos sobre fracciones organizados por nivel de dificultad. Incluye problemas de suma, resta, multiplicación y división de fracciones, con ejemplos resueltos paso a paso y espacios para que los estudiantes practiquen.</p>
                                <p><strong>Contenido incluido:</strong> Conceptos básicos, operaciones fundamentales, ejercicios resueltos y actividades prácticas. Formato descargable e imprimible.</p>
                            </div>
                        </div>

                        <div class="document-preview-card">
                            <div class="file-preview-header">
                                <div class="file-icon-container">
                                    <span class="material-icons file-icon pdf-icon">picture_as_pdf</span>
                                </div>
                                <div class="file-info">
                                    <h4 class="file-name">Guía de ejercicios fracciones.pdf</h4>
                                    <div class="file-details">
                                        <span class="file-size">2.5 MB</span>
                                        <span class="file-type">Documento PDF</span>
                                    </div>
                                </div>
                                <div class="file-actions">
                                    <button class="btn-download" onclick="downloadFile('guia_ejercicios_fracciones.pdf')">
                                        <span class="material-icons">download</span>
                                        Descargar
                                    </button>
                                </div>
                            </div>
                            <div class="file-preview-content">
                                <div class="preview-thumbnail">
                                    <span class="material-icons preview-icon">visibility</span>
                                    <p>Haz clic para ver el contenido del documento</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="btn-secondary" id="download-document-btn">
                            <span class="material-icons">download</span>
                            Descargar
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualizar contenido completo -->
    <div id="view-content-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="view-content-title">Título del contenido</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="view-content-meta" id="view-content-meta" style="margin-bottom:10px;color:#666;font-size:0.95em;"></div>
                <div class="view-content-description" id="view-content-description" style="color:#222;"></div>
            </div>
        </div>
    </div>

    <!-- Datos para JavaScript -->
    <script>
        window.cursoData = <?php echo json_encode($curso); ?>;
        window.semanasData = <?php echo json_encode($semanas); ?>;
        window.contenidoData = <?php echo json_encode($contenido); ?>;
        window.videoconferenciasData = <?php echo json_encode($videoconferencias); ?>;
        window.cursoId = <?php echo $cursoId; ?>;
    </script>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/contenido_m.js"></script>
</body>
</html>

