/* Estilos específicos para la página de inicio de maestros */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  
    /* Colores para clases */
    --math-color: #2196f3;
    --science-color: #4caf50;
    --language-color: #9c27b0;
    --history-color: #e91e63;
    --art-color: #ff9800;
  
    /* Colores para recursos */
    --pdf-color: #f44336;
    --doc-color: #2196f3;
    --ppt-color: #ff9800;
    --video-color: #4caf50;
  }
  
  /* Sección de bienvenida */
  .welcome-section {
    margin-bottom: 30px;
  }
  
  .welcome-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
  }
  
  .welcome-content {
    flex: 1;
  }
  
  .welcome-content h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 10px;
  }
  
  .welcome-content p {
    color: var(--text-light);
    margin-bottom: 20px;
    font-size: 1.05rem;
  }
  
  .welcome-actions {
    display: flex;
    gap: 15px;
  }
  
  .action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 5px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
  }
  
  .welcome-image {
    width: 150px;
    height: 150px;
    margin-left: 30px;
  }
  
  .welcome-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  /* Estadísticas rápidas */
  .stats-section {
    margin-bottom: 30px;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }
  
  .stat-icon .material-icons {
    font-size: 2rem;
    color: var(--primary-color);
  }
  
  .stat-info {
    flex: 1;
  }
  
  .stat-info h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-light);
  }
  
  .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 5px;
  }
  
  .stat-desc {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  /* Secciones del dashboard */
  .dashboard-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .section-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .view-all-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
  }
  
  .view-all-link:hover {
    color: #1e40af;
  }
  
  .view-all-link .material-icons {
    font-size: 1.1rem;
    margin-left: 5px;
    transition: transform 0.3s;
  }
  
  .view-all-link:hover .material-icons {
    transform: translateX(3px);
  }
  
  /* Horario del día */
  .schedule-list {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }

  .schedule-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
  }

  .schedule-item:last-child {
    border-bottom: none;
  }

  .schedule-item:hover {
    background-color: var(--secondary-color);
  }

  .schedule-item.current-class {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
  }

  .schedule-item.current-class::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  }

  .schedule-item.break-time {
    background-color: #f3e5f5;
    border-left: 4px solid #9c27b0;
  }

  .schedule-item.break-time::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ba68c8 0%, #9c27b0 100%);
  }

  .schedule-time {
    min-width: 100px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-light);
    margin-right: 20px;
  }

  .schedule-details {
    flex: 1;
  }

  .schedule-subject {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .schedule-grade {
    font-size: 0.85rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .schedule-item.current-class .schedule-subject {
    color: #1976d2;
  }

  .schedule-item.current-class .schedule-time {
    color: #1976d2;
  }

  .schedule-item.break-time .schedule-subject {
    color: #7b1fa2;
  }

  .schedule-item.break-time .schedule-time {
    color: #7b1fa2;
  }
  
  /* Tareas pendientes */
  .tasks-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background-color: var(--secondary-color);
    transition: var(--transition);
    cursor: pointer;
  }
  
  .task-item:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
  }
  
  .task-info {
    flex: 1;
  }
  
  .task-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .task-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .task-pending {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 80px;
  }

  .pending-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff9800;
    line-height: 1;
    margin-bottom: 2px;
  }

  .pending-label {
    font-size: 0.75rem;
    color: #ff9800;
    font-weight: 500;
    text-transform: lowercase;
  }

  .task-pending.urgent .pending-number {
    color: #f44336;
  }

  .task-pending.urgent .pending-label {
    color: #f44336;
  }
  
  /* Mensajes recientes */
  .messages-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .message-item {
    display: flex;
    padding: 15px;
    border-radius: 8px;
    background-color: var(--secondary-color);
    transition: var(--transition);
  }
  
  .message-item:hover {
    background-color: var(--primary-light);
  }
  
  .message-item.unread {
    border-left: 3px solid var(--primary-color);
  }
  
  .message-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
  }
  
  .message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .message-content {
    flex: 1;
  }
  
  .message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }
  
  .message-sender {
    font-weight: 600;
    color: var(--text-color);
  }
  
  .message-time {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .message-preview {
    color: var(--text-light);
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* Próximos eventos */
  .events-timeline {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .event-item {
    display: flex;
    background-color: var(--secondary-color);
    border-radius: 8px;
    overflow: hidden;
    transition: var(--transition);
  }
  
  .event-item:hover {
    background-color: var(--primary-light);
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
  }
  
  .event-date {
    width: 80px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
  }
  
  .date-day {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
  }
  
  .date-month {
    font-size: 0.9rem;
    font-weight: 500;
  }
  
  .event-content {
    flex: 1;
    padding: 15px;
  }
  
  .event-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-color);
  }
  
  .event-details {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
  }
  
  .event-time,
  .event-location {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .event-time .material-icons,
  .event-location .material-icons {
    font-size: 1rem;
    margin-right: 5px;
  }
  
  .event-description {
    font-size: 0.95rem;
    color: var(--text-light);
  }
  
  /* Recursos populares */
  .resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .resource-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    transition: var(--transition);
  }
  
  .resource-card:hover {
    background-color: var(--primary-light);
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
  }
  
  .resource-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
  }
  
  .pdf-icon {
    background-color: var(--pdf-color);
  }
  
  .doc-icon {
    background-color: var(--doc-color);
  }
  
  .ppt-icon {
    background-color: var(--ppt-color);
  }
  
  .video-icon {
    background-color: var(--video-color);
  }
  
  .resource-info {
    flex: 1;
  }
  
  .resource-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .resource-meta {
    font-size: 0.85rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .resource-actions {
    display: flex;
    gap: 5px;
  }
  
  .resource-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .resource-btn:hover {
    background-color: white;
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  /* Layout de dos columnas */
  .two-column-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 30px;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .welcome-card {
      flex-direction: column;
      text-align: center;
    }
  
    .welcome-content {
      margin-bottom: 20px;
    }
  
    .welcome-actions {
      justify-content: center;
    }
  
    .welcome-image {
      margin-left: 0;
    }
  
    .two-column-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  
    .resources-grid {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 768px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .event-details {
      flex-direction: column;
      gap: 5px;
    }
  
    .schedule-item {
      padding: 12px 16px;
    }

    .schedule-time {
      min-width: 80px;
      font-size: 0.85rem;
      margin-right: 15px;
    }

    .schedule-subject {
      font-size: 0.95rem;
    }

    .schedule-grade {
      font-size: 0.8rem;
    }
    
    .task-meta {
      flex-direction: column;
      gap: 5px;
    }
    
    .task-item {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .task-pending {
      margin-top: 10px;
      align-self: flex-end;
    }
  }
  
  @media (max-width: 576px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  
    .welcome-actions {
      flex-direction: column;
      gap: 10px;
    }
  
    .action-btn {
      width: 100%;
      justify-content: center;
    }
  
    .task-meta {
      flex-direction: column;
      gap: 5px;
    }
  }
  