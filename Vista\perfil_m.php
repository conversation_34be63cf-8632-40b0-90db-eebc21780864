<?php
require_once '../Controlador/AuthController.php';
require_once '../Controlador/PerfilController.php';

// Proteger la página - solo maestros
AuthController::protegerPagina(['maestro']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);

// Obtener datos completos del perfil
$perfilController = new PerfilController();
$datosPerfil = $perfilController->obtenerPerfilCompleto();
$perfil = $datosPerfil['success'] ? $datosPerfil : null;

// Validar que los datos básicos existan
if ($perfil && !isset($perfil['datos_basicos'])) {
    $perfil = null;
}

// Obtener información específica del maestro
$infoMaestro = $perfil['informacion_rol'] ?? [];
$especialidad = $infoMaestro['especialidad'] ?? 'No especificada';
$gradoTutor = $infoMaestro['grado_tutor'] ?? 'Sin grado asignado';
$nivelEducativo = $infoMaestro['nivel_educativo'] ?? 'No especificado';
$fechaContratacion = $infoMaestro['fecha_contratacion'] ?? 'No especificada';

// Datos básicos del perfil
$datosBasicos = $perfil['datos_basicos'] ?? [];
$edad = $perfil['edad'] ?? null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// URL de la foto de perfil
$fotoPerfilUrl = !empty($datosBasicos['nombre_foto']) ? "../Controlador/ImagenController.php?usuario_id=" . $usuarioActual['id'] : null;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Perfil Maestro</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/perfil.css">
    <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar maestro'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                        <?php if ($fotoPerfilUrl): ?>
                            <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                        <p>Maestro(a) - <?php echo htmlspecialchars($especialidad); ?></p>
                        <?php if ($gradoTutor !== 'Sin grado asignado'): ?>
                            <small>Tutor: <?php echo htmlspecialchars($gradoTutor); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_m.php">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../Controlador/AuthController.php?action=logout">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mi Perfil</h1>
                    <p class="current-date">Lunes, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <div class="profile-container">
                    <!-- Sección de perfil principal -->
                    <section class="profile-main">
                        <div class="profile-header">
                            <div class="profile-avatar-container">
                                <div class="profile-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar maestro'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                                    <?php if ($fotoPerfilUrl): ?>
                                        <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                                    <?php endif; ?>
                                </div>
                                <button class="change-avatar-btn" onclick="abrirModalFoto()">
                                    <span class="material-icons">photo_camera</span>
                                    <span>Cambiar foto</span>
                                </button>
                            </div>
                            <div class="profile-info">
                                <h2><?php echo htmlspecialchars($nombreCompleto); ?></h2>
                                <p class="profile-role">Profesor</p>
                                <p class="profile-grade"><?php echo htmlspecialchars($especialidad); ?> - <?php echo htmlspecialchars(ucfirst($nivelEducativo)); ?></p>
                                <p class="profile-subject">Especialidad: <?php echo htmlspecialchars($especialidad); ?></p>
                                <?php if ($gradoTutor !== 'Sin grado asignado'): ?>
                                    <p class="profile-tutor">Tutor: <?php echo htmlspecialchars($gradoTutor); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de información personal -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">person</span>
                                Información Personal
                            </h3>
                            <button class="edit-section-btn" data-section="personal" style="display:inline-flex;align-items:center;gap:4px;">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre completo</div>
                                    <div class="info-value"><?php echo htmlspecialchars($nombreCompleto); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Fecha de nacimiento</div>
                                    <div class="info-value"><?php echo $datosBasicos['fecha_nacimiento'] ? date('d/m/Y', strtotime($datosBasicos['fecha_nacimiento'])) : 'No especificada'; ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Edad</div>
                                    <div class="info-value"><?php echo $edad ? $edad . ' años' : 'No especificada'; ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Sexo</div>
                                    <div class="info-value"><?php echo htmlspecialchars(ucfirst($datosBasicos['sexo'] ?? 'No especificado')); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Dirección</div>
                                    <div class="info-value"><?php echo htmlspecialchars($datosBasicos['direccion'] ?? 'No especificada'); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono</div>
                                    <div class="info-value"><?php echo htmlspecialchars($datosBasicos['telefono'] ?? 'No especificado'); ?></div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de cuenta -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">account_circle</span>
                                Información de Cuenta
                            </h3>
                            <button class="edit-section-btn" data-section="account">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre de usuario</div>
                                    <div class="info-value"><?php echo htmlspecialchars($datosBasicos['nombre_usuario'] ?? 'No especificado'); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Correo electrónico</div>
                                    <div class="info-value"><?php echo htmlspecialchars($datosBasicos['email'] ?? 'No especificado'); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Contraseña</div>
                                    <div class="info-value password-field">••••••••••
                                        <button class="change-password-btn" onclick="abrirModalContraseña()">
                                            <span class="material-icons">lock</span>
                                            Cambiar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección profesional -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">school</span>
                                Información Profesional
                            </h3>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Especialidad</div>
                                    <div class="info-value"><?php echo htmlspecialchars($especialidad); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Nivel educativo</div>
                                    <div class="info-value"><?php echo htmlspecialchars(ucfirst($nivelEducativo)); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Fecha de contratación</div>
                                    <div class="info-value"><?php echo $fechaContratacion !== 'No especificada' ? date('d/m/Y', strtotime($fechaContratacion)) : 'No especificada'; ?></div>
                                </div>
                                <?php if ($gradoTutor !== 'Sin grado asignado'): ?>
                                <div class="info-item">
                                    <div class="info-label">Grado tutor</div>
                                    <div class="info-value"><?php echo htmlspecialchars($gradoTutor); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>
                    
                    
                    
                    <!-- Sección de contacto de emergencia -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">contact_phone</span>
                                Contacto de Emergencia
                            </h3>
                            <button class="edit-section-btn" data-section="emergency">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre del contacto</div>
                                    <div class="info-value">Laura Mendoza (Esposa)</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono principal</div>
                                    <div class="info-value">+51 987 654 321</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono alternativo</div>
                                    <div class="info-value">+51 987 789 012</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Correo electrónico</div>
                                    <div class="info-value"><EMAIL></div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Modal para editar información personal -->
    <div id="edit-personal-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información Personal</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="nombres">Nombres</label>
                        <input type="text" id="nombres" name="nombres" value="<?php echo htmlspecialchars($datosBasicos['nombres'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="apellido_paterno">Apellido Paterno</label>
                        <input type="text" id="apellido_paterno" name="apellido_paterno" value="<?php echo htmlspecialchars($datosBasicos['apellido_paterno'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="apellido_materno">Apellido Materno</label>
                        <input type="text" id="apellido_materno" name="apellido_materno" value="<?php echo htmlspecialchars($datosBasicos['apellido_materno'] ?? ''); ?>" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fecha_nacimiento">Fecha de nacimiento</label>
                            <input type="date" id="fecha_nacimiento" name="fecha_nacimiento" value="<?php echo $datosBasicos['fecha_nacimiento'] ?? ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="sexo">Sexo</label>
                            <select id="sexo" name="sexo">
                                <option value="">Seleccionar</option>
                                <option value="masculino" <?php echo ($datosBasicos['sexo'] ?? '') === 'masculino' ? 'selected' : ''; ?>>Masculino</option>
                                <option value="femenino" <?php echo ($datosBasicos['sexo'] ?? '') === 'femenino' ? 'selected' : ''; ?>>Femenino</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="direccion">Dirección</label>
                        <input type="text" id="direccion" name="direccion" value="<?php echo htmlspecialchars($datosBasicos['direccion'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label for="telefono">Teléfono</label>
                        <input type="tel" id="telefono" name="telefono" value="<?php echo htmlspecialchars($datosBasicos['telefono'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para editar información de cuenta -->
    <div id="edit-account-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información de Cuenta</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="email">Correo electrónico</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($datosBasicos['email'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="nombre_usuario">Nombre de usuario</label>
                        <input type="text" id="nombre_usuario" name="nombre_usuario" value="<?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?>" required>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para cambiar contraseña -->
    <div id="change-password-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cambiar Contraseña</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="current-password">Contraseña actual</label>
                        <input type="password" id="current-password" name="password_actual" required>
                    </div>
                    <div class="form-group">
                        <label for="new-password">Nueva contraseña</label>
                        <input type="password" id="new-password" name="password_nuevo" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="confirm-password">Confirmar nueva contraseña</label>
                        <input type="password" id="confirm-password" name="password_confirmar" required minlength="6">
                    </div>
                    
                    <div class="password-requirements">
                        <p>La contraseña debe cumplir con los siguientes requisitos:</p>
                        <ul>
                            <li>Mínimo 6 caracteres</li>
                            <li>Al menos una letra mayúscula</li>
                            <li>Al menos un número</li>
                            <li>Al menos un carácter especial</li>
                        </ul>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Cambiar contraseña</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para editar contacto de emergencia -->
    <div id="edit-emergency-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Contacto de Emergencia</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="emergency-name">Nombre del contacto</label>
                        <input type="text" id="emergency-name" value="Laura Mendoza (Esposa)">
                    </div>
                    <div class="form-group">
                        <label for="emergency-phone">Teléfono principal</label>
                        <input type="tel" id="emergency-phone" value="+51 987 654 321">
                    </div>
                    <div class="form-group">
                        <label for="emergency-alt-phone">Teléfono alternativo</label>
                        <input type="tel" id="emergency-alt-phone" value="+51 987 789 012">
                    </div>
                    <div class="form-group">
                        <label for="emergency-email">Correo electrónico</label>
                        <input type="email" id="emergency-email" value="<EMAIL>">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para subir foto de perfil -->
    <div id="upload-photo-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cambiar Foto de Perfil</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="upload-photo-form" class="edit-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="photo-file">Seleccionar imagen</label>
                        <input type="file" id="photo-file" name="foto_perfil" accept="image/*" required>
                        <small>Formatos permitidos: JPG, PNG, GIF. Tamaño máximo: 5MB</small>
                    </div>
                    
                    <div class="photo-preview" id="photo-preview" style="display: none;">
                        <img id="preview-image" src="" alt="Vista previa">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Subir foto</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/perfil_m.js"></script>
</body>
</html>