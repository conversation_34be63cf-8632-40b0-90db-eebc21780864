/* Estilos específicos para el portal de padres */

/* Variables */
:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Selector de estudiante */
.student-selector {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  padding: 20px 25px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.15);
  position: relative;
  overflow: hidden;
}

.student-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.student-selector label {
  font-weight: 600;
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
}

.student-selector select {
  flex: 1;
  padding: 14px 18px;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-color);
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2342a5f5' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 14px center;
  background-repeat: no-repeat;
  background-size: 18px;
  outline: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
}

.student-selector select:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  background-color: #f8fafc;
}

.student-selector select:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
  background-color: white;
}

.student-selector select option {
  padding: 12px;
  font-weight: 500;
  color: var(--text-color);
  background-color: white;
}

/* Resumen del estudiante */
.student-summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.student-profile {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.student-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--primary-light);
  flex-shrink: 0;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-info h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.student-info p {
  font-size: 0.95rem;
  color: var(--text-light);
  margin-bottom: 3px;
}

.student-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 10px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(66, 165, 245, 0.25);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #ffa726 0%, #ff8f00 100%);
  box-shadow: 0 8px 25px rgba(255, 167, 38, 0.15);
}

.stat-card:nth-child(2):hover {
  box-shadow: 0 15px 35px rgba(255, 167, 38, 0.25);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.15);
}

.stat-card:nth-child(3):hover {
  box-shadow: 0 15px 35px rgba(66, 165, 245, 0.25);
}

.stat-card:nth-child(4) {
  background: linear-gradient(135deg, #ffa726 0%, #ff8f00 100%);
  box-shadow: 0 8px 25px rgba(255, 167, 38, 0.15);
}

.stat-card:nth-child(4):hover {
  box-shadow: 0 15px 35px rgba(255, 167, 38, 0.25);
}

.stat-icon {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.stat-icon .material-icons {
  font-size: 2rem;
  color: white;
}

.stat-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-info h3 {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: rgba(255,255,255,0.9);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Anuncios */
.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.announcement-item {
  display: flex;
  gap: 15px;
  padding: 25px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(66, 165, 245, 0.1);
  position: relative;
  overflow: hidden;
}

.announcement-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
}

.announcement-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.12);
  border-color: rgba(66, 165, 245, 0.2);
}

.announcement-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #fff8e1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.announcement-icon .material-icons {
  font-size: 1.8rem;
  color: var(--warning-color);
}

.announcement-content {
  flex: 1;
}

.announcement-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.announcement-content p {
  font-size: 0.95rem;
  color: var(--text-color);
  margin-bottom: 15px;
  line-height: 1.5;
}

.announcement-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Calendario */
.month-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.month-nav-btn {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.month-nav-btn:hover {
  background-color: var(--secondary-color);
  color: var(--primary-color);
}

.calendar-container {
  margin-bottom: 30px;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.calendar-day {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 5px;
  cursor: pointer;
  transition: var(--transition);
}

.calendar-day:hover {
  background-color: var(--secondary-color);
}

.calendar-day.empty {
  cursor: default;
}

.calendar-day.weekend {
  color: var(--text-light);
}

.calendar-day.current {
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.calendar-day.has-event::after {
  content: "";
  position: absolute;
  bottom: 5px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.day-number {
  font-size: 0.9rem;
}

.event-indicator {
  position: absolute;
  bottom: 5px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.upcoming-events h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.event-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.event-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.event-date {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.date-month {
  font-size: 0.7rem;
  font-weight: 500;
}

.date-day {
  font-size: 1.3rem;
  font-weight: 700;
}

.event-content {
  flex: 1;
}

.event-content h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.event-content p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
  line-height: 1.5;
}

.event-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.85rem;
  color: var(--text-light);
}

.event-time, .event-location {
  display: flex;
  align-items: center;
  gap: 5px;
}

.event-time .material-icons, .event-location .material-icons {
  font-size: 1.1rem;
}

/* Tareas pendientes */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 167, 38, 0.1);
  position: relative;
  overflow: hidden;
}

.task-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #ffa726 0%, #ff8f00 100%);
}

.task-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.12);
  border-color: rgba(255, 167, 38, 0.2);
}

.task-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 5px;
  flex-shrink: 0;
}

.task-status.pending {
  background-color: var(--warning-color);
}

.task-status.completed {
  background-color: var(--success-color);
}

.task-content {
  flex: 1;
}

.task-content h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
}

.task-content p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.85rem;
}

.task-course {
  color: var(--primary-color);
  font-weight: 500;
}

.task-date {
  color: var(--text-light);
}

/* Responsive */
@media (max-width: 992px) {
  .student-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .student-profile {
    flex-direction: column;
    text-align: center;
  }
  
  .student-stats {
    grid-template-columns: 1fr;
  }
  
  .student-selector {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .student-selector select {
    width: 100%;
  }
  
  .announcement-item {
    flex-direction: column;
  }
  
  .announcement-icon {
    margin: 0 auto 15px;
  }
  
  .announcement-content {
    text-align: center;
  }
  
  .announcement-meta {
    justify-content: center;
  }
  
  .calendar-day {
    height: 35px;
  }
  
  .day-number {
    font-size: 0.8rem;
  }
  
  .event-item {
    flex-direction: column;
    text-align: center;
  }
  
  .event-date {
    margin: 0 auto 15px;
  }
  
  .event-meta {
    justify-content: center;
  }
}

/* Mejoras adicionales para el dashboard */
.dashboard-section {
  margin-bottom: 35px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f1f5f9;
}

.section-header h2 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-color);
  position: relative;
}

.section-header h2::before {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  border-radius: 2px;
}

.view-all {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(102, 126, 234, 0.1);
}

.view-all:hover {
  background-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

/* Animaciones adicionales */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-section {
  animation: fadeInUp 0.6s ease-out;
}

.dashboard-section:nth-child(2) {
  animation-delay: 0.1s;
}

.dashboard-section:nth-child(3) {
  animation-delay: 0.2s;
}

.dashboard-section:nth-child(4) {
  animation-delay: 0.3s;
}