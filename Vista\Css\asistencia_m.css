/* Estilos específicos para la página de asistencia de maestros */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Tarjeta de asistencia del día */
  .attendance-today-section {
    margin-bottom: 30px;
  }
  
  .attendance-card {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-sm);
  }
  
  .attendance-status {
    display: flex;
    align-items: center;
  }
  
  .status-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
  }
  
  .status-icon .material-icons {
    font-size: 2.5rem;
    color: var(--primary-color);
  }
  
  .status-info h2 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .status-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--warning-color);
  }
  
  .status-value.registered {
    color: var(--success-color);
  }
  
  .attendance-actions {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .time-display {
    text-align: center;
    margin-right: 10px;
  }
  
  .current-time {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 5px;
  }
  
  .time-label {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .attendance-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .entry-btn {
    background-color: var(--success-color);
    color: white;
  }
  
  .entry-btn:hover {
    background-color: #388e3c;
  }
  
  .exit-btn {
    background-color: var(--primary-color);
    color: white;
  }
  
  .exit-btn:hover {
    background-color: #1e40af;
  }
  
  .attendance-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  /* Resumen de asistencia */
  .attendance-summary-section {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .summary-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .month-selector {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .month-nav-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .month-nav-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .current-month {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
  }
  
  .stat-item {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: var(--transition);
  }
  
  .stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-sm);
  }
  
  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 0.95rem;
    color: var(--text-light);
  }
  
  /* Registro detallado de asistencia */
  .attendance-log-section {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .section-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .filter-options {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .filter-options select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: white;
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
    outline: none;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .filter-options select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .search-box {
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 0 15px;
    transition: var(--transition);
  }
  
  .search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .search-box .material-icons {
    color: var(--text-light);
    margin-right: 10px;
  }
  
  .search-box input {
    width: 200px;
    padding: 10px 0;
    border: none;
    background: transparent;
    outline: none;
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
  }
  
  .attendance-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
  }
  
  .attendance-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .attendance-table th {
    padding: 15px;
    text-align: left;
    background-color: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
    white-space: nowrap;
  }
  
  .attendance-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
  }
  
  .attendance-table tbody tr:last-child td {
    border-bottom: none;
  }
  
  .attendance-table tbody tr:hover {
    background-color: var(--secondary-color);
  }
  
  .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }
  
  .status-badge.present {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .status-badge.late {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .status-badge.absent {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .status-badge.weekend {
    background-color: rgba(158, 158, 158, 0.1);
    color: #757575;
  }
  
  .status-badge.pending {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
  }
  
  .status-badge.approved {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .status-badge.rejected {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
  }
  
  .pagination-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--text-color);
  }
  
  .pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .pagination-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Solicitudes de justificación */
  .justification-section {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .new-request-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .new-request-btn:hover {
    background-color: #1e40af;
  }
  
  .justification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
  }
  
  .justification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background-color: var(--secondary-color);
    transition: var(--transition);
  }
  
  .justification-item:hover {
    background-color: var(--primary-light);
  }
  
  .justification-info {
    display: grid;
    grid-template-columns: 120px 150px 1fr;
    gap: 20px;
    align-items: center;
  }
  
  .justification-date {
    font-weight: 600;
    color: var(--text-color);
  }
  
  .justification-type {
    color: var(--text-light);
  }
  
  .justification-reason {
    color: var(--text-color);
  }
  
  /* Modal de justificación */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  .justification-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .form-group label {
    font-weight: 500;
    color: var(--text-color);
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .file-upload {
    position: relative;
  }
  
  .file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  
  .file-upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 15px;
    border: 1px dashed var(--border-color);
    border-radius: 5px;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .file-upload:hover .file-upload-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 10px;
  }
  
  .btn-secondary,
  .btn-primary {
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: #e0e0e0;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .attendance-card {
      flex-direction: column;
      gap: 20px;
    }
  
    .attendance-status {
      width: 100%;
      justify-content: center;
    }
  
    .attendance-actions {
      width: 100%;
      justify-content: center;
    }
  
    .summary-stats {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .justification-info {
      grid-template-columns: 1fr;
      gap: 5px;
    }
  }
  
  @media (max-width: 768px) {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .filter-options {
      width: 100%;
    }
  
    .search-box {
      width: 100%;
    }
  
    .search-box input {
      width: 100%;
    }
  
    .summary-stats {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .attendance-actions {
      flex-direction: column;
    }
  
    .attendance-btn {
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    .summary-stats {
      grid-template-columns: 1fr;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }
  
  