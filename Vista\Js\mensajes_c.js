document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const messageSubject = document.getElementById("message-subject")
    const messageContent = document.getElementById("message-content")
    const sendMessageBtn = document.querySelector(".send-message-btn")
    
    // Enviar mensaje
    if (sendMessageBtn) {
      sendMessageBtn.addEventListener("click", () => {
        const subject = messageSubject.value
        const content = messageContent.value.trim()
        
        // Validar que se haya seleccionado un asunto
        if (!subject) {
          alert("Por favor, selecciona un asunto para tu mensaje.")
          return
        }
        
        // Validar que se haya escrito un mensaje
        if (!content) {
          alert("Por favor, escribe un mensaje antes de enviar.")
          return
        }
        
        // Aquí iría la lógica para enviar el mensaje al servidor
        // Por ahora, solo mostramos un mensaje de éxito
        alert("Mensaje enviado correctamente. El profesor responderá a la brevedad.")
        
        // Limpiar el formulario
        messageSubject.value = ""
        messageContent.value = ""
      })
    }
    
    // Mostrar detalles de un mensaje al hacer clic
    const messageItems = document.querySelectorAll(".message-item")
    if (messageItems.length > 0) {
      messageItems.forEach((item) => {
        item.addEventListener("click", () => {
          // Aquí podría ir la lógica para expandir/colapsar el mensaje
          // o mostrar más detalles si es necesario
        })
      })
    }
  })