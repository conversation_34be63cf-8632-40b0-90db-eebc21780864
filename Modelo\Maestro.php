<?php
require_once 'Conexion.php';
require_once 'Usuario.php';

/**
 * Modelo para manejar maestros
 */
class Maestro {
    private $pdo;
    private $usuario;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->usuario = new Usuario();
    }

    /**
     * Crea un nuevo maestro
     * @param array $datosUsuario
     * @param array $datosPersona
     * @param array $datosMaestro
     * @return int|false
     */
    public function crearMaestro($datosUsuario, $datosPersona, $datosMaestro) {
        try {
            $this->pdo->beginTransaction();

            // Crear usuario y persona
            $resultado = $this->usuario->crearUsuario($datosUsuario, $datosPersona);
            if (!$resultado) {
                throw new Exception("Error creando usuario base");
            }

            // Insertar datos específicos del maestro
            $sql = "INSERT INTO maestros (persona_id, especialidad, nivel_educativo, grado_tutor, fecha_contratacion) 
                    VALUES (:persona_id, :especialidad, :nivel_educativo, :grado_tutor, :fecha_contratacion)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':persona_id', $resultado['persona_id']);
            $stmt->bindParam(':especialidad', $datosMaestro['especialidad']);
            $stmt->bindParam(':nivel_educativo', $datosMaestro['nivel_educativo']);
            $stmt->bindParam(':grado_tutor', $datosMaestro['grado_tutor']);
            $stmt->bindParam(':fecha_contratacion', $datosMaestro['fecha_contratacion']);
            $stmt->execute();

            $maestroId = $this->pdo->lastInsertId();

            $this->pdo->commit();
            return $maestroId;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creando maestro: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene todos los maestros
     * @return array
     */
    public function obtenerTodos() {
        try {
            $sql = "SELECT m.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, u.email, u.nombre_usuario
                    FROM maestros m
                    INNER JOIN personas p ON m.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.activo = 1
                    ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo maestros: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtiene maestro por ID
     * @param int $id
     * @return array|false
     */
    public function obtenerPorId($id) {
        try {
            $sql = "SELECT m.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, u.email, u.nombre_usuario
                    FROM maestros m
                    INNER JOIN personas p ON m.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE m.id = :id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo maestro: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene maestros que pueden ser tutores
     * @return array
     */
    public function obtenerMaestrosParaTutor() {
        try {
            $sql = "SELECT m.id, p.nombres, p.apellido_paterno, p.apellido_materno, m.especialidad
                    FROM maestros m
                    INNER JOIN personas p ON m.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.activo = 1
                    ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo maestros para tutor: " . $e->getMessage());
            return [];
        }
    }
}
?>
