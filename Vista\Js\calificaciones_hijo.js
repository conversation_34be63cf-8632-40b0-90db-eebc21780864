document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const studentSelect = document.getElementById("student-select")
  const periodSelect = document.getElementById("period-select")
  const monthSelect = document.getElementById("month-select")
  const downloadPdfBtn = document.getElementById("download-pdf-btn")
  const viewSummaryBtn = document.getElementById("view-summary-btn")
  const gradesSummaryModal = document.getElementById("grades-summary-modal")
  const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
  const printSummaryBtn = document.getElementById("print-summary-btn")
  const downloadSummaryBtn = document.getElementById("download-summary-btn")
  const attendanceTabs = document.querySelectorAll(".attendance-tab")

  // Cambiar estudiante seleccionado
  if (studentSelect) {
    studentSelect.addEventListener("change", () => {
      // Aquí iría la lógica para cargar los datos del estudiante seleccionado
      const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
      console.log(`Estudiante seleccionado: ${selectedStudent}`)

      // En una implementación real, aquí se cargarían los datos del estudiante seleccionado
      // mediante una petición AJAX o similar
    })
  }

  // Cambiar periodo académico
  if (periodSelect) {
    periodSelect.addEventListener("change", () => {
      const selectedPeriod = periodSelect.options[periodSelect.selectedIndex].text
      console.log(`Periodo seleccionado: ${selectedPeriod}`)

      // En una implementación real, aquí se cargarían las calificaciones del periodo seleccionado
      updateSummaryModalTitle(selectedPeriod)
    })
  }

  // Cambiar periodo para conducta
  const conductPeriodSelect = document.getElementById("conduct-period-select")
  if (conductPeriodSelect) {
    conductPeriodSelect.addEventListener("change", () => {
      const selectedPeriod = conductPeriodSelect.options[conductPeriodSelect.selectedIndex].text
      console.log(`Periodo de conducta seleccionado: ${selectedPeriod}`)

      // En una implementación real, aquí se cargarían los datos de conducta del periodo seleccionado
    })
  }

  // Cambiar mes para asistencia
  if (monthSelect) {
    monthSelect.addEventListener("change", () => {
      const selectedMonth = monthSelect.options[monthSelect.selectedIndex].text
      console.log(`Mes seleccionado: ${selectedMonth}`)

      // En una implementación real, aquí se cargaría el registro de asistencia del mes seleccionado
      updateAttendanceTabsContent(selectedMonth)
    })
  }

  // Mostrar detalles de calificación al hacer clic en una fila de la tabla
  const gradeRows = document.querySelectorAll(".grades-table tbody tr")
  if (gradeRows.length > 0) {
    gradeRows.forEach((row) => {
      row.addEventListener("click", () => {
        const courseName = row.querySelector(".course-info span").textContent
        const finalGrade = row.querySelector(".grade").textContent

        // En una implementación real, aquí se mostraría un modal con detalles de la calificación
        console.log(`Detalles de ${courseName}: ${finalGrade}`)
      })
    })
  }

  // Funcionalidad para las pestañas de asistencia
  if (attendanceTabs.length > 0) {
    attendanceTabs.forEach((tab) => {
      tab.addEventListener("click", () => {
        // Remover clase active de todas las pestañas
        attendanceTabs.forEach((t) => t.classList.remove("active"))

        // Agregar clase active a la pestaña seleccionada
        tab.classList.add("active")

        // Mostrar el contenido correspondiente
        const tabId = tab.getAttribute("data-tab")
        const tabContents = document.querySelectorAll(".attendance-tab-content")

        tabContents.forEach((content) => {
          content.classList.remove("active")
        })

        document.getElementById(`${tabId}-content`).classList.add("active")
      })
    })
  }

  // Actualizar fecha actual
  const dateElements = document.querySelectorAll(".current-date")
  if (dateElements.length > 0) {
    const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
    const today = new Date()
    dateElements.forEach((element) => {
      element.textContent = today.toLocaleDateString("es-ES", options)
    })
  }

  // Funcionalidad para descargar PDF de calificaciones
  if (downloadPdfBtn) {
    downloadPdfBtn.addEventListener("click", () => {
      // En una implementación real, aquí se generaría el PDF con las calificaciones
      // Para esta demostración, simplemente mostraremos un mensaje
      alert("Descargando boleta de calificaciones en PDF...")
      console.log("Iniciando descarga de boleta de calificaciones")

      // Simulación de descarga después de 2 segundos
      setTimeout(() => {
        console.log("Boleta de calificaciones descargada")
      }, 2000)
    })
  }

  // Funcionalidad para ver resumen de calificaciones
  if (viewSummaryBtn && gradesSummaryModal) {
    viewSummaryBtn.addEventListener("click", () => {
      // Mostrar el modal de resumen
      gradesSummaryModal.classList.add("active")
      document.body.style.overflow = "hidden" // Evitar scroll en el body
    })
  }

  // Cerrar modales
  if (modalCloseBtns.length > 0) {
    modalCloseBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        // Cerrar todos los modales
        const modals = document.querySelectorAll(".modal-overlay")
        modals.forEach((modal) => {
          modal.classList.remove("active")
        })
        document.body.style.overflow = "" // Restaurar scroll en el body
      })
    })
  }

  // Cerrar modal al hacer clic fuera del contenido
  if (gradesSummaryModal) {
    gradesSummaryModal.addEventListener("click", (e) => {
      if (e.target === gradesSummaryModal) {
        gradesSummaryModal.classList.remove("active")
        document.body.style.overflow = "" // Restaurar scroll en el body
      }
    })
  }

  // Funcionalidad para imprimir resumen
  if (printSummaryBtn) {
    printSummaryBtn.addEventListener("click", () => {
      window.print()
    })
  }

  // Funcionalidad para descargar resumen en PDF
  if (downloadSummaryBtn) {
    downloadSummaryBtn.addEventListener("click", () => {
      alert("Descargando resumen de calificaciones en PDF...")
      console.log("Iniciando descarga de resumen de calificaciones")

      // Simulación de descarga después de 2 segundos
      setTimeout(() => {
        console.log("Resumen de calificaciones descargado")
      }, 2000)
    })
  }

  // Función para actualizar el título del modal de resumen según el periodo seleccionado
  function updateSummaryModalTitle(period) {
    const modalTitle = document.querySelector("#grades-summary-modal .modal-header h3")
    if (modalTitle) {
      modalTitle.textContent = `Resumen de Calificaciones - ${period}`
    }

    const periodInfo = document.querySelector(".student-info-summary p:nth-child(3)")
    if (periodInfo) {
      let periodText = "1er Bimestre (Enero - Febrero 2025)"

      if (period.includes("2do")) {
        periodText = "2do Bimestre (Marzo - Abril 2025)"
      } else if (period.includes("3er")) {
        periodText = "3er Bimestre (Mayo - Junio 2025)"
      } else if (period.includes("4to")) {
        periodText = "4to Bimestre (Agosto - Octubre 2025)"
      } else if (period.includes("Final")) {
        periodText = "Calificación Final (Año Escolar 2025)"
      }

      periodInfo.textContent = `Periodo: ${periodText}`
    }
  }

  // Función para actualizar el contenido de las pestañas de asistencia según el mes seleccionado
  function updateAttendanceTabsContent(month) {
    // En una implementación real, aquí se cargarían los datos de asistencia del mes seleccionado
    // Para esta demostración, solo actualizamos los títulos
    const headerTitles = document.querySelectorAll(".attendance-list-header h3")
    headerTitles.forEach((title) => {
      if (title.textContent.includes("-")) {
        const prefix = title.textContent.split("-")[0].trim()
        title.textContent = `${prefix} - ${month} 2025`
      }
    })
  }
})

