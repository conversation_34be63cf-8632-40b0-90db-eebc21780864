document.addEventListener("DOMContentLoaded", () => {
  // Manejar clics en tareas clickeables
  const clickableTasks = document.querySelectorAll(".clickable-task")
  
  clickableTasks.forEach((task) => {
    task.addEventListener("click", (e) => {
      // Evitar que se abra el modal si se hace clic en los botones de acción
      if (e.target.closest(".task-actions")) {
        return
      }
      
      const taskId = task.getAttribute("data-task")
      if (taskId) {
        document.getElementById("task-view").classList.add("active")
      }
    })
  })

  // Manejar clics en botones de ver tarea
  const viewTaskButtons = document.querySelectorAll(".view-task-btn")
  
  viewTaskButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      e.stopPropagation()
      const taskId = button.getAttribute("data-task")
      if (taskId) {
        document.getElementById("task-view").classList.add("active")
      }
    })
  })

  // Cerrar modal de tarea
  const closeTaskBtn = document.getElementById("close-task-btn")
  if (closeTaskBtn) {
    closeTaskBtn.addEventListener("click", () => {
      document.getElementById("task-view").classList.remove("active")
    })
  }

  // Cerrar modal al hacer clic fuera del contenido
  const taskModal = document.getElementById("task-view")
  if (taskModal) {
    taskModal.addEventListener("click", (e) => {
      if (e.target === taskModal) {
        taskModal.classList.remove("active")
      }
    })
  }
})

// Función para manejar la selección de archivos en tareas
function handleTaskFileSelect(input) {
  const selectedFilesDiv = document.getElementById('task-selected-files')
  selectedFilesDiv.innerHTML = ''
  
  if (input.files.length > 0) {
    const file = input.files[0]
    const fileItem = document.createElement('div')
    fileItem.className = 'selected-file-item'
    fileItem.innerHTML = `
      <span class="material-icons">description</span>
      <span class="file-name">${file.name}</span>
      <span class="file-size">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
      <button type="button" class="remove-file-btn" onclick="removeTaskFile()">
        <span class="material-icons">close</span>
      </button>
    `
    selectedFilesDiv.appendChild(fileItem)
  }
}

// Función para remover archivo seleccionado en tareas
function removeTaskFile() {
  document.getElementById('task-file-input').value = ''
  document.getElementById('task-selected-files').innerHTML = ''
}
