<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Solicitudes de Preinscripción</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/admin.css">
  <link rel="stylesheet" href="./Css/admision_a.css">
  <!-- Librería para exportar a Excel -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
  <div class="plataforma-container">
    <!-- Menú lateral -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="./img/logo-escuela.svg" alt="Logo Escuela">
        </div>
        <button class="menu-toggle" id="menu-toggle">
          <span class="material-icons">menu</span>
        </button>
      </div>
      
      <div class="sidebar-content">
        <div class="user-info">
          <div class="user-avatar">
            <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
          </div>
          <div class="user-details">
            <h3>Admin Sistema</h3>
            <p>Administrador</p>
          </div>
        </div>
        
        <nav class="sidebar-menu">
          <ul>
            <li>
              <a href="inicio_a.html">
                <span class="material-icons">dashboard</span>
                <span>Inicio</span>
              </a>
            </li>
            <li>
              <a href="perfil_a.html">
                <span class="material-icons">person</span>
                <span>Perfil</span>
              </a>
            </li>
            <li>
              <a href="usuarios_a.html">
                <span class="material-icons">people</span>
                <span>Usuarios</span>
              </a>
            </li>
            <li>
              <a href="anuncios_admin.html">
                <span class="material-icons">campaign</span>
                <span>Anuncios</span>
              </a>
            </li>
            <li class="active">
              <a href="admision_a.html">
                <span class="material-icons">how_to_reg</span>
                <span>Solicitudes de Admisión</span>
              </a>
            </li>
            <li>
              <a href="configuracion_admin.html">
                <span class="material-icons">settings</span>
                <span>Configuración</span>
              </a>
            </li>
            <li class="separator"></li>
            <li>
              <a href="intranet.html">
                <span class="material-icons">logout</span>
                <span>Cerrar Sesión</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>
    
    <!-- Contenido principal -->
    <main class="main-content">
      <header class="content-header">
        <div class="header-left">
          <h1>Solicitudes de Preinscripción</h1>
          <p class="current-date">martes, 24 de junio de 2025</p>
        </div>
        <div class="header-right">
          <div class="notifications">
            <button class="notification-btn">
              <span class="material-icons">notifications</span>
              <span class="notification-badge">5</span>
            </button>
          </div>
        </div>
      </header>
      
      <div class="content-body">
        <!-- Búsqueda -->
        <section class="card search-card">
          <div class="card-header">
            <h2>Buscar Solicitudes</h2>
            <p>Busque solicitudes por nombre, DNI o correo electrónico</p>
          </div>
          <div class="card-body">
            <div class="search-container">
              <div class="search-group">
                <label for="search-input">Buscar solicitudes:</label>
                <div class="search-input-wrapper">
                  <span class="material-icons">search</span>
                  <input type="text" id="search-input" class="search-input" placeholder="Buscar por nombre, DNI o correo...">
                </div>
              </div>

              <div class="search-group nivel-filter">
                <label for="nivel-select">FILTRAR POR NIVEL:</label>
                <select id="nivel-select" class="filter-select">
                  <option value="todos">Todos los niveles</option>
                  <option value="inicial">Inicial</option>
                  <option value="primaria">Primaria</option>
                </select>
              </div>
            </div>
          </div>
        </section>
        
        <!-- Sección de resultados y exportar -->
        <section class="results-section">
          <div class="results-header">
            <div class="results-info">
              <h2>Solicitudes Pendientes de Preinscripción</h2>
              <div class="solicitudes-count">
                <span id="total-count">0</span> solicitudes pendientes encontradas
              </div>
            </div>
            <div class="results-actions">
              <button type="button" id="export-btn" class="btn-export">
                <span class="material-icons">file_download</span>
                <span>Exportar a Excel</span>
              </button>
            </div>
          </div>
        </section>

        <!-- Tabla de solicitudes -->
        <section class="card table-section">
          <div class="card-body">
            <div class="table-container">
              <table id="solicitudes-table" class="data-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Estudiante</th>
                    <th>DNI Estudiante</th>
                    <th>DNI Padre</th>
                    <th>Apoderado</th>
                    <th>Grado</th>
                    <th>Fecha</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="solicitudes-table-body">
                  <!-- Las filas se generarán dinámicamente con JavaScript -->
                </tbody>
              </table>
            </div>
            
            <!-- Paginación -->
            <div class="pagination-container">
              <div class="items-per-page">
                <label for="items-per-page">Mostrar:</label>
                <select id="items-per-page">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
              
              <div id="pagination" class="pagination">
                <!-- La paginación se generará dinámicamente con JavaScript -->
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>

  <!-- Modal de Detalles de Solicitud -->
  <div id="solicitud-modal" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Detalles de la Solicitud</h3>
        <button class="modal-close-btn">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="solicitud-details">
          <!-- Información básica -->
          <div class="detail-section">
            <h3>Información Básica</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">ID de Solicitud:</div>
                <div id="solicitud-id" class="detail-value">1234</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Fecha de Solicitud:</div>
                <div id="solicitud-fecha" class="detail-value">01/01/2023</div>
              </div>
            </div>
          </div>
          
          <!-- Información del estudiante -->
          <div class="detail-section">
            <h3>Información del Estudiante</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Nombre(s):</div>
                <div id="solicitud-nombre" class="detail-value">Juan</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Apellido Paterno:</div>
                <div id="solicitud-apellido-paterno" class="detail-value">Pérez</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Apellido Materno:</div>
                <div id="solicitud-apellido-materno" class="detail-value">González</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">DNI del Estudiante:</div>
                <div id="solicitud-dni-estudiante" class="detail-value">12345678</div>
              </div>
            </div>
          </div>
          
          <!-- Información del apoderado -->
          <div class="detail-section">
            <h3>Información del Apoderado</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">DNI del Padre:</div>
                <div id="solicitud-dni-padre" class="detail-value">87654321</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Correo Electrónico:</div>
                <div id="solicitud-email" class="detail-value"><EMAIL></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Teléfono:</div>
                <div id="solicitud-telefono" class="detail-value">991 663 041</div>
              </div>
            </div>
          </div>
          
          <!-- Información académica -->
          <div class="detail-section">
            <h3>Información Académica</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Grado de Interés:</div>
                <div id="solicitud-grado" class="detail-value">Inicial 4 años</div>
              </div>
            </div>
          </div>
          

        </div>
      </div>
      <div class="modal-footer">
        <div class="modal-actions">
          <button type="button" class="btn-secondary modal-close-btn">Cerrar</button>
          <button type="button" id="aprobar-solicitud-btn" class="btn-primary">
            <span class="material-icons">check_circle</span>
            <span>Aprobar Solicitud</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/admision_a.js"></script>
</body>
</html>