<?php
require_once '../Controlador/AuthController.php';

// Proteger la página - solo administradores
AuthController::proteger<PERSON><PERSON>na(['administrador']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Plataforma Educativa - Panel de Administración</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
<link rel="stylesheet" href="./Css/plataforma.css">
<link rel="stylesheet" href="./Css/admin.css">
<link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
<div class="plataforma-container">
  <!-- Menú lateral -->
  <aside class="sidebar">
    <div class="sidebar-header">
      <div class="logo">
        <img src="./img/logo-escuela.svg" alt="Logo Escuela">
      </div>
      <button class="menu-toggle" id="menu-toggle">
        <span class="material-icons">menu</span>
      </button>
    </div>
    
    <div class="sidebar-content">
      <div class="user-info">
        <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
          <?php if ($fotoPerfilUrl): ?>
            <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
          <?php endif; ?>
        </div>
        <div class="user-details">
          <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
          <p>Administrador</p>
        </div>
      </div>
      
      <nav class="sidebar-menu">
        <ul>
          <li class="active">
            <a href="inicio_a.php">
              <span class="material-icons">dashboard</span>
              <span>Inicio</span>
            </a>
          </li>
          <li>
            <a href="perfil_a.php">
              <span class="material-icons">person</span>
              <span>Perfil</span>
            </a>
          </li>
          <li>
            <a href="usuarios_a.html">
              <span class="material-icons">people</span>
              <span>Usuarios</span>
            </a>
          </li>
          <li>
            <a href="anuncios_admin.html">
              <span class="material-icons">campaign</span>
              <span>Anuncios</span>
            </a>
          </li>
          <li>
            <a href="admision_a.html">
              <span class="material-icons">how_to_reg</span>
              <span>Solicitudes de Admisión</span>
            </a>
          </li>
          <li>
            <a href="configuracion_admin.html">
              <span class="material-icons">settings</span>
              <span>Configuración</span>
            </a>
          </li>
          <li class="separator"></li>
          <li>
            <a href="../Controlador/AuthController.php?action=logout">
              <span class="material-icons">logout</span>
              <span>Cerrar Sesión</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </aside>
  
  <!-- Contenido principal -->
  <main class="main-content">
    <header class="content-header">
      <div class="header-left">
        <h1>Panel de Administración</h1>
        <p class="current-date">Lunes, 22 de marzo de 2025</p>
      </div>
      <div class="header-right">
        <div class="notifications">
          <button class="notification-btn">
            <span class="material-icons">notifications</span>
            <span class="notification-badge">5</span>
          </button>
        </div>
      </div>
    </header>
    
    <div class="content-body">
      <!-- Resumen de estadísticas -->
      <section class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon users">
            <span class="material-icons">people</span>
          </div>
          <div class="stat-details">
            <h3>1,245</h3>
            <p>Usuarios Totales</p>
          </div>
          <div class="stat-change positive">
            <span class="material-icons">trending_up</span>
            <span>+5.2%</span>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon students">
            <span class="material-icons">school</span>
          </div>
          <div class="stat-details">
            <h3>856</h3>
            <p>Estudiantes</p>
          </div>
          <div class="stat-change positive">
            <span class="material-icons">trending_up</span>
            <span>+3.7%</span>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon teachers">
            <span class="material-icons">assignment_ind</span>
          </div>
          <div class="stat-details">
            <h3>68</h3>
            <p>Profesores</p>
          </div>
          <div class="stat-change neutral">
            <span class="material-icons">trending_flat</span>
            <span>0%</span>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon admissions">
            <span class="material-icons">how_to_reg</span>
          </div>
          <div class="stat-details">
            <h3>24</h3>
            <p>Solicitudes Pendientes</p>
          </div>
          <div class="stat-change negative">
            <span class="material-icons">trending_down</span>
            <span>-12.3%</span>
          </div>
        </div>
      </section>
      
      <!-- Secciones principales -->
      <div class="dashboard-grid two-sections">
        <!-- Actividad reciente -->
        <section class="dashboard-section recent-activity">
          <div class="section-header">
            <h2>Actividad Reciente</h2>
            <button class="view-all-btn">Ver todo</button>
          </div>
          <div class="section-content">
            <div class="activity-list">
              <div class="activity-item">
                <div class="activity-icon">
                  <span class="material-icons">person_add</span>
                </div>
                <div class="activity-details">
                  <p><strong>Nuevo usuario registrado:</strong> María López (Estudiante)</p>
                  <span class="activity-time">Hace 35 minutos</span>
                </div>
              </div>
              
              <div class="activity-item">
                <div class="activity-icon">
                  <span class="material-icons">edit</span>
                </div>
                <div class="activity-details">
                  <p><strong>Perfil actualizado:</strong> Carlos García (Profesor)</p>
                  <span class="activity-time">Hace 1 hora</span>
                </div>
              </div>
              
              <div class="activity-item">
                <div class="activity-icon">
                  <span class="material-icons">how_to_reg</span>
                </div>
                <div class="activity-details">
                  <p><strong>Nueva solicitud de admisión:</strong> Familia Rodríguez</p>
                  <span class="activity-time">Hace 2 horas</span>
                </div>
              </div>
              
              <div class="activity-item">
                <div class="activity-icon">
                  <span class="material-icons">campaign</span>
                </div>
                <div class="activity-details">
                  <p><strong>Nuevo anuncio publicado:</strong> Reunión de padres</p>
                  <span class="activity-time">Hace 3 horas</span>
                </div>
              </div>
              
              <div class="activity-item">
                <div class="activity-icon">
                  <span class="material-icons">delete</span>
                </div>
                <div class="activity-details">
                  <p><strong>Usuario eliminado:</strong> Juan Pérez (Estudiante)</p>
                  <span class="activity-time">Hace 5 horas</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        

        <!-- Anuncios pendientes -->
        <section class="dashboard-section pending-announcements">
          <div class="section-header">
            <h2>Anuncios Pendientes</h2>
          </div>
          <div class="section-content">
            <div class="announcement-list">
              <div class="announcement-item">
                <div class="announcement-details">
                  <h3>Reunión de padres - Fin de trimestre</h3>
                  <p>Reunión general para informar sobre el cierre del primer trimestre académico.</p>
                  <div class="announcement-meta">
                    <span class="announcement-author">Por: Admin Sistema</span>
                    <span class="announcement-date">Programado: 25/03/2025</span>
                  </div>
                </div>
              </div>
              
              <div class="announcement-item">
                <div class="announcement-details">
                  <h3>Actualización de plataforma educativa</h3>
                  <p>Información sobre las nuevas funcionalidades implementadas en la plataforma.</p>
                  <div class="announcement-meta">
                    <span class="announcement-author">Por: Soporte Técnico</span>
                    <span class="announcement-date">Programado: 28/03/2025</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- Estudiantes recientes -->
        <section class="dashboard-section recent-users">
          <div class="section-header">
            <h2>Estudiantes Recientes</h2>
          </div>
          <div class="section-content">
            <div class="users-list">
              <div class="user-item">
                <div class="user-avatar">
                  <img src="/placeholder.svg?height=50&width=50" alt="María López">
                </div>
                <div class="user-details">
                  <h3>María López</h3>
                  <p data-role="Estudiante">Estudiante - 3° Primaria</p>
                  <span class="user-date">Registrado: 22/03/2025</span>
                </div>
              </div>

              <div class="user-item">
                <div class="user-avatar">
                  <img src="/placeholder.svg?height=50&width=50" alt="Carlos Mendoza">
                </div>
                <div class="user-details">
                  <h3>Carlos Mendoza</h3>
                  <p data-role="Estudiante">Estudiante - 5° Primaria</p>
                  <span class="user-date">Registrado: 21/03/2025</span>
                </div>
              </div>

              <div class="user-item">
                <div class="user-avatar">
                  <img src="/placeholder.svg?height=50&width=50" alt="Ana García">
                </div>
                <div class="user-details">
                  <h3>Ana García</h3>
                  <p data-role="Estudiante">Estudiante - 2° Primaria</p>
                  <span class="user-date">Registrado: 20/03/2025</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </main>
</div>

<script src="./Js/plataforma.js"></script>
<script src="./Js/inicio_a.js"></script>
</body>
</html>