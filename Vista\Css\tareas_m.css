/* Estilos específicos para la página de tareas (vista de maestros) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Sección de acciones */
  .section-actions {
    display: flex;
    gap: 15px;
    align-items: center;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    padding: 8px 15px 8px 40px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    width: 250px;
  }
  
  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
  }
  
  .action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background-color: #1e40af;
  }
  
  .create-task-btn {
    background-color: var(--success-color);
  }
  
  .create-task-btn:hover {
    background-color: #3d8b40;
  }
  
  /* Sección de tareas por semana */
  .week-tasks {
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    box-shadow: var(--shadow-sm);
  }
  
  .week-header {
    padding: 15px 20px;
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .week-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: var(--text-color);
  }
  
  .week-dates {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  /* Tarjetas de tareas */
  .task-card {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
  }
  
  .task-card:last-child {
    border-bottom: none;
  }
  
  .task-card:hover {
    background-color: var(--secondary-color);
  }

  /* Estilos específicos para exámenes */
  .task-card.exam-card {
    border-left: 4px solid #ff9800;
    background-color: #fff8f0;
  }

  .task-card.exam-card:hover {
    background-color: #fff3e0;
  }

  .task-title-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .exam-icon {
    color: #ff9800;
    font-size: 1.2rem;
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .task-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
  }
  
  .task-badge {
    font-size: 0.85rem;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 500;
  }
  
  .task-badge.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .task-badge.graded {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .task-details {
    margin-bottom: 15px;
  }
  
  .task-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .meta-item .material-icons {
    font-size: 1.1rem;
  }
  
  .task-description {
    font-size: 0.95rem;
    color: var(--text-color);
    margin: 0;
    line-height: 1.5;
  }
  
  .task-actions {
    display: flex;
    gap: 10px;
  }
  
  .task-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    flex: 1;
    border: none;
  }
  
  .task-action-btn.view-btn {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .task-action-btn.view-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .task-action-btn.submissions-btn {
    background-color: var(--primary-color);
    color: white;
  }
  
  .task-action-btn.submissions-btn:hover {
    background-color: #1e40af;
  }
  
  /* Modal de entregas */
  .submissions-container {
    max-width: 900px;
    width: 95%;
  }
  
  .submissions-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .stat-item {
    flex: 1;
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
  }
  
  .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .submissions-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .filter-group label {
    font-weight: 500;
    color: var(--text-color);
  }
  
  .filter-group select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
  }
  
  .submissions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .submission-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
  }
  
  .submission-item:hover {
    box-shadow: var(--shadow-md);
  }
  
  .student-info {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 30%;
  }
  
  .student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .student-avatar.large {
    width: 80px;
    height: 80px;
  }
  
  .student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .student-details {
    display: flex;
    flex-direction: column;
  }
  
  .student-name {
    font-weight: 500;
    margin-bottom: 3px;
  }
  
  .student-id {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .submission-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: 30%;
  }
  
  .submission-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    font-weight: 500;
  }
  
  .submission-status.on-time {
    color: var(--success-color);
  }
  
  .submission-status.late {
    color: var(--warning-color);
  }
  
  .submission-status.pending {
    color: var(--text-light);
  }
  
  .submission-date {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .submission-grade {
    display: flex;
    align-items: center;
    gap: 5px;
    width: 20%;
    font-weight: 600;
  }
  
  .grade-value {
    font-size: 1.1rem;
    color: var(--text-color);
  }
  
  .grade-max {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .submission-grade.pending {
    font-weight: normal;
    color: var(--text-light);
    font-size: 0.9rem;
  }
  
  .submission-actions {
    display: flex;
    justify-content: flex-end;
    width: 20%;
  }
  
  .submission-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--text-color);
  }
  
  .submission-action-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .submission-action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Modal de entrega individual */
  .submission-container {
    max-width: 800px;
    width: 95%;
  }
  
  .submission-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .student-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    min-width: 250px;
  }
  
  .student-profile h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 5px 0;
  }
  
  .student-profile p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .submission-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    flex: 2;
    min-width: 300px;
  }
  
  .submission-content {
    margin-bottom: 30px;
  }
  
  .submission-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--text-color);
  }
  
  .submission-files {
    margin-bottom: 20px;
  }
  
  .file-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    margin-bottom: 10px;
  }
  
  .file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .file-icon.pdf {
    background-color: #ffebee;
  }
  
  .file-icon.pdf .material-icons {
    color: #f44336;
  }
  
  .file-details {
    flex: 1;
  }
  
  .file-name {
    display: block;
    font-weight: 500;
    margin-bottom: 3px;
  }
  
  .file-size {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .file-actions {
    display: flex;
    gap: 8px;
  }
  
  .file-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .file-action-btn:hover {
    background-color: white;
  }
  
  .file-action-btn.download-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .file-action-btn.preview-btn:hover {
    color: var(--info-color);
    border-color: var(--info-color);
  }
  
  .submission-comments {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
  }
  
  .comment-text {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
  }
  
  .comment-text p {
    margin: 0 0 10px 0;
  }
  
  .comment-text p:last-child {
    margin-bottom: 0;
  }
  
  .grading-section {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
  }
  
  .grade-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .form-group label {
    font-weight: 500;
    color: var(--text-color);
  }
  
  .points-input {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .points-input input {
    width: 80px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    color: var(--text-color);
  }
  
  .points-max {
    font-size: 1rem;
    color: var(--text-light);
  }
  
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    resize: vertical;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 10px;
  }
  
  .btn-secondary,
  .btn-primary {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-color);
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .section-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  
    .search-input {
      width: 100%;
    }
  
    .submissions-stats {
      flex-wrap: wrap;
    }
  
    .stat-item {
      min-width: calc(50% - 10px);
    }
  
    .submissions-filters {
      flex-direction: column;
      gap: 10px;
    }
  
    .filter-group,
    .search-container {
      width: 100%;
    }
  
    .submission-item {
      flex-wrap: wrap;
    }
  
    .student-info,
    .submission-details,
    .submission-grade,
    .submission-actions {
      width: 100%;
    }
  
    .submission-actions {
      justify-content: flex-start;
      margin-top: 10px;
    }
  }
  
  @media (max-width: 768px) {
    .task-actions {
      flex-direction: column;
    }
  
    .stat-item {
      min-width: 100%;
    }
  
    .student-profile {
      flex-direction: column;
      text-align: center;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }

  /* Vista de tarea */
  .assignment-container {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
  }

  .assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px 10px 0 0;
  }

  .assignment-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
  }

  .assignment-title .material-icons {
    font-size: 1.8rem;
  }

  .grade-pending {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .assignment-content {
    padding: 0;
  }

  .assignment-section {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
  }

  .assignment-section:last-child {
    border-bottom: none;
  }

  .assignment-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .assignment-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
  }

  .assignment-image {
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .assignment-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .meta-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .meta-label .material-icons {
    font-size: 1.2rem;
    color: var(--primary-color);
  }

  .meta-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
  }

  .submission-section {
    padding: 0;
  }

  .submission-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .task-status-info {
    margin-bottom: 20px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }

  .status-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
  }

  .status-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .status-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .status-value {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
  }

  .submission-options {
    margin-top: 20px;
  }

