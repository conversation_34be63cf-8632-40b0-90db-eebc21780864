<?php
require_once __DIR__ . '/Conexion.php';
require_once __DIR__ . '/Usuario.php';

/**
 * Modelo para manejar administradores
 */
class Administrador {
    private $pdo;
    private $usuario;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->usuario = new Usuario();
    }

    /**
     * Crea un nuevo administrador
     * @param array $datosUsuario
     * @param array $datosPersona
     * @param array $datosAdministrador
     * @return int|false
     */
    public function crearAdministrador($datosUsuario, $datosPersona, $datosAdministrador) {
        try {
            $this->pdo->beginTransaction();

            // Crear usuario y persona
            $resultado = $this->usuario->crearUsuario($datosUsuario, $datosPersona);
            if (!$resultado) {
                throw new Exception("Error creando usuario base");
            }

            // Insertar datos específicos del administrador
            $sql = "INSERT INTO administradores (persona_id, cargo, departamento, fecha_contratacion) 
                    VALUES (:persona_id, :cargo, :departamento, :fecha_contratacion)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':persona_id', $resultado['persona_id']);
            $stmt->bindParam(':cargo', $datosAdministrador['cargo']);
            $stmt->bindParam(':departamento', $datosAdministrador['departamento']);
            $stmt->bindParam(':fecha_contratacion', $datosAdministrador['fecha_contratacion']);
            $stmt->execute();

            $administradorId = $this->pdo->lastInsertId();

            $this->pdo->commit();
            return $administradorId;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creando administrador: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene todos los administradores
     * @return array
     */
    public function obtenerTodos() {
        try {
            $sql = "SELECT a.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, u.email, u.nombre_usuario
                    FROM administradores a
                    INNER JOIN personas p ON a.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.activo = 1
                    ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo administradores: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtiene administrador por ID
     * @param int $id
     * @return array|false
     */
    public function obtenerPorId($id) {
        try {
            $sql = "SELECT a.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, u.email, u.nombre_usuario
                    FROM administradores a
                    INNER JOIN personas p ON a.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE a.id = :id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo administrador: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene administrador por usuario_id
     * @param int $usuarioId
     * @return array|false
     */
    public function obtenerPorUsuarioId($usuarioId) {
        try {
            $sql = "SELECT a.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, u.email, u.nombre_usuario
                    FROM administradores a
                    INNER JOIN personas p ON a.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.id = :usuario_id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo administrador por usuario: " . $e->getMessage());
            return false;
        }
    }
}
?>
