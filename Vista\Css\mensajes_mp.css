/* Estilos específicos para la sección de mensajes con padres (vista de maestros) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Eliminar los estilos para la navegación de mensajes */
  .messages-navigation {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
  }
  
  .messages-nav-tabs {
    display: flex;
    gap: 10px;
    padding: 0 20px;
  }
  
  .messages-tab {
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
  }
  
  .messages-tab:hover {
    color: var(--primary-color);
  }
  
  .messages-tab.active {
    color: var(--primary-color);
  }
  
  .messages-tab.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
  }
  
  /* Selector de estudiante */
  .student-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .student-selector label {
    font-weight: 500;
    color: var(--text-color);
  }
  
  .student-selector select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    min-width: 250px;
  }
  
  /* Contenedor de chat */
  .chat-container {
    display: flex;
    height: calc(100vh - 300px);
    min-height: 500px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  /* Lista de contactos */
  .contacts-list {
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    width: 350px;
    min-width: 350px;
    background-color: white;
  }
  
  .contacts-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .contacts-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }
  
  .search-box {
    display: flex;
    align-items: center;
    background-color: var(--secondary-color);
    border-radius: 20px;
    padding: 8px 15px;
  }
  
  .search-box .material-icons {
    color: var(--text-light);
    font-size: 1.2rem;
    margin-right: 8px;
  }
  
  .search-box input {
    background: none;
    border: none;
    outline: none;
    width: 100%;
    font-size: 0.9rem;
    color: var(--text-color);
  }
  
  .contacts-body {
    flex: 1;
    overflow-y: auto;
    height: calc(100% - 100px);
  }
  
  .contact-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
    background-color: white;
  }
  
  .contact-item:hover {
    background-color: var(--secondary-color);
  }
  
  .contact-item.active {
    background-color: var(--primary-light);
  }
  
  .contact-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
  }
  
  .contact-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .contact-info {
    flex: 1;
    min-width: 0;
  }
  
  .contact-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .contact-preview {
    font-size: 0.85rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .contact-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: 10px;
    flex-shrink: 0;
  }
  
  .contact-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 5px;
  }
  
  .contact-badge {
    background-color: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Área de chat */
  .chat-area {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }
  
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: white;
  }
  
  .chat-contact {
    display: flex;
    align-items: center;
  }
  
  .chat-contact .contact-avatar {
    margin-right: 15px;
  }
  
  .chat-contact .contact-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
  }
  
  .chat-contact .contact-status {
    font-size: 0.85rem;
    color: var(--success-color);
  }
  
  .chat-actions {
    display: flex;
    gap: 10px;
  }
  
  .chat-action-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .chat-action-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f5f7fb;
  }
  
  .chat-date {
    text-align: center;
    font-size: 0.85rem;
    color: var(--text-light);
    margin: 10px 0;
    position: relative;
  }
  
  .chat-date::before,
  .chat-date::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 70px;
    height: 1px;
    background-color: var(--border-color);
  }
  
  .chat-date::before {
    left: calc(50% - 100px);
  }
  
  .chat-date::after {
    right: calc(50% - 100px);
  }
  
  .message {
    display: flex;
    margin-bottom: 15px;
  }
  
  .message.received {
    justify-content: flex-start;
  }
  
  .message.sent {
    justify-content: flex-end;
  }
  
  .message-content {
    max-width: 70%;
  }
  
  .message-bubble {
    padding: 12px 15px;
    border-radius: 10px;
    font-size: 0.95rem;
    line-height: 1.5;
    position: relative;
  }
  
  .message.received .message-bubble {
    background-color: white;
    color: var(--text-color);
    border-top-left-radius: 0;
    box-shadow: var(--shadow-sm);
  }
  
  .message.sent .message-bubble {
    background-color: var(--primary-color);
    color: white;
    border-top-right-radius: 0;
  }
  
  .message-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 5px;
    text-align: right;
  }
  
  .message.sent .message-time {
    color: var(--text-light);
  }
  
  /* Mensaje con archivo */
  .file-message {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .file-message .material-icons {
    font-size: 2rem;
    color: inherit;
  }
  
  .file-info {
    flex: 1;
  }
  
  .file-name {
    font-weight: 500;
    margin-bottom: 3px;
  }
  
  .file-size {
    font-size: 0.8rem;
    opacity: 0.8;
  }
  
  .file-download-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
  }
  
  /* Área de entrada de chat */
  .chat-input-area {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: white;
    width: 100%;
  }
  
  .chat-tool-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .chat-tool-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .chat-input {
    flex: 1;
    margin: 0 15px;
  }
  
  .chat-input input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.95rem;
    color: var(--text-color);
    outline: none;
    transition: var(--transition);
  }
  
  .chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .chat-send-btn {
    background-color: var(--primary-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .chat-send-btn:hover {
    background-color: #1e40af;
  }
  
  /* Scroll para mensajes anteriores */
  .chat-messages {
    position: relative;
  }
  
  .scroll-indicator {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    opacity: 0.8;
    transition: var(--transition);
    z-index: 10;
  }
  
  .scroll-indicator:hover {
    opacity: 1;
    transform: scale(1.1);
  }
  
  .scroll-indicator .material-icons {
    font-size: 1.5rem;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .chat-container {
      flex-direction: column;
    }
  
    .contacts-list {
      width: 100%;
      min-width: 100%;
      height: 300px;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }
  
    .chat-area {
      height: calc(100% - 300px);
    }
  }
  
  @media (max-width: 768px) {
    .student-selector {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .student-selector select {
      width: 100%;
    }
  }
    