<?php
// Script simple para ejecutar el SQL de calificaciones de participación
require_once 'Modelo/conexion.php';

try {
    $pdo = Conexion::getConexion();
    
    // SQL para crear la tabla
    $sql = "
    CREATE TABLE IF NOT EXISTS calificaciones_participacion (
        id INT AUTO_INCREMENT PRIMARY KEY,
        estudiante_id INT NOT NULL,
        contenido_id INT NOT NULL,
        calificacion DECIMAL(4,2) NOT NULL DEFAULT 0.00,
        fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (estudiante_id) REFERENCES usuarios(id) ON DELETE CASCADE,
        FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
        
        INDEX idx_estudiante_contenido (estudiante_id, contenido_id),
        INDEX idx_contenido (contenido_id),
        INDEX idx_estudiante (estudiante_id),
        
        UNIQUE KEY unique_estudiante_contenido (estudiante_id, contenido_id),
        
        CHECK (calificacion >= 0 AND calificacion <= 20)
    );
    ";
    
    $pdo->exec($sql);
    echo "✅ Tabla 'calificaciones_participacion' creada exitosamente!";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
