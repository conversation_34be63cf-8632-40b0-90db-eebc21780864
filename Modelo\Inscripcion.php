<?php
require_once 'Conexion.php';

/**
 * Modelo para manejar inscripciones de estudiantes a cursos
 */
class Inscripcion {
    private $pdo;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
    }

    /**
     * Obtiene la conexión PDO
     * @return PDO
     */
    public function obtenerConexion() {
        return $this->pdo;
    }

    /**
     * Obtiene los estudiantes inscritos en un curso específico
     * @param int $cursoId
     * @return array
     */
    public function obtenerInscritos($cursoId) {
        $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil
                FROM inscripciones i
                INNER JOIN estudiantes e ON e.id = i.estudiante_id
                INNER JOIN personas p ON e.persona_id = p.id
                WHERE i.curso_id = :curso_id AND i.activo = 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Obtiene los estudiantes disponibles para inscribir en un curso
     * @param int $cursoId
     * @return array
     */
    public function obtenerDisponibles($cursoId) {
        $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil
                FROM estudiantes e
                INNER JOIN personas p ON e.persona_id = p.id
                INNER JOIN usuarios u ON p.usuario_id = u.id
                WHERE u.activo = 1 
                AND e.id NOT IN (
                    SELECT i.estudiante_id 
                    FROM inscripciones i 
                    WHERE i.curso_id = :curso_id AND i.activo = 1
                )
                ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Verifica si un estudiante está inscrito en un curso
     * @param int $cursoId
     * @param int $estudianteId
     * @return bool
     */
    public function estaInscrito($cursoId, $estudianteId) {
        $sql = "SELECT COUNT(*) FROM inscripciones 
                WHERE curso_id = :curso_id AND estudiante_id = :estudiante_id AND activo = 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmt->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Registra una nueva inscripción o reactiva una existente
     * @param int $cursoId
     * @param int $estudianteId
     * @return bool
     */
    public function agregar($cursoId, $estudianteId) {
        try {
            error_log("Modelo Inscripcion::agregar - Iniciando para curso $cursoId, estudiante $estudianteId");
            
            // Primero verificar si existe algún registro (activo o inactivo)
            $sqlCheck = "SELECT id, activo FROM inscripciones WHERE curso_id = :curso_id AND estudiante_id = :estudiante_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
            $stmtCheck->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
            $stmtCheck->execute();
            $registro = $stmtCheck->fetch();
            
            if ($registro) {
                if ($registro['activo'] == 1) {
                    error_log("El estudiante ya está inscrito y activo");
                    return false; // Ya está inscrito
                } else {
                    // Reactivar inscripción existente
                    error_log("Reactivando inscripción existente");
                    $sqlUpdate = "UPDATE inscripciones SET activo = 1, fecha_inscripcion = NOW() WHERE id = :id";
                    $stmtUpdate = $this->pdo->prepare($sqlUpdate);
                    $stmtUpdate->bindParam(':id', $registro['id'], PDO::PARAM_INT);
                    
                    $resultado = $stmtUpdate->execute();
                    
                    if ($resultado) {
                        error_log("Inscripción reactivada exitosamente");
                    } else {
                        error_log("Error al reactivar inscripción: " . json_encode($stmtUpdate->errorInfo()));
                    }
                    
                    return $resultado;
                }
            } else {
                // No existe registro, crear uno nuevo
                error_log("Creando nueva inscripción");
                $sqlInsert = "INSERT INTO inscripciones (curso_id, estudiante_id, fecha_inscripcion, activo)
                        VALUES (:curso_id, :estudiante_id, NOW(), 1)";
                $stmtInsert = $this->pdo->prepare($sqlInsert);
                $stmtInsert->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
                $stmtInsert->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
                
                $resultado = $stmtInsert->execute();
                
                if ($resultado) {
                    error_log("Nueva inscripción creada exitosamente");
                } else {
                    error_log("Error al crear nueva inscripción: " . json_encode($stmtInsert->errorInfo()));
                }
                
                return $resultado;
            }
        } catch (PDOException $e) {
            error_log("Excepción PDO en Inscripcion::agregar: " . $e->getMessage());
            error_log("Código de error: " . $e->getCode());
            return false;
        }
    }

    /**
     * Elimina un estudiante del curso (marca como inactivo)
     * @param int $cursoId
     * @param int $estudianteId
     * @return bool
     */
    public function eliminar($cursoId, $estudianteId) {
        $sql = "UPDATE inscripciones SET activo = 0 
                WHERE curso_id = :curso_id AND estudiante_id = :estudiante_id";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmt->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Obtiene el número de estudiantes inscritos en un curso
     * @param int $cursoId
     * @return int
     */
    public function contarInscritos($cursoId) {
        $sql = "SELECT COUNT(*) FROM inscripciones WHERE curso_id = :curso_id AND activo = 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmt->execute();
        return (int)$stmt->fetchColumn();
    }

    /**
     * Obtiene los cursos en los que está inscrito un estudiante
     * @param int $estudianteId
     * @return array
     */
    public function obtenerCursosPorEstudiante($estudianteId) {
        $sql = "SELECT c.*, i.fecha_inscripcion
                FROM inscripciones i
                INNER JOIN cursos c ON i.curso_id = c.id
                WHERE i.estudiante_id = :estudiante_id AND i.activo = 1 AND c.activo = 1
                ORDER BY c.nombre";
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
}
?>
