<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Asistencias</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/maestros.css">
    <link rel="stylesheet" href="./Css/asistencias_estudiantes.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Carlos García</h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_m.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #2196f3;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_m.html" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1>Matemáticas</h1>
                            <p>5° Primaria</p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <div class="schedule-day">
                                <span class="day-label">Lunes</span>
                                <span class="day-time">8:00 - 9:30</span>
                            </div>
                            <div class="schedule-day">
                                <span class="day-label">Miércoles</span>
                                <span class="day-time">8:00 - 9:30</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_m.html" class="course-tab">Contenido</a>
                    <a href="tareas_menu.html" class="course-tab">Tareas</a>
                    <a href="estudiantes_m.html" class="course-tab">Estudiantes</a>
                    <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                    <a href="asistencias_estudiantes.html" class="course-tab active">Asistencia</a>
                    <a href="mensajes_m.html" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de asistencias -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Registro de Asistencias</h2>
                    </div>
                    
                    <div class="attendance-controls">
                        <div class="date-selector">
                            <button class="date-nav-btn" id="prev-date">
                                <span class="material-icons">chevron_left</span>
                            </button>
                            <div class="current-date" id="current-date">
                                <span class="date-display">Lunes, 22 de marzo de 2025</span>
                                <button class="calendar-btn" id="open-calendar-btn">
                                    <span class="material-icons">calendar_today</span>
                                </button>
                            </div>
                            <button class="date-nav-btn" id="next-date">
                                <span class="material-icons">chevron_right</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="attendance-table-container">
                        <table class="attendance-table">
                            <thead>
                                <tr>
                                    <th class="student-col">Estudiante</th>
                                    <th class="status-col">Estado</th>
                                </tr>
                            </thead>
                            <tbody id="attendance-list">
                                <!-- Los estudiantes se cargarán dinámicamente con JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="attendance-actions">
                        <button class="btn-primary" id="save-attendance-btn">
                            <span class="material-icons">save</span>
                            Guardar asistencia
                        </button>
                        <button class="btn-secondary" id="reset-attendance-btn">
                            <span class="material-icons">refresh</span>
                            Restablecer
                        </button>
                    </div>

                    <div class="attendance-summary" style="margin-top: 30px;">
                        <div class="summary-item">
                            <div class="summary-icon present">
                                <span class="material-icons">check_circle</span>
                            </div>
                            <div class="summary-details">
                                <span class="summary-count" id="present-count">0</span>
                                <span class="summary-label">Presentes</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon absent">
                                <span class="material-icons">cancel</span>
                            </div>
                            <div class="summary-details">
                                <span class="summary-count" id="absent-count">0</span>
                                <span class="summary-label">Ausentes</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon late">
                                <span class="material-icons">schedule</span>
                            </div>
                            <div class="summary-details">
                                <span class="summary-count" id="late-count">0</span>
                                <span class="summary-label">Tardanzas</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon excused">
                                <span class="material-icons">assignment_turned_in</span>
                            </div>
                            <div class="summary-details">
                                <span class="summary-count" id="excused-count">0</span>
                                <span class="summary-label">Justificados</span>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Historial de asistencias -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Historial de Asistencias</h2>
                    </div>
                    
                    <div class="attendance-history">
                        <div class="history-filters">
                            <div class="filter-group">
                                <label for="month-filter">Mes:</label>
                                <select id="month-filter">
                                    <option value="3">Marzo 2025</option>
                                    <option value="4">Abril 2025</option>
                                    <option value="5">Mayo 2025</option>
                                    <option value="6">Junio 2025</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="student-filter">Estudiante:</label>
                                <select id="student-filter">
                                    <option value="all">Todos los estudiantes</option>
                                    <!-- Más opciones se cargarán dinámicamente -->
                                </select>
                            </div>
                        </div>
                        
                        <div class="history-calendar" id="attendance-calendar">
                            <!-- El calendario se generará dinámicamente con JavaScript -->
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para seleccionar fecha -->
    <div id="calendar-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>Seleccionar fecha</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="calendar-container" id="date-picker">
                    <!-- El calendario se generará dinámicamente -->
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="button" class="btn-primary" id="select-date-btn">Seleccionar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal para ver detalle de asistencia -->
    <div id="history-detail-modal" class="modal-overlay">
        <div class="modal-content history-detail-container">
            <div class="history-detail-header">
                <h3>Asistencia del día</h3>
            </div>
            <div class="history-detail-content">
                <!-- El contenido se generará dinámicamente -->
            </div>
            <div class="modal-actions">
                <button class="btn-secondary modal-close-btn">Cerrar</button>
            </div>
        </div>
    </div>
    
    <!-- Modal para cambiar estado de asistencia -->
    <div id="change-status-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cambiar estado de asistencia</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- El contenido se generará dinámicamente -->
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/asistencias_estudiantes.js"></script>
</body>
</html>

