document.addEventListener("DOMContentLoaded", () => {
  // Datos de las tareas calificadas
  const gradedTasks = {
    "sumas-restas": {
      title: "Sumas y restas",
      description: "Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.",
      grade: "15",
      dueDate: "23/03/2025, 12:00 AM",
      studentComment: "Profesor, tuve algunas dificultades con los ejercicios 5 y 6, pero creo que logré resolverlos correctamente. Espero su retroalimentación.",
      teacherComment: "Trabajo completo y bien desarrollado. Excelente comprensión de los conceptos básicos.",
      file: "Tarea_Sumas_Restas_Juan.pdf",
      image: "./Imagenes/ejercicios-sumas-restas.jpg"
    },
    "fracciones-basicas": {
      title: "Fracciones básicas",
      description: "Completa los ejercicios sobre fracciones equivalentes y simplificación del capítulo 4.",
      grade: "16.5",
      dueDate: "25/03/2025, 11:59 PM",
      studentComment: "Me gustó mucho este tema. Las fracciones equivalentes me parecieron fáciles de entender.",
      teacherComment: "Muy buen trabajo. Demuestra comprensión clara de las fracciones equivalentes.",
      file: "Tarea_Fracciones_Juan.pdf",
      image: "./Imagenes/ejercicios-fracciones.jpg"
    },
    "problemas-aplicacion": {
      title: "Problemas de aplicación",
      description: "Resuelve los problemas matemáticos aplicados a situaciones cotidianas de las páginas 30-32.",
      grade: "14",
      dueDate: "28/03/2025, 11:59 PM",
      studentComment: "Los problemas 3 y 4 me costaron mucho trabajo. No estaba seguro de cómo plantear las operaciones.",
      teacherComment: "Buen esfuerzo, pero necesita mejorar en la interpretación de problemas complejos.",
      file: "Tarea_Problemas_Juan.pdf",
      image: "./Imagenes/problemas-aplicacion.jpg"
    },
    "geometria-figuras": {
      title: "Geometría - Figuras planas",
      description: "Identifica y clasifica las diferentes figuras geométricas planas del ejercicio 5.1.",
      grade: "17",
      dueDate: "30/03/2025, 11:59 PM",
      studentComment: "Me encanta la geometría. Fue muy fácil identificar todas las figuras.",
      teacherComment: "Excelente identificación y clasificación de figuras. Muy ordenado y claro.",
      file: "Tarea_Geometria_Juan.pdf",
      image: "./Imagenes/figuras-geometricas.jpg"
    },
    "operaciones-combinadas": {
      title: "Operaciones combinadas",
      description: "Resuelve ejercicios que combinan suma, resta, multiplicación y división siguiendo el orden correcto.",
      grade: "13.5",
      dueDate: "02/04/2025, 11:59 PM",
      studentComment: "Me confundí con el orden de las operaciones en algunos ejercicios. Necesito practicar más.",
      teacherComment: "Necesita practicar más el orden de las operaciones. Revisar la jerarquía matemática.",
      file: "Tarea_Operaciones_Juan.pdf",
      image: "./Imagenes/operaciones-combinadas.jpg"
    },
    "medidas-longitud": {
      title: "Medidas de longitud",
      description: "Practica las conversiones entre diferentes unidades de medida de longitud del tema 6.",
      grade: "18",
      dueDate: "05/04/2025, 11:59 PM",
      studentComment: "Las conversiones me salieron muy bien. Usé la tabla que nos enseñó en clase.",
      teacherComment: "Excelente comprensión de las conversiones. Trabajo muy bien presentado.",
      file: "Tarea_Medidas_Juan.pdf",
      image: "./Imagenes/medidas-longitud.jpg"
    }
  }

  // Manejar clics en botones "Ver"
  const viewTaskButtons = document.querySelectorAll(".view-task-btn")
  
  viewTaskButtons.forEach((button, index) => {
    button.addEventListener("click", (e) => {
      e.stopPropagation()
      
      // Obtener la tarea basada en el índice
      const taskKeys = Object.keys(gradedTasks)
      const taskKey = taskKeys[index]
      const task = gradedTasks[taskKey]
      
      if (task) {
        showGradedTaskModal(task)
      }
    })
  })

  // Cerrar modal
  const closeModalBtn = document.getElementById("close-graded-task-modal")
  const modal = document.getElementById("graded-task-modal")
  
  if (closeModalBtn) {
    closeModalBtn.addEventListener("click", () => {
      modal.classList.remove("active")
    })
  }

  // Cerrar modal al hacer clic fuera del contenido
  if (modal) {
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.classList.remove("active")
      }
    })
  }

  // Función para mostrar el modal con los datos de la tarea
  function showGradedTaskModal(task) {
    document.getElementById("modal-task-title").textContent = `Tarea: ${task.title}`
    document.getElementById("modal-task-description").textContent = task.description
    document.getElementById("modal-grade-display").textContent = task.grade
    document.getElementById("modal-due-date").textContent = task.dueDate
    document.getElementById("modal-student-comment").textContent = task.studentComment
    document.getElementById("modal-teacher-comment").textContent = task.teacherComment
    document.getElementById("modal-file-name").textContent = task.file

    // Actualizar la imagen si existe
    const imageElement = document.getElementById("modal-task-image")
    if (imageElement && task.image) {
      imageElement.src = task.image
      imageElement.alt = `Ejercicios de ${task.title}`
    }

    // Mostrar el modal
    document.getElementById("graded-task-modal").classList.add("active")
  }

  // Manejar descarga de archivo (simulada)
  const downloadBtn = document.querySelector(".download-file-btn")
  if (downloadBtn) {
    downloadBtn.addEventListener("click", () => {
      alert("Descargando archivo... (Funcionalidad simulada)")
    })
  }
})
