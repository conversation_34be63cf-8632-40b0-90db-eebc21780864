document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const studentSelect = document.getElementById("student-select")
  const contactItems = document.querySelectorAll(".contact-item")
  const searchInput = document.querySelector(".search-box input")
  const chatInput = document.querySelector(".chat-input input")
  const sendButton = document.querySelector(".chat-send-btn")

  // Cambiar estudiante seleccionado
  if (studentSelect) {
    studentSelect.addEventListener("change", () => {
      // Aquí iría la lógica para cargar los contactos del estudiante seleccionado
      const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
      console.log(`Estudiante seleccionado: ${selectedStudent}`)
    })
  }

  // Seleccionar contacto
  if (contactItems.length > 0) {
    contactItems.forEach((item) => {
      item.addEventListener("click", () => {
        // Remover clase active de todos los contactos
        contactItems.forEach((contact) => {
          contact.classList.remove("active")
        })

        // Agregar clase active al contacto seleccionado
        item.classList.add("active")

        // Obtener nombre del contacto
        const contactName = item.querySelector(".contact-name").textContent

        // Actualizar encabezado del chat
        const chatContactName = document.querySelector(".chat-contact .contact-name")
        if (chatContactName) {
          chatContactName.textContent = contactName
        }

        // En una implementación real, aquí se cargarían los mensajes del contacto seleccionado
        console.log(`Contacto seleccionado: ${contactName}`)
      })
    })
  }

  // Buscar contactos
  if (searchInput) {
    searchInput.addEventListener("input", () => {
      const searchTerm = searchInput.value.toLowerCase()

      contactItems.forEach((item) => {
        const contactName = item.querySelector(".contact-name").textContent.toLowerCase()

        if (contactName.includes(searchTerm)) {
          item.style.display = "flex"
        } else {
          item.style.display = "none"
        }
      })
    })
  }

  // Función para enviar mensaje
  const sendMessage = () => {
    if (chatInput && chatInput.value.trim() !== "") {
      const messageText = chatInput.value.trim()
      const chatMessages = document.querySelector(".chat-messages")

      // Crear nuevo mensaje
      const messageHTML = `
        <div class="message sent">
          <div class="message-content">
            <div class="message-bubble">
              ${messageText}
            </div>
            <div class="message-time">${getCurrentTime()}</div>
          </div>
        </div>
      `

      // Agregar mensaje al chat
      if (chatMessages) {
        chatMessages.insertAdjacentHTML("beforeend", messageHTML)
        chatMessages.scrollTop = chatMessages.scrollHeight
      }

      // Limpiar input
      chatInput.value = ""

      // En una implementación real, aquí se enviaría el mensaje al servidor
      console.log(`Mensaje enviado: ${messageText}`)

      // Simular respuesta después de 1 segundo
      setTimeout(() => {
        const responseHTML = `
          <div class="message received">
            <div class="message-content">
              <div class="message-bubble">
                Gracias por su mensaje. Le responderé a la brevedad.
              </div>
              <div class="message-time">${getCurrentTime()}</div>
            </div>
          </div>
        `

        if (chatMessages) {
          chatMessages.insertAdjacentHTML("beforeend", responseHTML)
          chatMessages.scrollTop = chatMessages.scrollHeight
        }
      }, 1000)
    }
  }

  // Evento para enviar mensaje con el botón
  if (sendButton) {
    sendButton.addEventListener("click", sendMessage)
  }

  // Evento para enviar mensaje con Enter
  if (chatInput) {
    chatInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        sendMessage()
      }
    })
  }

  // Función para obtener la hora actual en formato HH:MM
  const getCurrentTime = () => {
    const now = new Date()
    const hours = now.getHours().toString().padStart(2, "0")
    const minutes = now.getMinutes().toString().padStart(2, "0")
    return `${hours}:${minutes}`
  }

  // Asegurar que todos los contactos sean visibles
  const fixContactsVisibility = () => {
    const contactsList = document.querySelector(".contacts-list")
    const contactsBody = document.querySelector(".contacts-body")

    if (contactsList && contactsBody) {
      // Asegurar que el contenedor tenga posición relativa
      contactsList.style.position = "relative"

      // Asegurar que el scroll funcione correctamente
      contactsBody.style.overflowY = "auto"
      contactsBody.style.maxHeight = "calc(100% - 80px)" // Restar altura del header

      console.log("Visibilidad de contactos ajustada")
    }
  }

  // Ejecutar al cargar la página
  fixContactsVisibility()

  // También ejecutar cuando cambie el tamaño de la ventana
  window.addEventListener("resize", fixContactsVisibility)
})

