// SISTEMA DE EMERGENCIA PARA CURSOS - SIN IMÁGENES
document.addEventListener("DOMContentLoaded", () => {
    console.log('=== SISTEMA DE EMERGENCIA ACTIVADO ===')
    
    // Elementos básicos
    const courseModal = document.getElementById("course-modal")
    const courseForm = document.getElementById("course-form")
    const courseIdInput = document.getElementById("course-id")
    const courseNameInput = document.getElementById("course-name")
    const courseGradeInput = document.getElementById("course-grade")
    const courseIconInput = document.getElementById("course-icon")
    const modalTitle = document.getElementById("modal-title")
    const createCourseBtn = document.getElementById("create-course-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const scheduleDayCheckboxes = document.querySelectorAll('input[name="schedule-day"]')
    
    console.log('Elementos críticos:', {
        modal: !!courseModal,
        form: !!courseForm,
        idInput: !!courseIdInput,
        nameInput: !!courseNameInput,
        gradeInput: !!courseGradeInput,
        iconInput: !!courseIconInput
    })

    // Función de alerta simple
    function alerta(mensaje) {
        alert(mensaje)
    }

    // Cargar datos del curso SIN IMÁGENES
    async function cargarCurso(courseId) {
        try {
            console.log('Cargando curso:', courseId)
            
            const response = await fetch(`../api_cursos.php?action=obtener_curso&id=${courseId}`)
            const data = await response.json()
            
            console.log('Datos recibidos:', data)
            
            if (data.success && data.curso) {
                const curso = data.curso
                
                // Llenar formulario
                if (courseIdInput) courseIdInput.value = curso.id
                if (courseNameInput) courseNameInput.value = curso.nombre
                if (courseGradeInput) courseGradeInput.value = curso.grado
                if (courseIconInput) courseIconInput.value = curso.icono || 'school'
                if (modalTitle) modalTitle.textContent = "Editar Curso"
                
                // Limpiar horarios
                scheduleDayCheckboxes.forEach(cb => {
                    cb.checked = false
                    const day = cb.value
                    const start = document.querySelector(`input[name="start-time-${day}"]`)
                    const end = document.querySelector(`input[name="end-time-${day}"]`)
                    if (start) start.disabled = true
                    if (end) end.disabled = true
                })
                
                // Cargar horarios
                if (curso.horarios_procesados) {
                    Object.keys(curso.horarios_procesados).forEach(dia => {
                        const cb = document.querySelector(`input[name="schedule-day"][value="${dia}"]`)
                        if (cb) {
                            cb.checked = true
                            const start = document.querySelector(`input[name="start-time-${dia}"]`)
                            const end = document.querySelector(`input[name="end-time-${dia}"]`)
                            if (start && end) {
                                start.disabled = false
                                end.disabled = false
                                const horario = curso.horarios_procesados[dia]
                                const [inicio, fin] = horario.split('-')
                                start.value = inicio
                                end.value = fin
                            }
                        }
                    })
                }
                
                console.log('Curso cargado exitosamente')
                return true
            } else {
                alerta('Error: ' + (data.error || 'No se pudo cargar el curso'))
                return false
            }
        } catch (error) {
            console.error('Error:', error)
            alerta('Error al cargar curso: ' + error.message)
            return false
        }
    }

    // Abrir modal para crear
    function crearCurso() {
        console.log('Abriendo modal para crear')
        if (courseForm) courseForm.reset()
        if (courseIdInput) courseIdInput.value = ""
        if (modalTitle) modalTitle.textContent = "Crear Nuevo Curso"
        
        // Limpiar horarios
        scheduleDayCheckboxes.forEach(cb => {
            cb.checked = false
            const day = cb.value
            const start = document.querySelector(`input[name="start-time-${day}"]`)
            const end = document.querySelector(`input[name="end-time-${day}"]`)
            if (start) start.disabled = true
            if (end) end.disabled = true
        })
        
        if (courseModal) {
            courseModal.classList.add("active")
            document.body.style.overflow = "hidden"
        }
    }

    // Event listeners
    if (createCourseBtn) {
        createCourseBtn.addEventListener("click", crearCurso)
    }

    // Cerrar modales
    modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", () => {
            document.querySelectorAll(".modal-overlay").forEach(m => {
                m.classList.remove("active")
            })
            document.body.style.overflow = ""
        })
    })

    // Cerrar al hacer clic fuera
    document.querySelectorAll(".modal-overlay").forEach(modal => {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.classList.remove("active")
                document.body.style.overflow = ""
            }
        })
    })

    // Manejar checkboxes
    scheduleDayCheckboxes.forEach(cb => {
        cb.addEventListener("change", () => {
            const day = cb.value
            const start = document.querySelector(`input[name="start-time-${day}"]`)
            const end = document.querySelector(`input[name="end-time-${day}"]`)
            
            if (start && end) {
                start.disabled = !cb.checked
                end.disabled = !cb.checked
                if (cb.checked) {
                    start.required = true
                    end.required = true
                } else {
                    start.required = false
                    end.required = false
                    start.value = ''
                    end.value = ''
                }
            }
        })
    })

    // BOTÓN DE EDITAR - FUNCIÓN PRINCIPAL
    document.addEventListener("click", async (e) => {
        if (e.target.closest('.edit-btn')) {
            e.preventDefault()
            e.stopPropagation()
            
            const btn = e.target.closest('.edit-btn')
            const courseId = btn.getAttribute("data-id")
            
            console.log('EDITAR CLICKEADO - Curso ID:', courseId)
            
            if (courseId) {
                const success = await cargarCurso(courseId)
                if (success && courseModal) {
                    courseModal.classList.add("active")
                    document.body.style.overflow = "hidden"
                    console.log('MODAL ABIERTO EXITOSAMENTE')
                    alerta('Modal de edición abierto correctamente')
                } else {
                    console.error('FALLO AL ABRIR MODAL')
                    alerta('Error: No se pudo abrir el modal de edición')
                }
            } else {
                console.error('ID NO ENCONTRADO')
                alerta('Error: ID de curso no encontrado')
            }
        }
    })

    // Enviar formulario
    if (courseForm) {
        courseForm.addEventListener("submit", async (e) => {
            e.preventDefault()
            
            const courseId = courseIdInput ? courseIdInput.value : ""
            const isNew = courseId === ""
            
            const formData = {
                nombre: courseNameInput ? courseNameInput.value : "",
                grado: courseGradeInput ? courseGradeInput.value : "",
                icono: courseIconInput ? courseIconInput.value : "school"
            }
            
            // Horarios
            const horarios = {}
            scheduleDayCheckboxes.forEach(cb => {
                if (cb.checked) {
                    const day = cb.value
                    const start = document.querySelector(`input[name="start-time-${day}"]`)
                    const end = document.querySelector(`input[name="end-time-${day}"]`)
                    
                    if (start && end && start.value && end.value) {
                        horarios[day] = {
                            activo: true,
                            inicio: start.value,
                            fin: end.value
                        }
                    }
                }
            })
            formData.horarios = horarios
            
            try {
                const url = isNew ? 
                    '../api_cursos.php?action=crear_curso' : 
                    '../api_cursos.php?action=actualizar_curso'
                
                if (!isNew) {
                    formData.id = courseId
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                })
                
                const data = await response.json()
                
                if (data.success) {
                    alerta(data.mensaje || `Curso ${isNew ? 'creado' : 'actualizado'} correctamente`)
                    
                    if (courseModal) {
                        courseModal.classList.remove("active")
                        document.body.style.overflow = ""
                    }
                    
                    window.location.reload()
                } else {
                    alerta(data.error || `Error al ${isNew ? 'crear' : 'actualizar'} curso`)
                }
            } catch (error) {
                console.error('Error:', error)
                alerta(`Error: ${error.message}`)
            }
        })
    }

    console.log('=== SISTEMA DE EMERGENCIA LISTO ===')
}) 