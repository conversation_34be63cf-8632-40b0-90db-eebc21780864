/* Estilos específicos para el panel de administración */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  
    /* Colores específicos para el panel de administración */
    --users-color: #2196f3;
    --students-color: #4caf50;
    --teachers-color: #ff9800;
    --admissions-color: #9c27b0;
  }
  
  /* Resumen de estadísticas */
  .stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
  }
  
  .stat-icon .material-icons {
    font-size: 2rem;
    color: white;
  }
  
  .stat-icon.users {
    background-color: var(--users-color);
  }
  
  .stat-icon.students {
    background-color: var(--students-color);
  }
  
  .stat-icon.teachers {
    background-color: var(--teachers-color);
  }
  
  .stat-icon.admissions {
    background-color: var(--admissions-color);
  }
  
  .stat-details {
    flex: 1;
  }
  
  .stat-details h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: var(--text-color);
  }
  
  .stat-details p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .stat-change {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 20px;
  }
  
  .stat-change.positive {
    color: var(--success-color);
    background-color: rgba(76, 175, 80, 0.1);
  }
  
  .stat-change.negative {
    color: var(--danger-color);
    background-color: rgba(244, 67, 54, 0.1);
  }
  
  .stat-change.neutral {
    color: var(--text-light);
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .stat-change .material-icons {
    font-size: 1.2rem;
    margin-right: 3px;
  }
  
  /* Grid del dashboard */
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 30px;
  }

  /* Layout especial para 2 secciones */
  .dashboard-grid.two-sections {
    grid-template-columns: 1fr;
    gap: 30px;
    margin: 30px 0 0;
    width: 100%;
  }

  .dashboard-grid.two-sections .dashboard-section {
    margin-bottom: 0;
  }
  
  /* Secciones del dashboard */
  .dashboard-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-bottom: 25px;
    transition: all 0.3s ease;
  }

  .dashboard-section:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
  
  .section-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
  }
  
  .view-all-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .view-all-btn:hover {
    text-decoration: underline;
  }
  
  .section-content {
    padding: 25px 30px;
    flex: 1;
    overflow-y: auto;
    max-height: 450px;
  }
  
  /* Actividad reciente */
  .activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .activity-item:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .activity-icon .material-icons {
    color: var(--primary-color);
    font-size: 1.2rem;
  }
  
  .activity-details {
    flex: 1;
  }
  
  .activity-details p {
    margin: 0 0 5px 0;
    font-size: 0.95rem;
    color: var(--text-color);
  }
  
  .activity-time {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  /* Solicitudes de admisión */
  .admission-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .admission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background-color: var(--secondary-color);
    transition: var(--transition);
  }
  
  .admission-item:hover {
    background-color: #edf2f7;
  }
  
  .admission-details {
    flex: 1;
  }
  
  .admission-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: var(--text-color);
  }
  
  .admission-details p {
    font-size: 0.95rem;
    color: var(--text-color);
    margin: 0 0 10px 0;
  }
  
  .admission-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.85rem;
  }
  
  .admission-date {
    color: var(--text-light);
  }
  
  .admission-status {
    padding: 3px 10px;
    border-radius: 20px;
    font-weight: 500;
  }
  
  .admission-status.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .admission-status.in-review {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
  }
  
  .admission-status.approved {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .admission-status.rejected {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .admission-actions {
    display: flex;
    gap: 8px;
  }
  
  .action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background-color: white;
  }
  
  .action-btn.view-btn:hover {
    color: var(--info-color);
    border-color: var(--info-color);
  }
  
  .action-btn.edit-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .action-btn.approve-btn:hover {
    color: var(--success-color);
    border-color: var(--success-color);
  }
  
  .action-btn.reject-btn:hover,
  .action-btn.delete-btn:hover {
    color: var(--danger-color);
    border-color: var(--danger-color);
  }
  
  .action-btn.publish-btn:hover {
    color: var(--success-color);
    border-color: var(--success-color);
  }
  
  /* Anuncios */
  .announcement-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .announcement-item {
    position: relative;
    padding: 25px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .announcement-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
  }

  .announcement-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: var(--primary-color);
  }

  .announcement-details {
    width: 100%;
  }

  .announcement-details h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    color: var(--text-color);
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .announcement-details h3::before {
    content: '📢';
    font-size: 1.1rem;
    opacity: 0.8;
  }

  .announcement-details p {
    font-size: 0.95rem;
    color: var(--text-light);
    margin: 0 0 15px 0;
    line-height: 1.5;
  }

  .announcement-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 0.85rem;
    color: var(--text-light);
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
  }

  .announcement-author,
  .announcement-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
  }

  .announcement-author::before {
    content: '👤';
    font-size: 0.9rem;
  }

  .announcement-date::before {
    content: '📅';
    font-size: 0.9rem;
  }

  .announcement-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    margin-top: 15px;
    justify-content: flex-end;
  }
  
  /* Usuarios recientes */
  .users-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .user-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }



  .user-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: var(--success-color);
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
    border: 3px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
  }

  .user-item:hover .user-avatar {
    border-color: var(--success-color);
    transform: scale(1.05);
  }

  .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }



  .user-details {
    flex: 1;
  }

  .user-details h3 {
    font-size: 1.05rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--text-color);
    line-height: 1.3;
  }

  .user-details p {
    font-size: 0.95rem;
    color: var(--text-light);
    margin: 0 0 8px 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .user-details p[data-role="Estudiante"]::before {
    content: '🎓';
    font-size: 0.9rem;
  }

  .user-details p[data-role="Profesor"]::before {
    content: '👨‍🏫';
  }

  .user-details p[data-role="Padre/Madre"]::before {
    content: '👨‍👩‍👧‍👦';
  }

  .user-date {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
  }

  .user-date::before {
    content: '📅';
    font-size: 0.9rem;
  }

  .user-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }
  
  /* Responsive */
  @media (max-width: 1200px) {
    .dashboard-grid {
      grid-template-columns: 1fr;
    }

    .dashboard-grid.two-sections {
      grid-template-columns: 1fr;
      gap: 25px;
    }

    .stats-overview {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .stats-overview {
      grid-template-columns: 1fr;
    }

    .dashboard-grid.two-sections {
      grid-template-columns: 1fr;
      gap: 20px;
      margin-top: 20px;
    }

    .section-header {
      padding: 15px 20px;
    }

    .section-content {
      padding: 20px;
    }

    .admission-item,
    .announcement-item,
    .user-item {
      padding: 20px;
    }

    .announcement-item::before {
      width: 100%;
      height: 4px;
      top: 0;
      left: 0;
    }

    .admission-actions,
    .announcement-actions,
    .user-actions {
      margin-top: 15px;
      align-self: flex-end;
    }

    .user-avatar {
      width: 50px;
      height: 50px;
      margin-right: 15px;
    }

    .announcement-details h3 {
      font-size: 1.1rem;
    }

    .user-details h3 {
      font-size: 1.05rem;
    }
  }
  
  @media (max-width: 576px) {
    .stat-card {
      flex-direction: column;
      text-align: center;
    }
  
    .stat-icon {
      margin-right: 0;
      margin-bottom: 15px;
    }
  
    .stat-change {
      margin-top: 10px;
    }
  }
    