document.addEventListener("DOMContentLoaded", function() {
    // Referencias a elementos del DOM
    const statusFilter = document.getElementById("status-filter");
    const typeFilter = document.getElementById("type-filter");
    const justificationCards = document.querySelectorAll(".justification-card");

    // Configurar event listeners
    setupEventListeners();

    function setupEventListeners() {
        // Filtros
        if (statusFilter) {
            statusFilter.addEventListener("change", filterJustifications);
        }
        
        if (typeFilter) {
            typeFilter.addEventListener("change", filterJustifications);
        }
    }

    // Función para filtrar justificaciones
    function filterJustifications() {
        const statusValue = statusFilter.value;
        const typeValue = typeFilter.value;

        justificationCards.forEach(card => {
            const cardStatus = card.getAttribute("data-status");
            const cardType = card.getAttribute("data-type");

            const statusMatch = statusValue === "all" || cardStatus === statusValue;
            const typeMatch = typeValue === "all" || cardType === typeValue;

            if (statusMatch && typeMatch) {
                card.style.display = "block";
            } else {
                card.style.display = "none";
            }
        });

        // Mostrar mensaje si no hay resultados
        updateNoResultsMessage();
    }

    // Función para mostrar mensaje cuando no hay resultados
    function updateNoResultsMessage() {
        const visibleCards = Array.from(justificationCards).filter(card => 
            card.style.display !== "none"
        );

        let noResultsMessage = document.querySelector(".no-results-message");

        if (visibleCards.length === 0) {
            if (!noResultsMessage) {
                noResultsMessage = document.createElement("div");
                noResultsMessage.className = "no-results-message";
                noResultsMessage.innerHTML = `
                    <div class="no-results-content">
                        <span class="material-icons">search_off</span>
                        <h3>No se encontraron solicitudes</h3>
                        <p>No hay solicitudes que coincidan con los filtros seleccionados.</p>
                    </div>
                `;
                document.querySelector(".justifications-container").appendChild(noResultsMessage);
            }
            noResultsMessage.style.display = "block";
        } else {
            if (noResultsMessage) {
                noResultsMessage.style.display = "none";
            }
        }
    }

    // Hacer funciones globales para uso desde HTML
    window.processJustification = processJustification;
    window.filterJustifications = filterJustifications;
});

// Función para procesar una justificación (aprobar o rechazar)
function processJustification(button, action) {
    const card = button.closest('.justification-card');
    const dateElement = card.querySelector('.date');
    const typeElement = card.querySelector('.type-badge');
    const statusElement = card.querySelector('.status-badge');
    const actionsSection = card.querySelector('.actions-section');
    
    if (!card || !dateElement || !typeElement || !statusElement) {
        console.error('No se pudieron encontrar los elementos necesarios');
        return;
    }
    
    const date = dateElement.textContent;
    const type = typeElement.textContent;
    
    // Confirmar la acción
    const actionText = action === 'aprobada' ? 'aprobar' : 'rechazar';
    const confirmMessage = `¿Está seguro de que desea ${actionText} la solicitud de ${type.toLowerCase()} del ${date}?`;
    
    if (confirm(confirmMessage)) {
        // Actualizar el estado visual
        statusElement.textContent = action === 'aprobada' ? 'Aprobada' : 'Rechazada';
        statusElement.className = `status-badge ${action}`;
        card.setAttribute('data-status', action);
        
        // Crear elemento de información procesada
        const processedDiv = document.createElement('div');
        processedDiv.className = `processed-info ${action === 'rechazada' ? 'rejected' : ''}`;
        
        const icon = document.createElement('span');
        icon.className = 'material-icons';
        icon.textContent = action === 'aprobada' ? 'check_circle' : 'cancel';
        
        const text = document.createElement('span');
        const currentDate = new Date().toLocaleDateString('es-ES');
        const actionText = action === 'aprobada' ? 'Aprobada' : 'Rechazada';
        
        if (action === 'aprobada') {
            text.textContent = `${actionText} el ${currentDate} por Administrador`;
        } else {
            text.textContent = `${actionText} el ${currentDate} - Motivo no válido`;
        }
        
        processedDiv.appendChild(icon);
        processedDiv.appendChild(text);
        
        // Reemplazar la sección de acciones
        actionsSection.replaceWith(processedDiv);
        
        // Mostrar mensaje de confirmación
        const message = action === 'aprobada' 
            ? `Solicitud aprobada correctamente. El estado de asistencia del maestro ha sido actualizado.`
            : `Solicitud rechazada. El estado de asistencia del maestro se mantiene sin cambios.`;
        
        showSuccessMessage(message);
        
        // Simular envío al servidor
        console.log(`Justificación ${action}:`, {
            date: date,
            type: type,
            action: action,
            processedDate: currentDate,
            processedBy: 'Administrador'
        });
        
        // Actualizar filtros si es necesario
        filterJustifications();
    }
}

// Función para mostrar mensajes de éxito
function showSuccessMessage(message) {
    // Remover mensaje anterior si existe
    const existingMessage = document.querySelector('.success-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Crear elemento de mensaje
    const messageDiv = document.createElement('div');
    messageDiv.className = 'success-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 20px 25px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        z-index: 10000;
        font-size: 1rem;
        font-weight: 600;
        max-width: 400px;
        animation: slideInRight 0.4s ease-out;
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    
    // Crear contenido del mensaje
    const messageContent = document.createElement('div');
    messageContent.style.cssText = `
        display: flex;
        align-items: center;
        gap: 12px;
    `;
    
    const icon = document.createElement('span');
    icon.className = 'material-icons';
    icon.textContent = 'check_circle';
    icon.style.fontSize = '1.4rem';
    
    const text = document.createElement('span');
    text.textContent = message;
    
    messageContent.appendChild(icon);
    messageContent.appendChild(text);
    messageDiv.appendChild(messageContent);
    
    // Agregar animación CSS si no existe
    if (!document.querySelector('#success-message-styles')) {
        const style = document.createElement('style');
        style.id = 'success-message-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            .no-results-message {
                text-align: center;
                padding: 60px 20px;
                background: white;
                border-radius: 16px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border: 1px solid #e9ecef;
            }
            
            .no-results-content .material-icons {
                font-size: 4rem;
                color: #dee2e6;
                margin-bottom: 20px;
            }
            
            .no-results-content h3 {
                margin: 0 0 10px 0;
                color: #6c757d;
                font-size: 1.4rem;
            }
            
            .no-results-content p {
                margin: 0;
                color: #adb5bd;
                font-size: 1rem;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Agregar al DOM
    document.body.appendChild(messageDiv);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        messageDiv.style.animation = 'slideOutRight 0.4s ease-out';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 400);
    }, 5000);
}

// Función para obtener parámetros de URL (para cargar datos específicos del maestro)
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Inicializar datos del maestro si se pasan por URL
function initializeTeacherData() {
    const teacherName = getUrlParameter('teacher');
    const teacherRole = getUrlParameter('role');
    const teacherGrade = getUrlParameter('grade');
    
    if (teacherName) {
        document.getElementById('teacher-name').textContent = teacherName;
    }
    
    if (teacherRole) {
        document.querySelector('.teacher-role').textContent = `Maestro - ${teacherRole}`;
    }
    
    if (teacherGrade) {
        document.querySelector('.teacher-grade').textContent = `Grado Tutor: ${teacherGrade}`;
    }
}

// Inicializar cuando se carga la página
document.addEventListener("DOMContentLoaded", function() {
    initializeTeacherData();
});
