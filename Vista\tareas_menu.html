<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Menú <PERSON></title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_menu.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                  </div>
                  <div class="user-details">
                      <h3>Carlos García</h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.html">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.html">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.html">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.html">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_m.html" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1>Matemáticas</h1>
                          <p>5° Primaria</p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <div class="schedule-day">
                              <span class="day-label">Lunes</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                          <div class="schedule-day">
                              <span class="day-label">Miércoles</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.html" class="course-tab">Contenido</a>
                  <a href="tareas_menu.html" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.html" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de opciones de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Gestión de Tareas</h2>
                      <p class="section-description">Seleccione el modo de visualización para gestionar las tareas del curso</p>
                  </div>
                  
                  <div class="view-options-container">
                      <div class="view-option-card" onclick="window.location.href='tareas_m.html'">
                          <div class="view-option-icon">
                              <span class="material-icons">view_agenda</span>
                          </div>
                          <div class="view-option-content">
                              <h3>Vista Estándar</h3>
                              <p>Visualiza las tareas organizadas por semanas con detalles completos de cada tarea.</p>
                              <ul class="view-features">
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Organización por semanas</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Detalles completos de cada tarea</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Gestión de entregas</span>
                                  </li>
                              </ul>
                              <button class="view-option-btn">
                                  <span>Seleccionar</span>
                                  <span class="material-icons">arrow_forward</span>
                              </button>
                          </div>
                      </div>
                      
                      <div class="view-option-card" onclick="window.location.href='tareas_crud.html'">
                          <div class="view-option-icon crud">
                              <span class="material-icons">table_view</span>
                          </div>
                          <div class="view-option-content">
                              <h3>Vista de Tabla CRUD</h3>
                              <p>Administra todas las tareas en formato de tabla con opciones para crear, editar y eliminar rápidamente.</p>
                              <ul class="view-features">
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Formato de tabla compacto</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Creación rápida de tareas</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Edición y eliminación en línea</span>
                                  </li>
                              </ul>
                              <button class="view-option-btn crud">
                                  <span>Seleccionar</span>
                                  <span class="material-icons">arrow_forward</span>
                              </button>
                          </div>
                      </div>
                  </div>
              </section>
          </div>
      </main>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/tareas_menu.js"></script>
</body>
</html>
