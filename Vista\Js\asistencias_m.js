document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const registerEntryBtn = document.getElementById("register-entry-btn")
    const registerExitBtn = document.getElementById("register-exit-btn")
    const statusValue = document.querySelector(".status-value")
    const currentTimeDisplay = document.querySelector(".current-time")
    const attendanceFilter = document.getElementById("attendance-filter")
    const attendanceRows = document.querySelectorAll(".attendance-row")
    const newRequestBtn = document.querySelector(".new-request-btn")
    const justificationModal = document.getElementById("justification-modal")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const justificationForm = document.querySelector(".justification-form")
  
    // Actualizar fecha actual
    const dateElements = document.querySelectorAll(".current-date")
    if (dateElements.length > 0) {
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      const today = new Date()
      dateElements.forEach((element) => {
        element.textContent = today.toLocaleDateString("es-ES", options)
      })
    }
  
    // Actualizar hora actual
    const updateCurrentTime = () => {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, "0")
      const minutes = now.getMinutes().toString().padStart(2, "0")
      const seconds = now.getSeconds().toString().padStart(2, "0")
  
      if (currentTimeDisplay) {
        currentTimeDisplay.textContent = `${hours}:${minutes}:${seconds}`
      }
    }
  
    // Actualizar la hora cada segundo
    updateCurrentTime()
    setInterval(updateCurrentTime, 1000)
  
    // Registrar entrada
    if (registerEntryBtn) {
      registerEntryBtn.addEventListener("click", () => {
        // En una implementación real, aquí se enviaría la información al servidor
        console.log("Registrando entrada...")
  
        // Actualizar la interfaz
        statusValue.textContent = "Entrada registrada"
        statusValue.classList.add("registered")
  
        // Cambiar el estado de los botones
        registerEntryBtn.disabled = true
        registerExitBtn.disabled = false
  
        // Mostrar mensaje de confirmación
        alert("Entrada registrada correctamente a las " + currentTimeDisplay.textContent)
      })
    }
  
    // Registrar salida
    if (registerExitBtn) {
      registerExitBtn.addEventListener("click", () => {
        // En una implementación real, aquí se enviaría la información al servidor
        console.log("Registrando salida...")
  
        // Actualizar la interfaz
        statusValue.textContent = "Salida registrada"
        statusValue.classList.add("registered")
  
        // Cambiar el estado de los botones
        registerExitBtn.disabled = true
  
        // Mostrar mensaje de confirmación
        alert("Salida registrada correctamente a las " + currentTimeDisplay.textContent)
      })
    }
  
    // Filtrar registros de asistencia
    if (attendanceFilter) {
      attendanceFilter.addEventListener("change", () => {
        const filterValue = attendanceFilter.value
  
        attendanceRows.forEach((row) => {
          if (filterValue === "all") {
            row.style.display = "table-row"
          } else {
            if (row.classList.contains(filterValue)) {
              row.style.display = "table-row"
            } else {
              row.style.display = "none"
            }
          }
        })
      })
    }
  
    // Abrir modal de nueva solicitud
    if (newRequestBtn && justificationModal) {
      newRequestBtn.addEventListener("click", () => {
        justificationModal.classList.add("active")
        document.body.style.overflow = "hidden" // Evitar scroll en el body
      })
    }
  
    // Cerrar modal
    if (modalCloseBtns.length > 0) {
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          if (justificationModal) {
            justificationModal.classList.remove("active")
            document.body.style.overflow = "" // Restaurar scroll en el body
          }
        })
      })
    }
  
    // Cerrar modal al hacer clic fuera del contenido
    if (justificationModal) {
      justificationModal.addEventListener("click", (e) => {
        if (e.target === justificationModal) {
          justificationModal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
      })
    }
  
    // Enviar formulario de justificación
    if (justificationForm) {
      justificationForm.addEventListener("submit", (e) => {
        e.preventDefault()
  
        // Obtener los valores del formulario
        const date = document.getElementById("justification-date").value
        const type = document.getElementById("justification-type").value
        const reason = document.getElementById("justification-reason").value
  
        // En una implementación real, aquí se enviaría la información al servidor
        console.log("Enviando solicitud de justificación:", { date, type, reason })
  
        // Mostrar mensaje de confirmación
        alert("Solicitud de justificación enviada correctamente")
  
        // Cerrar el modal
        if (justificationModal) {
          justificationModal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
  
        // Limpiar el formulario
        justificationForm.reset()
      })
    }
  
    // Funcionalidad para los botones de paginación
    const paginationBtns = document.querySelectorAll(".pagination-btn.page-number")
    if (paginationBtns.length > 0) {
      paginationBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          // Remover clase active de todos los botones
          paginationBtns.forEach((b) => b.classList.remove("active"))
  
          // Agregar clase active al botón seleccionado
          btn.classList.add("active")
  
          // En una implementación real, aquí se cargarían los registros de la página seleccionada
          console.log(`Página seleccionada: ${btn.textContent}`)
        })
      })
    }
  })
  