:root {
  --primary-color: #ff4500;
  --secondary-color: #333;
  --accent-color: #f8f9fa;
  --text-color: #333;
  --white: #ffffff;
}

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 20px;
}

.logo img {
  height: 70px;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 3rem;
  margin-left: auto;
}

.nav-link {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s;
  font-size: 15px;
  letter-spacing: 0.5px;
  position: relative;
}

.nav-link:not(:last-child)::after {
  content: "|";
  position: absolute;
  right: -1.5rem;
  color: #ddd;
}

.nav-link.preinscripcion {
  color: #ff4500;
  font-weight: 600;
}

.nav-link.intranet {
  background: transparent;
  color: inherit;
  padding: 0;
}

.nav-link.intranet:hover {
  background: transparent;
  color: var(--primary-color);
}

.menu-toggle {
  display: none;
}

/* Hero Section Styles */
.hero {
  position: relative;
  height: 100vh;
  background: var(--accent-color);
  overflow: hidden;
  padding-top: 100px;
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  width: 100%;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Slider Styles */
.slider-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.slider {
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 5s ease-in-out;
}

.slide.active {
  opacity: 1;
}

.slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-content {
  position: absolute;
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 2;
}

.cta-button {
  display: inline-block;
  padding: 1rem 2rem;
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #e63e00;
}

/* Nuevos estilos para los botones de navegación */
.slider-arrows {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 3;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none; /* Permite que los clics pasen a través del contenedor */
}

.arrow-btn {
  background: rgba(255, 255, 255, 0.7);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  pointer-events: auto; /* Asegura que los botones reciban clics */
}

.arrow-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

.arrow-btn:active {
  transform: scale(0.95);
}

.arrow-btn .material-icons {
  font-size: 36px;
  color: var(--primary-color);
}

/* Educational Levels Styles */
.educational-levels {
  padding: 5rem 0;
  background: var(--white);
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.level-card {
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.level-card:hover {
  transform: translateY(-5px);
}

.level-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.level-content {
  padding: 1.5rem;
  text-align: center;
}

.level-content h2 {
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.level-content p {
  margin-bottom: 1.5rem;
  color: #666;
}

.level-button {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.level-button:hover {
  background-color: #e63e00;
}

/* Footer Styles */
.footer {
  background: var(--secondary-color);
  color: var(--white);
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Ajuste para la sección "Síguenos" */
.footer-section:last-child {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.footer-section h3 {
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: var(--white);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  color: var(--white);
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header .container {
    padding: 1rem 20px;
  }

  .logo img {
    height: 50px;
  }

  .menu-toggle {
    display: block;
    background: none;
    border: none;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    color: var(--secondary-color);
    cursor: pointer;
  }

  .menu-toggle .material-icons {
    font-size: 2rem;
  }

  .nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
  }

  .nav-overlay.active {
    display: block;
  }

  .nav-list {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    flex-direction: column;
    padding: 80px 40px;
    z-index: 999;
    justify-content: flex-start;
    gap: 2rem;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .nav-list.active {
    display: flex;
    transform: translateX(0);
  }

  .nav-list .nav-link {
    color: var(--white);
    font-size: 1.2rem;
    padding: 1rem 0;
  }

  .nav-link.intranet {
    background: transparent;
    border: 1px solid var(--white);
    text-align: center;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .menu-toggle.active {
    color: var(--white);
  }
  
  /* Estilos responsivos para los botones de navegación */
  .arrow-btn {
    width: 40px;
    height: 40px;
  }
  
  .arrow-btn .material-icons {
    font-size: 28px;
  }
  
  /* Ajuste para la sección "Síguenos" en móvil */
  .footer-section:last-child {
    margin-top: 30px;
    align-items: center;
  }
}

/* Estilos para el logo con enlace */
.logo a {
  display: block;
}

/* Estilos mejorados para los iconos de redes sociales */
.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  color: var(--white);
  font-size: 1.5rem;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Ajustes para el icono SVG de WhatsApp */
.whatsapp-icon svg {
  width: 20px;
  height: 20px;
}

/* Color específico para WhatsApp */
.social-links a[aria-label="WhatsApp"]:hover {
  background-color: #25D366;
  color: white;
}

/* Color específico para Facebook */
.social-links a[aria-label="Facebook"]:hover {
  background-color: #1877F2;
  color: white;
}

/* Ajuste para la distribución del footer en pantallas grandes */
@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 1fr 0.8fr;
  }
  
  .footer-section:last-child {
    justify-self: end;
    margin-left: 30px;
  }
}
