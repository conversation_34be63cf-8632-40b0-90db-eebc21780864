<?php
require_once '../Controlador/AuthController.php';
require_once '../Controlador/PerfilController.php';

// Proteger la página - solo administradores
AuthController::protegerPagina(['administrador']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);

// Obtener datos completos del perfil
$perfilController = new PerfilController();
$datosPerfil = $perfilController->obtenerPerfilCompleto();
$perfil = $datosPerfil['success'] ? $datosPerfil : null;

// Validar que los datos básicos existan
if ($perfil && !isset($perfil['datos_basicos'])) {
    $perfil = null;
}

// Datos básicos del perfil
$datosBasicos = $perfil['datos_basicos'] ?? [];
$edad = $perfil['edad'] ?? null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// URL de la foto de perfil
$fotoPerfilUrl = !empty($datosBasicos['nombre_foto']) ? "../Controlador/ImagenController.php?usuario_id=" . $usuarioActual['id'] : null;
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Perfil Administrador</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/admin.css">
  <link rel="stylesheet" href="./Css/perfil_a.css">
  <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
  <div class="plataforma-container">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                      <?php if ($fotoPerfilUrl): ?>
                          <img src="../Controlador/ImagenController.php?usuario_id=<?php echo $usuarioActual['id']; ?>" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                      <p>Administrador</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_a.php">
                              <span class="material-icons">dashboard</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="perfil_a.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li>
                          <a href="usuarios_a.html">
                              <span class="material-icons">people</span>
                              <span>Usuarios</span>
                          </a>
                      </li>
                      <li>
                          <a href="crear_anuncio.html">
                              <span class="material-icons">campaign</span>
                              <span>Anuncios</span>
                          </a>
                      </li>
                      <li>
                          <a href="admision_a.html">
                              <span class="material-icons">how_to_reg</span>
                              <span>Solicitudes de Admisión</span>
                          </a>
                      </li>
                      <li>
                          <a href="configuracion_a.html">
                              <span class="material-icons">settings</span>
                              <span>Configuración</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="../Controlador/AuthController.php?action=logout">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <header class="content-header">
              <div class="header-left">
                  <h1>Mi Perfil</h1>
                  <p class="current-date"><?php echo date('l, j \d\e F \d\e Y'); ?></p>
              </div>
              <div class="header-right">
                  <div class="notifications">
                      <button class="notification-btn">
                          <span class="material-icons">notifications</span>
                          <span class="notification-badge">5</span>
                      </button>
                  </div>
              </div>
          </header>
          
          <div class="content-body">
              <div class="profile-container">
                  <!-- Sección de perfil principal -->
                  <section class="profile-main">
                      <div class="profile-header">
                          <div class="profile-avatar-container">
                              <div class="profile-avatar">
                                  <?php if ($fotoPerfilUrl): ?>
                                      <img src="../Controlador/ImagenController.php?usuario_id=<?php echo $usuarioActual['id']; ?>" alt="Foto de perfil">
                                  <?php else: ?>
                                      <div class="default-avatar admin" data-initials="<?php echo htmlspecialchars($iniciales); ?>"></div>
                                  <?php endif; ?>
                              </div>
                              <button class="change-avatar-btn" id="change-avatar-btn">
                                  <span class="material-icons">photo_camera</span>
                                  <span>Cambiar foto</span>
                              </button>
                              <input type="file" id="foto-input" accept="image/*" style="display: none;">
                          </div>
                          <div class="profile-info">
                              <h2><?php echo htmlspecialchars($nombreCompleto); ?></h2>
                              <p class="profile-role">Administrador del Sistema</p>
                              <?php if ($perfil && isset($perfil['informacion_rol']['cargo'])): ?>
                                  <p class="profile-grade"><?php echo htmlspecialchars($perfil['informacion_rol']['cargo']); ?></p>
                              <?php endif; ?>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de información personal -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">person</span>
                              Información Personal
                          </h3>
                          <button class="edit-section-btn" data-section="personal">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre completo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($nombreCompleto); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Fecha de nacimiento</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($datosBasicos['fecha_nacimiento']) {
                                          echo date('d/m/Y', strtotime($datosBasicos['fecha_nacimiento']));
                                      } else {
                                          echo 'No especificada';
                                      }
                                      ?>
                                  </div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Edad</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($edad) {
                                          echo $edad . ' años';
                                      } else {
                                          echo 'No especificada';
                                      }
                                      ?>
                                  </div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Sexo</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($datosBasicos['sexo']) {
                                          echo ucfirst($datosBasicos['sexo']);
                                      } else {
                                          echo 'No especificado';
                                      }
                                      ?>
                                  </div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Dirección</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($datosBasicos['direccion']) {
                                          echo htmlspecialchars($datosBasicos['direccion']);
                                      } else {
                                          echo 'No especificada';
                                      }
                                      ?>
                                  </div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($datosBasicos['telefono']) {
                                          echo htmlspecialchars($datosBasicos['telefono']);
                                      } else {
                                          echo 'No especificado';
                                      }
                                      ?>
                                  </div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">DNI</div>
                                  <div class="info-value">
                                      <?php 
                                      if ($datosBasicos['dni']) {
                                          echo htmlspecialchars($datosBasicos['dni']);
                                      } else {
                                          echo 'No especificado';
                                      }
                                      ?>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de cuenta -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">account_circle</span>
                              Información de Cuenta
                          </h3>
                          <button class="edit-section-btn" data-section="account">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre de usuario</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Correo electrónico</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['email']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Rol</div>
                                  <div class="info-value">Administrador</div>
                              </div>
                              <?php if ($perfil && isset($perfil['informacion_rol']['cargo'])): ?>
                              <div class="info-item">
                                  <div class="info-label">Cargo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($perfil['informacion_rol']['cargo']); ?></div>
                              </div>
                              <?php endif; ?>
                              <?php if ($perfil && isset($perfil['informacion_rol']['departamento'])): ?>
                              <div class="info-item">
                                  <div class="info-label">Departamento</div>
                                  <div class="info-value"><?php echo htmlspecialchars($perfil['informacion_rol']['departamento']); ?></div>
                              </div>
                              <?php endif; ?>
                              <?php if ($perfil && isset($perfil['informacion_rol']['fecha_contratacion'])): ?>
                              <div class="info-item">
                                  <div class="info-label">Fecha de contratación</div>
                                  <div class="info-value"><?php echo date('d/m/Y', strtotime($perfil['informacion_rol']['fecha_contratacion'])); ?></div>
                              </div>
                              <?php endif; ?>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de seguridad -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">security</span>
                              Seguridad
                          </h3>
                      </div>
                      
                      <div class="section-content">
                          <div class="security-actions">
                              <button class="change-password-btn">
                                  <span class="material-icons">lock</span>
                                  <span>Cambiar contraseña</span>
                              </button>
                          </div>
                      </div>
                  </section>
              </div>
          </div>
      </main>
  </div>

  <!-- Modal para editar información personal -->
  <div class="modal-overlay" id="edit-personal-modal">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información Personal</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <form class="edit-form" id="personal-form">
              <div class="form-group">
                  <label for="nombres">Nombres</label>
                  <input type="text" id="nombres" name="nombres" value="<?php echo htmlspecialchars($datosBasicos['nombres'] ?? ''); ?>" required>
              </div>
              <div class="form-group">
                  <label for="apellido_paterno">Apellido Paterno</label>
                  <input type="text" id="apellido_paterno" name="apellido_paterno" value="<?php echo htmlspecialchars($datosBasicos['apellido_paterno'] ?? ''); ?>" required>
              </div>
              <div class="form-group">
                  <label for="apellido_materno">Apellido Materno</label>
                  <input type="text" id="apellido_materno" name="apellido_materno" value="<?php echo htmlspecialchars($datosBasicos['apellido_materno'] ?? ''); ?>" required>
              </div>
              <div class="form-group">
                  <label for="dni">DNI</label>
                  <input type="text" id="dni" name="dni" value="<?php echo htmlspecialchars($datosBasicos['dni'] ?? ''); ?>" maxlength="8">
              </div>
              <div class="form-group">
                  <label for="fecha_nacimiento">Fecha de Nacimiento</label>
                  <input type="date" id="fecha_nacimiento" name="fecha_nacimiento" value="<?php echo $datosBasicos['fecha_nacimiento'] ?? ''; ?>">
              </div>
              <div class="form-group">
                  <label for="sexo">Sexo</label>
                  <select id="sexo" name="sexo">
                      <option value="">Seleccionar</option>
                      <option value="masculino" <?php echo ($datosBasicos['sexo'] ?? '') === 'masculino' ? 'selected' : ''; ?>>Masculino</option>
                      <option value="femenino" <?php echo ($datosBasicos['sexo'] ?? '') === 'femenino' ? 'selected' : ''; ?>>Femenino</option>
                  </select>
              </div>
              <div class="form-group">
                  <label for="direccion">Dirección</label>
                  <textarea id="direccion" name="direccion"><?php echo htmlspecialchars($datosBasicos['direccion'] ?? ''); ?></textarea>
              </div>
              <div class="form-group">
                  <label for="telefono">Teléfono</label>
                  <input type="tel" id="telefono" name="telefono" value="<?php echo htmlspecialchars($datosBasicos['telefono'] ?? ''); ?>">
              </div>
              <div class="form-actions">
                  <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                  <button type="submit" class="btn-primary">Guardar Cambios</button>
              </div>
          </form>
      </div>
  </div>

  <!-- Modal para editar información de cuenta -->
  <div class="modal-overlay" id="edit-account-modal">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información de Cuenta</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <form class="edit-form" id="account-form">
              <div class="form-group">
                  <label for="email">Correo Electrónico</label>
                  <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($usuarioActual['email']); ?>" required>
              </div>
              <div class="form-group">
                  <label for="nombre_usuario">Nombre de usuario</label>
                  <input type="text" id="nombre_usuario" name="nombre_usuario" value="<?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?>" required>
              </div>
              <div class="form-actions">
                  <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                  <button type="submit" class="btn-primary">Guardar Cambios</button>
              </div>
          </form>
      </div>
  </div>

  <!-- Modal para cambiar contraseña -->
  <div class="modal-overlay" id="change-password-modal">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Cambiar Contraseña</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <form class="edit-form" id="password-form">
              <div class="form-group">
                  <label for="password_actual">Contraseña Actual</label>
                  <input type="password" id="password_actual" name="password_actual" required>
              </div>
              <div class="form-group">
                  <label for="password_nuevo">Nueva Contraseña</label>
                  <input type="password" id="password_nuevo" name="password_nuevo" required minlength="6">
              </div>
              <div class="form-group">
                  <label for="password_confirmar">Confirmar Nueva Contraseña</label>
                  <input type="password" id="password_confirmar" name="password_confirmar" required minlength="6">
              </div>
              <div class="form-actions">
                  <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                  <button type="submit" class="btn-primary">Cambiar Contraseña</button>
              </div>
          </form>
      </div>
  </div>

  <script src="./Js/perfil_a.js"></script>
</body>
</html>

