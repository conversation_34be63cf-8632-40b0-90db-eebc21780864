/* Estilos específicos para la sección de control de pagos */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Resumen de pagos */
  .payment-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .payment-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .payment-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .payment-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }
  
  .payment-icon .material-icons {
    font-size: 2rem;
    color: var(--primary-color);
  }
  
  .payment-info {
    flex: 1;
  }
  
  .payment-info h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-light);
  }
  
  .payment-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
  }
  
  /* Acciones de pago */
  .payment-actions {
    display: flex;
    gap: 10px;
  }
  
  .payment-action-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .payment-action-btn:hover {
    background-color: var(--primary-color);
    color: white;
  }
  
  /* Tabla de cronograma de pagos */
  .payment-schedule-container {
    overflow-x: auto;
    margin-bottom: 20px;
  }
  
  .payment-schedule {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  .payment-schedule thead {
    background-color: var(--primary-light);
  }
  
  .payment-schedule th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
  }
  
  .payment-schedule td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
  }
  
  .payment-schedule tbody tr:last-child td {
    border-bottom: none;
  }
  
  .payment-schedule tbody tr:hover {
    background-color: var(--secondary-color);
  }
  
  .payment-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
  }
  
  .payment-status.paid {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
  
  .payment-status.pending {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  .payment-status.late {
    background-color: #ffebee;
    color: #c62828;
  }
  
  .table-action-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .table-action-btn:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .table-action-btn.pay-btn {
    color: var(--primary-color);
  }
  
  .table-action-btn.pay-btn:hover {
    background-color: var(--primary-light);
  }
  
  /* Métodos de pago */
  .payment-methods {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .payment-method-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .payment-method-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .payment-method-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .payment-method-icon .material-icons {
    font-size: 2rem;
    color: white;
  }
  
  .payment-method-icon.bank {
    background-color: #2196f3;
  }
  
  .payment-method-icon.online {
    background-color: #4caf50;
  }
  
  .payment-method-icon.cash {
    background-color: #ff9800;
  }
  
  .payment-method-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
  }
  
  .payment-method-info p {
    font-size: 0.95rem;
    color: var(--text-light);
    margin-bottom: 15px;
  }
  
  .account-info,
  .office-info div {
    display: flex;
    margin-bottom: 5px;
  }
  
  .account-label,
  .office-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    width: 120px;
  }
  
  .account-value,
  .office-value {
    font-size: 0.9rem;
    color: var(--text-light);
    flex: 1;
  }
  
  .online-payment-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 15px;
  }
  
  .online-payment-btn:hover {
    background-color: #1e40af;
  }
  
  /* Modal de pago */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
  }
  
  .modal-overlay.active {
    opacity: 1;
    visibility: visible;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    transform: translateY(20px);
    transition: transform 0.3s;
  }
  
  .modal-overlay.active .modal-content {
    transform: translateY(0);
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f9f9f9;
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 20px;
  }
  
  /* Detalles de pago en modal */
  .payment-details {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .payment-detail-item {
    display: flex;
    margin-bottom: 10px;
  }
  
  .payment-detail-item:last-child {
    margin-bottom: 0;
  }
  
  .detail-label {
    font-weight: 500;
    color: var(--text-color);
    width: 120px;
  }
  
  .detail-value {
    color: var(--text-color);
    flex: 1;
  }
  
  /* Opciones de pago en modal */
  .payment-options {
    margin-bottom: 20px;
  }
  
  .payment-options h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
  }
  
  .payment-option-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .payment-option {
    display: flex;
    align-items: center;
  }
  
  .payment-option input[type="radio"] {
    margin-right: 10px;
  }
  
  .payment-option label {
    display: flex;
    align-items: center;
    gap  {
    margin-right: 10px;
  }
  
  .payment-option label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
  }
  
  .payment-option label .material-icons {
    color: var(--primary-color);
  }
  
  /* Formulario de tarjeta de crédito */
  .credit-card-form {
    margin-bottom: 20px;
  }
  
  .form-group {
    margin-bottom: 15px;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  
  .form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .form-group input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-color);
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .payment-summary {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .payment-methods {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 768px) {
    .payment-summary {
      grid-template-columns: 1fr;
    }
    
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
}