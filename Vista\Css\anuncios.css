/* Estilos para la página de anuncios */

/* Hero Section */
.anuncios-hero {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .anuncios-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .anuncios-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
  }
  
  /* Filtros */
  .anuncios-filtros {
    background-color: var(--white);
    padding: 2rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }
  
  .filtros-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .filtro-busqueda {
    flex: 1;
    min-width: 250px;
  }
  
  .input-group {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    border: 1px solid #e0e0e0;
  }
  
  .input-group .material-icons {
    color: #666;
    margin-right: 0.5rem;
  }
  
  .input-group input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 0.5rem 0;
    font-family: "Montserrat", sans-serif;
    font-size: 1rem;
    outline: none;
  }
  
  .filtro-año {
    min-width: 150px;
  }
  
  .filtro-año select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f5f5f5;
    font-family: "Montserrat", sans-serif;
    font-size: 1rem;
    outline: none;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
  }
  
  /* Lista de Anuncios */
  .anuncios-lista {
    padding: 4rem 0;
    background-color: #f9f9f9;
    min-height: 500px;
  }
  
  .anuncios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
  }
  
  .anuncio-card {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  
  .anuncio-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
  
  .anuncio-imagen {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }
  
  .anuncio-imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
  }
  
  .anuncio-card:hover .anuncio-imagen img {
    transform: scale(1.05);
  }
  
  .anuncio-contenido {
    padding: 1.5rem;
  }
  
  .anuncio-fecha {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    margin-bottom: 1rem;
  }
  
  .anuncio-titulo {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
    line-height: 1.4;
  }
  
  .anuncio-extracto {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  
  .anuncio-link {
    display: inline-block;
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s;
    cursor: pointer;
  }
  
  .anuncio-link:hover {
    color: #e63e00;
  }
  
  .anuncio-link .material-icons {
    font-size: 1rem;
    vertical-align: middle;
    margin-left: 0.3rem;
  }
  
  /* Mensaje de no resultados */
  .no-resultados {
    text-align: center;
    padding: 3rem 0;
  }
  
  .no-resultados .material-icons {
    font-size: 4rem;
    color: #ccc;
    margin-bottom: 1rem;
  }
  
  .no-resultados p {
    color: #666;
    font-size: 1.2rem;
  }
  
  /* Estilos para el logo con enlace */
  .logo a {
    display: block;
  }
  
  /* Estilos para el modal */
  .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-contenido {
    background-color: white;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: flex-end;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .cerrar-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }
  
  .cerrar-modal:hover {
    background-color: #f0f0f0;
    color: #333;
  }
  
  .modal-body {
    padding: 0 30px 30px;
  }
  
  .modal-info-autor {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .autor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }
  
  .autor-avatar .material-icons {
    font-size: 30px;
    color: #666;
  }
  
  .autor-info {
    flex: 1;
  }
  
  .autor-nombre {
    font-size: 1.1rem;
    margin: 0;
    color: #333;
  }
  
  .autor-cargo {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
  }
  
  .modal-fecha {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 0.9rem;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    margin-bottom: 15px;
  }
  
  .modal-titulo {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--secondary-color);
    line-height: 1.3;
  }
  
  .modal-imagen-container {
    width: 100%;
    margin-bottom: 25px;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .modal-imagen-container img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .modal-contenido-texto {
    color: #333;
    line-height: 1.7;
    font-size: 1.05rem;
    margin-bottom: 30px;
  }
  
  .modal-contenido-texto p {
    margin-bottom: 15px;
  }
  
  .modal-footer {
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;
  }
  
  .modal-acciones {
    display: flex;
    gap: 15px;
  }
  
  .compartir-btn,
  .descargar-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.9rem;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .compartir-btn:hover,
  .descargar-btn:hover {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .compartir-btn .material-icons,
  .descargar-btn .material-icons {
    font-size: 18px;
  }
  
  /* Estilos responsivos */
  @media (max-width: 768px) {
    .anuncios-hero {
      padding: 100px 0 40px;
    }
  
    .anuncios-hero h1 {
      font-size: 2rem;
    }
  
    .filtros-container {
      flex-direction: column;
      align-items: stretch;
    }
  
    .filtro-busqueda,
    .filtro-año {
      width: 100%;
    }
  
    .anuncios-grid {
      grid-template-columns: 1fr;
    }
  
    .modal-contenido {
      width: 95%;
      max-height: 85vh;
    }
  
    .modal-body {
      padding: 0 20px 20px;
    }
  
    .modal-titulo {
      font-size: 1.5rem;
    }
  
    .modal-acciones {
      flex-direction: column;
    }
  
    /* Ajuste para la sección "Síguenos" en móvil */
    .footer-section:last-child {
      margin-top: 30px;
      align-items: center;
    }
  }
  
  /* Estilos para el enlace activo en el menú */
  .nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
  }
  
  /* Prevenir scroll cuando el modal está abierto */
  body.modal-open {
    overflow: hidden;
  }
  
  /* Estilos mejorados para los iconos de redes sociales */
  .social-links {
    display: flex;
    gap: 1rem;
  }
  
  .social-links a {
    color: var(--white);
    font-size: 1.5rem;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
  
  /* Ajustes para el icono SVG de WhatsApp */
  .whatsapp-icon svg {
    width: 20px;
    height: 20px;
  }
  
  /* Color específico para WhatsApp */
  .social-links a[aria-label="WhatsApp"]:hover {
    background-color: #25d366;
    color: white;
  }
  
  /* Color específico para Facebook */
  .social-links a[aria-label="Facebook"]:hover {
    background-color: #1877f2;
    color: white;
  }
  
  /* Ajuste para la sección "Síguenos" */
  .footer-section:last-child {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }
  
  /* Ajuste para la distribución del footer en pantallas grandes */
  @media (min-width: 768px) {
    .footer-content {
      grid-template-columns: 1fr 1fr 0.8fr;
    }
  
    .footer-section:last-child {
      justify-self: end;
      margin-left: 30px;
    }
  }
  
  