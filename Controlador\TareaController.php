<?php
require_once 'Conexion.php';

class TareaController {
    private $conexion;
    
    public function __construct() {
        $this->conexion = new Conexion();
    }
    
    // Obtener todas las tareas de un curso
    public function obtenerTareasPorCurso($cursoId) {
        try {
            $sql = "SELECT c.*, s.titulo as sesion_titulo, sa.titulo as semana_titulo, sa.numero_semana
                    FROM contenido c
                    LEFT JOIN sesiones s ON c.sesion_id = s.id
                    LEFT JOIN semanas_academicas sa ON s.semana_id = sa.id
                    WHERE c.tipo IN ('tarea', 'examen') 
                    AND s.curso_id = ?
                    AND c.activo = 1
                    ORDER BY sa.numero_semana ASC, c.created_at DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $cursoId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            $tareas = [];
            while ($row = $resultado->fetch_assoc()) {
                $tareas[] = $row;
            }
            
            return $tareas;
        } catch (Exception $e) {
            error_log("Error en obtenerTareasPorCurso: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener tareas agrupadas por semana
    public function obtenerTareasAgrupadasPorSemana($cursoId) {
        try {
            $sql = "SELECT 
                        sa.id as semana_id,
                        sa.numero_semana,
                        sa.titulo as semana_titulo,
                        sa.fecha_inicio,
                        sa.fecha_fin,
                        c.id as contenido_id,
                        c.tipo,
                        c.titulo,
                        c.descripcion,
                        c.fecha_limite,
                        c.hora_limite,
                        c.puntos,
                        c.created_at
                    FROM semanas_academicas sa
                    LEFT JOIN sesiones s ON sa.id = s.semana_id
                    LEFT JOIN contenido c ON s.id = c.sesion_id AND c.tipo IN ('tarea', 'examen') AND c.activo = 1
                    WHERE sa.curso_id = ? AND sa.activo = 1
                    ORDER BY sa.numero_semana ASC, c.created_at DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $cursoId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            $semanas = [];
            while ($row = $resultado->fetch_assoc()) {
                $semanaId = $row['semana_id'];
                if (!isset($semanas[$semanaId])) {
                    $semanas[$semanaId] = [
                        'id' => $row['semana_id'],
                        'numero_semana' => $row['numero_semana'],
                        'titulo' => $row['semana_titulo'],
                        'fecha_inicio' => $row['fecha_inicio'],
                        'fecha_fin' => $row['fecha_fin'],
                        'tareas' => []
                    ];
                }
                
                if ($row['contenido_id']) {
                    $semanas[$semanaId]['tareas'][] = [
                        'id' => $row['contenido_id'],
                        'tipo' => $row['tipo'],
                        'titulo' => $row['titulo'],
                        'descripcion' => $row['descripcion'],
                        'fecha_limite' => $row['fecha_limite'],
                        'hora_limite' => $row['hora_limite'],
                        'puntos' => $row['puntos'],
                        'created_at' => $row['created_at']
                    ];
                }
            }
            
            return array_values($semanas);
        } catch (Exception $e) {
            error_log("Error en obtenerTareasAgrupadasPorSemana: " . $e->getMessage());
            return false;
        }
    }
    
    // Crear nueva tarea
    public function crearTarea($datos) {
        try {
            // Primero crear o obtener la sesión
            $sesionId = $this->obtenerOCrearSesion($datos['curso_id'], $datos['semana_id']);
            
            $sql = "INSERT INTO contenido (sesion_id, tipo, titulo, descripcion, fecha_limite, hora_limite, puntos, activo) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("isssssi", 
                $sesionId,
                $datos['tipo'],
                $datos['titulo'],
                $datos['descripcion'],
                $datos['fecha_limite'],
                $datos['hora_limite'],
                $datos['puntos']
            );
            
            if ($stmt->execute()) {
                return $this->conexion->insert_id;
            }
            return false;
        } catch (Exception $e) {
            error_log("Error en crearTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Actualizar tarea
    public function actualizarTarea($id, $datos) {
        try {
            $sql = "UPDATE contenido 
                    SET tipo = ?, titulo = ?, descripcion = ?, fecha_limite = ?, hora_limite = ?, puntos = ?
                    WHERE id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("sssssii", 
                $datos['tipo'],
                $datos['titulo'],
                $datos['descripcion'],
                $datos['fecha_limite'],
                $datos['hora_limite'],
                $datos['puntos'],
                $id
            );
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error en actualizarTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Eliminar tarea (marcar como inactiva)
    public function eliminarTarea($id) {
        try {
            $sql = "UPDATE contenido SET activo = 0 WHERE id = ?";
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $id);
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error en eliminarTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener tarea por ID
    public function obtenerTareaPorId($id) {
        try {
            $sql = "SELECT c.*, s.curso_id, sa.numero_semana, sa.titulo as semana_titulo
                    FROM contenido c
                    LEFT JOIN sesiones s ON c.sesion_id = s.id
                    LEFT JOIN semanas_academicas sa ON s.semana_id = sa.id
                    WHERE c.id = ? AND c.activo = 1";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            return $resultado->fetch_assoc();
        } catch (Exception $e) {
            error_log("Error en obtenerTareaPorId: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener entregas de una tarea
    public function obtenerEntregasPorTarea($contenidoId) {
        try {
            $sql = "SELECT e.*, 
                           CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as nombre_estudiante,
                           est.grado_actual
                    FROM entregas e
                    JOIN estudiantes est ON e.estudiante_id = est.id
                    JOIN personas p ON est.persona_id = p.id
                    WHERE e.contenido_id = ?
                    ORDER BY e.fecha_entrega DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $contenidoId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            $entregas = [];
            while ($row = $resultado->fetch_assoc()) {
                $entregas[] = $row;
            }
            
            return $entregas;
        } catch (Exception $e) {
            error_log("Error en obtenerEntregasPorTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Calificar entrega
    public function calificarEntrega($entregaId, $calificacion, $comentario) {
        try {
            $sql = "UPDATE entregas 
                    SET calificacion = ?, comentario_maestro = ?, estado = 'calificado', fecha_calificacion = NOW()
                    WHERE id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("dsi", $calificacion, $comentario, $entregaId);
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error en calificarEntrega: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener estadísticas de entregas
    public function obtenerEstadisticasEntregas($contenidoId) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_entregas,
                        SUM(CASE WHEN estado = 'entregado' THEN 1 ELSE 0 END) as entregadas,
                        SUM(CASE WHEN estado = 'tardio' THEN 1 ELSE 0 END) as tardias,
                        SUM(CASE WHEN estado = 'pendiente' THEN 1 ELSE 0 END) as pendientes,
                        SUM(CASE WHEN estado = 'calificado' THEN 1 ELSE 0 END) as calificadas
                    FROM entregas 
                    WHERE contenido_id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $contenidoId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            return $resultado->fetch_assoc();
        } catch (Exception $e) {
            error_log("Error en obtenerEstadisticasEntregas: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener o crear sesión para una semana
    private function obtenerOCrearSesion($cursoId, $semanaId) {
        try {
            // Buscar si ya existe una sesión para esta semana
            $sql = "SELECT id FROM sesiones WHERE curso_id = ? AND semana_id = ?";
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("ii", $cursoId, $semanaId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            if ($resultado->num_rows > 0) {
                $row = $resultado->fetch_assoc();
                return $row['id'];
            } else {
                // Crear nueva sesión
                $sql = "INSERT INTO sesiones (curso_id, semana_id, titulo, fecha_inicio, fecha_fin) 
                        VALUES (?, ?, 'Sesión principal', '0000-00-00', '0000-00-00')";
                $stmt = $this->conexion->prepare($sql);
                $stmt->bind_param("ii", $cursoId, $semanaId);
                $stmt->execute();
                return $this->conexion->insert_id;
            }
        } catch (Exception $e) {
            error_log("Error en obtenerOCrearSesion: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener semanas académicas de un curso
    public function obtenerSemanasAcademicas($cursoId) {
        try {
            $sql = "SELECT * FROM semanas_academicas WHERE curso_id = ? AND activo = 1 ORDER BY numero_semana ASC";
            $stmt = $this->conexion->prepare($sql);
            $stmt->bind_param("i", $cursoId);
            $stmt->execute();
            $resultado = $stmt->get_result();
            
            $semanas = [];
            while ($row = $resultado->fetch_assoc()) {
                $semanas[] = $row;
            }
            
            return $semanas;
        } catch (Exception $e) {
            error_log("Error en obtenerSemanasAcademicas: " . $e->getMessage());
            return false;
        }
    }
}
?> 