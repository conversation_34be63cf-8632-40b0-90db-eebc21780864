<?php
require_once __DIR__ . '/../Modelo/Conexion.php';

class TareaController {
    private $conexion;
    
    public function __construct() {
        $this->conexion = Conexion::getConexion();
    }
    
    // Obtener todas las tareas de un curso
    public function obtenerTareasPorCurso($cursoId) {
        try {
            $sql = "SELECT c.*, s.titulo as sesion_titulo, sa.titulo as semana_titulo, sa.numero_semana
                    FROM contenido c
                    LEFT JOIN sesiones s ON c.sesion_id = s.id
                    LEFT JOIN semanas_academicas sa ON s.semana_id = sa.id
                    WHERE c.tipo IN ('tarea', 'examen') 
                    AND s.curso_id = ?
                    AND c.activo = 1
                    ORDER BY sa.numero_semana ASC, c.created_at DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$cursoId]);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error en obtenerTareasPorCurso: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener tareas agrupadas por semana
    public function obtenerTareasAgrupadasPorSemana($cursoId) {
        try {
            $sql = "SELECT 
                        sa.id as semana_id,
                        sa.numero_semana,
                        sa.titulo as semana_titulo,
                        sa.fecha_inicio,
                        sa.fecha_fin,
                        c.id as contenido_id,
                        c.tipo,
                        c.titulo,
                        c.descripcion,
                        c.fecha_limite,
                        c.hora_limite,
                        c.puntos,
                        c.created_at
                    FROM semanas_academicas sa
                    LEFT JOIN sesiones s ON sa.id = s.semana_id
                    LEFT JOIN contenido c ON s.id = c.sesion_id AND c.tipo IN ('tarea', 'examen') AND c.activo = 1
                    WHERE sa.curso_id = ? AND sa.activo = 1
                    ORDER BY sa.numero_semana ASC, c.created_at DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$cursoId]);
            $resultado = $stmt->fetchAll();
            
            $semanas = [];
            foreach ($resultado as $row) {
                $semanaId = $row['semana_id'];
                if (!isset($semanas[$semanaId])) {
                    $semanas[$semanaId] = [
                        'id' => $row['semana_id'],
                        'numero_semana' => $row['numero_semana'],
                        'titulo' => $row['semana_titulo'],
                        'fecha_inicio' => $row['fecha_inicio'],
                        'fecha_fin' => $row['fecha_fin'],
                        'tareas' => []
                    ];
                }
                
                if ($row['contenido_id']) {
                    $semanas[$semanaId]['tareas'][] = [
                        'id' => $row['contenido_id'],
                        'tipo' => $row['tipo'],
                        'titulo' => $row['titulo'],
                        'descripcion' => $row['descripcion'],
                        'fecha_limite' => $row['fecha_limite'],
                        'hora_limite' => $row['hora_limite'],
                        'puntos' => $row['puntos'],
                        'created_at' => $row['created_at']
                    ];
                }
            }
            
            return array_values($semanas);
        } catch (Exception $e) {
            error_log("Error en obtenerTareasAgrupadasPorSemana: " . $e->getMessage());
            return false;
        }
    }
    
    // Crear nueva tarea
    public function crearTarea($datos) {
        try {
            // Primero crear o obtener la sesión
            $sesionId = $this->obtenerOCrearSesion($datos['curso_id'], $datos['semana_id']);
            
            $sql = "INSERT INTO contenido (sesion_id, tipo, titulo, descripcion, fecha_limite, hora_limite, puntos, activo) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([
                $sesionId,
                $datos['tipo'],
                $datos['titulo'],
                $datos['descripcion'],
                $datos['fecha_limite'],
                $datos['hora_limite'],
                $datos['puntos']
            ]);
            
            return $this->conexion->lastInsertId();
        } catch (Exception $e) {
            error_log("Error en crearTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Actualizar tarea
    public function actualizarTarea($id, $datos) {
        try {
            $sql = "UPDATE contenido 
                    SET tipo = ?, titulo = ?, descripcion = ?, fecha_limite = ?, hora_limite = ?, puntos = ?
                    WHERE id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            return $stmt->execute([
                $datos['tipo'],
                $datos['titulo'],
                $datos['descripcion'],
                $datos['fecha_limite'],
                $datos['hora_limite'],
                $datos['puntos'],
                $id
            ]);
        } catch (Exception $e) {
            error_log("Error en actualizarTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Eliminar tarea (borrado físico)
    public function eliminarTarea($id) {
        try {
            $sql = "DELETE FROM contenido WHERE id = ?";
            $stmt = $this->conexion->prepare($sql);
            return $stmt->execute([$id]);
        } catch (Exception $e) {
            error_log("Error en eliminarTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener tarea por ID
    public function obtenerTareaPorId($id) {
        try {
            $sql = "SELECT c.*, s.curso_id, sa.numero_semana, sa.titulo as semana_titulo
                    FROM contenido c
                    LEFT JOIN sesiones s ON c.sesion_id = s.id
                    LEFT JOIN semanas_academicas sa ON s.semana_id = sa.id
                    WHERE c.id = ? AND c.activo = 1";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error en obtenerTareaPorId: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener entregas de una tarea
    public function obtenerEntregasPorTarea($contenidoId) {
        try {
            $sql = "SELECT e.*, 
                           CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as nombre_estudiante,
                           est.grado_actual
                    FROM entregas e
                    JOIN estudiantes est ON e.estudiante_id = est.id
                    JOIN personas p ON est.persona_id = p.id
                    WHERE e.contenido_id = ?
                    ORDER BY e.fecha_entrega DESC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$contenidoId]);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error en obtenerEntregasPorTarea: " . $e->getMessage());
            return false;
        }
    }
    
    // Calificar entrega
    public function calificarEntrega($entregaId, $calificacion, $comentario) {
        try {
            $sql = "UPDATE entregas 
                    SET calificacion = ?, comentario_maestro = ?, fecha_calificacion = NOW()
                    WHERE id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            return $stmt->execute([$calificacion, $comentario, $entregaId]);
        } catch (Exception $e) {
            error_log("Error en calificarEntrega: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener estadísticas de entregas
    public function obtenerEstadisticasEntregas($contenidoId) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_entregas,
                        COUNT(CASE WHEN calificacion IS NOT NULL THEN 1 END) as entregas_calificadas,
                        AVG(calificacion) as promedio_calificacion,
                        MIN(calificacion) as calificacion_minima,
                        MAX(calificacion) as calificacion_maxima
                    FROM entregas 
                    WHERE contenido_id = ?";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$contenidoId]);
            
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error en obtenerEstadisticasEntregas: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener o crear sesión para una semana
    private function obtenerOCrearSesion($cursoId, $semanaId) {
        try {
            // Primero intentar obtener la sesión existente
            $sql = "SELECT id FROM sesiones WHERE curso_id = ? AND semana_id = ? LIMIT 1";
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$cursoId, $semanaId]);
            $sesion = $stmt->fetch();
            
            if ($sesion) {
                return $sesion['id'];
            }
            
            // Si no existe, crear una nueva sesión
            $sql = "INSERT INTO sesiones (curso_id, semana_id, titulo, descripcion, activo) 
                    VALUES (?, ?, 'Sesión automática', 'Sesión creada automáticamente para tareas', 1)";
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$cursoId, $semanaId]);
            
            return $this->conexion->lastInsertId();
        } catch (Exception $e) {
            error_log("Error en obtenerOCrearSesion: " . $e->getMessage());
            return false;
        }
    }
    
    // Obtener semanas académicas de un curso
    public function obtenerSemanasAcademicas($cursoId) {
        try {
            $sql = "SELECT id, numero_semana, titulo, fecha_inicio, fecha_fin 
                    FROM semanas_academicas 
                    WHERE curso_id = ? AND activo = 1 
                    ORDER BY numero_semana ASC";
            
            $stmt = $this->conexion->prepare($sql);
            $stmt->execute([$cursoId]);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error en obtenerSemanasAcademicas: " . $e->getMessage());
            return false;
        }
    }
}
?> 