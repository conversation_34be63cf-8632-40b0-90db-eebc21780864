/* Estilos específicos para la página de mensajes (vista de maestros) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Contenedor principal de email */
  .email-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    min-height: 500px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  /* Barra de herramientas principal */
  .email-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--secondary-color);
  }
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    width: 300px;
    padding: 10px 15px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
  }
  
  .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
  }
  
  .compose-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .compose-btn:hover {
    background-color: #1e40af;
  }
  
  /* Contenido principal de email */
  .email-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  
  /* Lista de mensajes */
  .email-list {
    width: 35%;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
  }
  
  .email-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .email-list-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
  }
  
  .email-list-actions {
    display: flex;
    gap: 10px;
  }
  
  .refresh-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .refresh-btn:hover {
    color: var(--primary-color);
  }
  
  .email-items {
    flex: 1;
    overflow-y: auto;
  }
  
  .email-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .email-item:hover {
    background-color: var(--secondary-color);
  }
  
  .email-item.selected {
    background-color: var(--primary-light);
  }
  
  .email-item.unread {
    font-weight: 600;
  }
  
  .email-item-checkbox {
    margin-right: 10px;
  }
  
  .email-item-checkbox input[type="checkbox"] {
    display: none;
  }
  
  .email-item-checkbox label {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .email-item-checkbox input[type="checkbox"]:checked + label::after {
    content: "";
    position: absolute;
    top: 2px;
    left: 6px;
    width: 5px;
    height: 10px;
    border: solid var(--primary-color);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  
  .email-item-star {
    margin-right: 15px;
    color: var(--text-light);
  }
  
  .email-item-star.starred {
    color: var(--warning-color);
  }
  
  .email-item-sender {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 15px;
  }
  
  .email-item-content {
    flex: 1;
    min-width: 0;
    margin-right: 15px;
  }
  
  .email-item-subject {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 3px;
  }
  
  .email-item-snippet {
    font-size: 0.9rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: normal;
  }
  
  .email-item-time {
    font-size: 0.85rem;
    color: var(--text-light);
    white-space: nowrap;
  }
  
  /* Vista de mensaje */
  .email-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .email-view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .email-view-subject {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
  }
  
  .email-view-actions {
    display: flex;
    gap: 10px;
  }
  
  .email-action-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .email-action-btn:hover {
    color: var(--primary-color);
  }
  
  .email-view-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }
  
  .email-message {
    margin-bottom: 30px;
  }
  
  .email-message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .sender-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .sender-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
  }
  
  .sender-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .sender-details {
    display: flex;
    flex-direction: column;
  }
  
  .sender-name {
    font-weight: 600;
    color: var(--text-color);
  }
  
  .sender-email {
    font-weight: normal;
    color: var(--text-light);
  }
  
  .message-to {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .message-date {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .date-time {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .message-options-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .message-options-btn:hover {
    color: var(--primary-color);
  }
  
  .email-message-body {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
  }
  
  .email-message-body p {
    margin-bottom: 15px;
  }
  
  /* Área de respuesta */
  .reply-area {
    margin-top: 30px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .reply-header {
    padding: 10px 15px;
    background-color: var(--secondary-color);
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .reply-editor {
    display: flex;
    flex-direction: column;
  }
  
  .reply-editor textarea {
    width: 100%;
    min-height: 120px;
    padding: 15px;
    border: none;
    font-size: 1rem;
    color: var(--text-color);
    font-family: inherit;
    resize: none;
    outline: none;
  }
  
  .reply-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-top: 1px solid var(--border-color);
    background-color: var(--secondary-color);
  }
  
  .toolbar-formatting {
    display: flex;
    gap: 10px;
  }
  
  .format-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .format-btn:hover {
    color: var(--primary-color);
  }
  
  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .attachment-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .attachment-btn:hover {
    color: var(--primary-color);
  }
  
  .send-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .send-btn:hover {
    background-color: #1e40af;
  }
  
  /* Modal para redactar */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  .compose-modal {
    max-width: 700px;
    height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .recipients-modal {
    max-width: 500px;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  /* Formulario de redacción */
  #compose-form {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .compose-field {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
  }
  
  .compose-field label {
    width: 60px;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .compose-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
  }
  
  .compose-input {
    flex: 1;
    border: none;
    font-size: 1rem;
    color: var(--text-color);
    outline: none;
  }
  
  .recipients-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
  }
  
  .compose-body {
    flex: 1;
    padding: 15px 0;
  }
  
  #compose-message {
    width: 100%;
    height: 100%;
    border: none;
    font-size: 1rem;
    color: var(--text-color);
    font-family: inherit;
    resize: none;
    outline: none;
  }
  
  .compose-attachments {
    padding: 10px 0;
  }
  
  .compose-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
  }
  
  .toolbar-left {
    display: flex;
    gap: 15px;
  }
  
  .toolbar-right {
    display: flex;
    gap: 10px;
  }
  
  .btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary:hover {
    background-color: #e0e0e0;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  /* Lista de destinatarios */
  .recipients-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
  }
  
  .recipients-group {
    margin-bottom: 20px;
  }
  
  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .group-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .group-action {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .recipients-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .recipient-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .recipient-item input[type="checkbox"] {
    margin: 0;
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  /* Estilos adicionales para notificaciones */
  .notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow-md);
    z-index: 2000;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
  }
  
  .notification.show {
    transform: translateY(0);
    opacity: 1;
  }
  
  .notification.success {
    background-color: var(--success-color);
  }
  
  .notification.error {
    background-color: var(--danger-color);
  }
  
  .notification .material-icons {
    font-size: 1.2rem;
  }
  
  /* Animación de rotación para el icono de refrescar */
  .rotating {
    animation: rotate 1s linear infinite;
  }
  
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  /* Estilos para los adjuntos */
  .attachment-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background-color: var(--secondary-color);
    border-radius: 5px;
    margin-bottom: 8px;
  }
  
  .attachment-name {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-color);
  }
  
  .attachment-size {
    font-size: 0.8rem;
    color: var(--text-light);
  }
  
  .remove-attachment-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .remove-attachment-btn:hover {
    color: var(--danger-color);
  }
  
  /* Scroll para mensajes anteriores */
  .email-view-content {
    position: relative;
  }
  
  .scroll-indicator {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    opacity: 0.8;
    transition: var(--transition);
    z-index: 10;
  }
  
  .scroll-indicator:hover {
    opacity: 1;
    transform: scale(1.1);
  }
  
  .scroll-indicator .material-icons {
    font-size: 1.5rem;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .email-content {
      flex-direction: column;
    }
  
    .email-list {
      width: 100%;
      height: 300px;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }
  
    .email-view {
      height: calc(100% - 300px);
    }
  }
  
  @media (max-width: 768px) {
    .email-item-sender {
      width: 80px;
    }
  
    .email-message-header {
      flex-direction: column;
      gap: 15px;
    }
  
    .message-date {
      align-self: flex-end;
    }
  
    .reply-toolbar {
      flex-direction: column;
      gap: 10px;
    }
  
    .toolbar-formatting,
    .toolbar-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
  