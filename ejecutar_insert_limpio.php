<?php
require_once 'Modelo/conexion.php';

try {
    $pdo = Conexion::getConexion();
    
    // Leer el archivo SQL limpio
    $sql = file_get_contents('insert_tarea_limpio.sql');
    
    // Dividir en statements individuales
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "✅ Datos de ejemplo insertados correctamente.\n";
    echo "📝 Tarea: 'Ejercicios de Matemáticas - Fracciones'\n";
    echo "👨‍🎓 Diego Martínez: ENTREGADO (18.5/20)\n";
    echo "👩‍🎓 So<PERSON><PERSON> Sánchez: PENDIENTE\n";
    echo "🎯 Curso ID: 6, Contenido ID: 12\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
