<?php
require_once '../Controlador/AuthController.php';

// Proteger la página - solo estudiantes
AuthController::proteger<PERSON><PERSON>na(['estudiante']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información específica del estudiante
$infoEstudiante = $usuarioActual['informacion_rol'];
$gradoActual = $infoEstudiante['grado_actual'] ?? 'No asignado';
$promedioGeneral = $infoEstudiante['promedio_general'] ?? 0;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Inicio</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar estudiante'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                        <?php if ($fotoPerfilUrl): ?>
                            <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                        <p><?php echo htmlspecialchars(ucfirst(str_replace('-', ' ', $gradoActual))); ?></p>
                        <?php if ($promedioGeneral > 0): ?>
                            <small>Promedio: <?php echo number_format($promedioGeneral, 1); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li class="active">
                            <a href="inicio_e.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_e.html">
                                <span class="material-icons">book</span>
                                <span>Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="notas_e.html">
                                <span class="material-icons">grade</span>
                                <span>Notas</span>
                            </a>
                        </li>
                        <li>
                            <a href="tareas_e.html">
                                <span class="material-icons">assignment</span>
                                <span>Tareas</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../Controlador/AuthController.php?action=logout">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Bienvenido, Juan</h1>
                    <p class="current-date">Sábado, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Resumen de actividad -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Resumen de actividad</h2>
                    </div>
                    
                    <div class="activity-cards">
                        <div class="activity-card">
                            <div class="activity-icon">
                                <span class="material-icons">book</span>
                            </div>
                            <div class="activity-info">
                                <h3>Cursos activos</h3>
                                <div class="activity-count">6</div>
                            </div>
                        </div>
                        
                        <div class="activity-card">
                            <div class="activity-icon">
                                <span class="material-icons">assignment</span>
                            </div>
                            <div class="activity-info">
                                <h3>Tareas pendientes</h3>
                                <div class="activity-count">3</div>
                            </div>
                        </div>
                        
                        <div class="activity-card">
                            <div class="activity-icon">
                                <span class="material-icons">event_busy</span>
                            </div>
                            <div class="activity-info">
                                <h3>N° de faltas</h3>
                                <div class="activity-count">2</div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Horario de hoy -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Horario de hoy</h2>
                    </div>
                    
                    <div class="schedule">
                        <div class="schedule-item">
                            <div class="schedule-time">7:30 - 8:15</div>
                            <div class="schedule-content">
                                <h3>Matemáticas</h3>
                                <p>Prof. Carlos García</p>
                            </div>
                        </div>

                        <div class="schedule-item current">
                            <div class="schedule-time">8:15 - 9:00</div>
                            <div class="schedule-content">
                                <h3>Comunicación</h3>
                                <p>Prof. Ana Rodríguez</p>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">9:00 - 9:45</div>
                            <div class="schedule-content">
                                <h3>Ciencias Naturales</h3>
                                <p>Prof. Laura Martínez</p>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">9:45 - 10:15</div>
                            <div class="schedule-content">
                                <h3>Recreo</h3>
                                <p>Patio principal</p>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">10:15 - 11:00</div>
                            <div class="schedule-content">
                                <h3>Inglés</h3>
                                <p>Prof. David Smith</p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="./Js/plataforma.js"></script>
</body>
</html>