<?php
class Curso {
    private $conexion;

    public function __construct($conexion) {
        $this->conexion = $conexion;
    }

    // Obtener todos los cursos
    public function obtenerTodos() {
        $query = "SELECT * FROM cursos WHERE activo = 1 ORDER BY nombre";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    // Obtener curso por ID
    public function obtenerPorId($id) {
        $query = "SELECT * FROM cursos WHERE id = ? AND activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    // Crear nuevo curso
    public function crear($datos) {
        $query = "INSERT INTO cursos (nombre, especialidad, maestro_id, grado, anio_escolar, descripcion, icono, imagen) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([
            $datos['nombre'],
            $datos['especialidad'],
            $datos['maestro_id'],
            $datos['grado'],
            $datos['anio_escolar'],
            $datos['descripcion'],
            $datos['icono'],
            $datos['imagen']
        ]);
        return $this->conexion->lastInsertId();
    }

    // Actualizar curso
    public function actualizar($id, $datos) {
        $query = "UPDATE cursos SET 
                  nombre = ?, 
                  especialidad = ?, 
                  grado = ?, 
                  descripcion = ?, 
                  icono = ?, 
                  imagen = ?
                  WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([
            $datos['nombre'],
            $datos['especialidad'],
            $datos['grado'],
            $datos['descripcion'],
            $datos['icono'],
            $datos['imagen'],
            $id
        ]);
        return $stmt->rowCount() > 0;
    }

    // Eliminar curso (marcar como inactivo)
    public function eliminar($id) {
        $query = "UPDATE cursos SET activo = 0 WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$id]);
        return $stmt->rowCount() > 0;
    }

    // Obtener horarios de un curso
    public function obtenerHorarios($cursoId) {
        $query = "SELECT * FROM horarios WHERE curso_id = ? ORDER BY dia_semana";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        return $stmt->fetchAll();
    }

    // Guardar horarios de un curso
    public function guardarHorarios($cursoId, $horarios) {
        // Primero eliminar horarios existentes
        $queryDelete = "DELETE FROM horarios WHERE curso_id = ?";
        $stmtDelete = $this->conexion->prepare($queryDelete);
        $stmtDelete->execute([$cursoId]);

        // Insertar nuevos horarios
        $queryInsert = "INSERT INTO horarios (curso_id, dia_semana, hora_inicio, hora_fin) VALUES (?, ?, ?, ?)";
        $stmtInsert = $this->conexion->prepare($queryInsert);
        
        foreach ($horarios as $horario) {
            $stmtInsert->execute([
                $cursoId,
                $horario['dia_semana'],
                $horario['hora_inicio'],
                $horario['hora_fin']
            ]);
        }
        
        return true;
    }

    // Obtener número de estudiantes inscritos en un curso
    public function obtenerNumeroEstudiantes($cursoId) {
        $query = "SELECT COUNT(*) as total FROM inscripciones WHERE curso_id = ? AND activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        $row = $stmt->fetch();
        return $row['total'];
    }

    // --- VIDEOCONFERENCIA ---
    public function obtenerVideoconferenciaActiva($cursoId) {
        $query = "SELECT * FROM enlaces_videoconferencia WHERE curso_id = ? AND activo = 1 LIMIT 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        return $stmt->fetch();
    }

    public function crearOActualizarVideoconferencia($cursoId, $titulo, $fecha, $hora, $url) {
        $actual = $this->obtenerVideoconferenciaActiva($cursoId);
        if ($actual) {
            $query = "UPDATE enlaces_videoconferencia SET titulo=?, fecha=?, hora=?, url=? WHERE id=?";
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([$titulo, $fecha, $hora, $url, $actual['id']]);
            return $actual['id'];
        } else {
            $query = "INSERT INTO enlaces_videoconferencia (curso_id, titulo, fecha, hora, url, activo) VALUES (?, ?, ?, ?, ?, 1)";
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([$cursoId, $titulo, $fecha, $hora, $url]);
            return $this->conexion->lastInsertId();
        }
    }

    public function eliminarVideoconferencia($id) {
        $query = "UPDATE enlaces_videoconferencia SET activo = 0 WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([$id]);
    }
}
?> 