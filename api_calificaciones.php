<?php
// API para gestionar calificaciones de estudiantes
session_start();
header('Content-Type: application/json');

function errorJson($msg, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $msg]);
    exit;
}

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    errorJson('Acceso denegado. Se requiere rol de maestro.', 403);
}

require_once 'Modelo/Conexion.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';
$cursoId = $_GET['curso_id'] ?? null;

if (!$cursoId) {
    errorJson('curso_id requerido');
}

$conexion = Conexion::getConexion();
$maestroId = $_SESSION['usuario_id'];

// Verificar que el maestro tiene acceso al curso
$queryAcceso = "SELECT COUNT(*) FROM cursos c 
                JOIN maestros m ON c.maestro_id = m.id 
                JOIN personas p ON m.persona_id = p.id 
                JOIN usuarios u ON p.usuario_id = u.id 
                WHERE c.id = ? AND u.id = ? AND c.activo = 1";
$stmtAcceso = $conexion->prepare($queryAcceso);
$stmtAcceso->execute([$cursoId, $maestroId]);

if ($stmtAcceso->fetchColumn() == 0) {
    errorJson('No tienes acceso a este curso', 403);
}

try {
    switch ($method) {
        case 'GET':
            if ($action === 'obtener') {
                // Obtener calificaciones existentes
                $query = "SELECT c.*, e.id as estudiante_id, p.nombres, p.apellido_paterno, p.apellido_materno
                         FROM calificaciones c
                         JOIN estudiantes e ON c.estudiante_id = e.id
                         JOIN personas p ON e.persona_id = p.id
                         JOIN inscripciones i ON e.id = i.estudiante_id
                         WHERE c.curso_id = ? AND i.curso_id = ? AND i.activo = 1
                         ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
                
                $stmt = $conexion->prepare($query);
                $stmt->execute([$cursoId, $cursoId]);
                $calificaciones = $stmt->fetchAll();
                
                echo json_encode(['success' => true, 'data' => $calificaciones]);
            } else {
                errorJson('Acción GET no válida');
            }
            break;

        case 'POST':
            if ($action === 'guardar') {
                $input = file_get_contents('php://input');
                $data = json_decode($input, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    errorJson('JSON inválido');
                }
                
                $estudianteId = $data['estudiante_id'] ?? null;
                $tareas = $data['tareas'] ?? 0;
                $examenes = $data['examenes'] ?? 0;
                $participacion = $data['participacion'] ?? 0;
                
                if (!$estudianteId) {
                    errorJson('estudiante_id requerido');
                }
                
                // Verificar que el estudiante está inscrito en el curso
                $queryInscrito = "SELECT COUNT(*) FROM inscripciones 
                                 WHERE estudiante_id = ? AND curso_id = ? AND activo = 1";
                $stmtInscrito = $conexion->prepare($queryInscrito);
                $stmtInscrito->execute([$estudianteId, $cursoId]);
                
                if ($stmtInscrito->fetchColumn() == 0) {
                    errorJson('El estudiante no está inscrito en este curso');
                }
                
                // Calcular promedio final
                $promedioFinal = ($tareas + $examenes + $participacion) / 3;
                
                // Obtener bimestre actual (por simplicidad, usamos el primer bimestre activo)
                $queryBimestre = "SELECT id FROM bimestres WHERE activo = 1 ORDER BY numero LIMIT 1";
                $stmtBimestre = $conexion->prepare($queryBimestre);
                $stmtBimestre->execute();
                $bimestreId = $stmtBimestre->fetchColumn();
                
                if (!$bimestreId) {
                    errorJson('No hay bimestre activo configurado');
                }
                
                // Insertar o actualizar calificación
                $queryUpsert = "INSERT INTO calificaciones 
                               (estudiante_id, curso_id, bimestre_id, promedio_tareas, promedio_examenes, promedio_participacion, calificacion_final)
                               VALUES (?, ?, ?, ?, ?, ?, ?)
                               ON DUPLICATE KEY UPDATE
                               promedio_tareas = VALUES(promedio_tareas),
                               promedio_examenes = VALUES(promedio_examenes),
                               promedio_participacion = VALUES(promedio_participacion),
                               calificacion_final = VALUES(calificacion_final),
                               updated_at = CURRENT_TIMESTAMP";
                
                $stmtUpsert = $conexion->prepare($queryUpsert);
                $resultado = $stmtUpsert->execute([
                    $estudianteId, $cursoId, $bimestreId, 
                    $tareas, $examenes, $participacion, $promedioFinal
                ]);
                
                if ($resultado) {
                    echo json_encode(['success' => true, 'mensaje' => 'Calificación guardada exitosamente']);
                } else {
                    errorJson('Error al guardar la calificación');
                }
                
            } else {
                errorJson('Acción POST no válida');
            }
            break;

        default:
            errorJson('Método no permitido', 405);
    }
    
} catch (Exception $e) {
    error_log('Error en API calificaciones: ' . $e->getMessage());
    errorJson('Error interno del servidor', 500);
}
?>
