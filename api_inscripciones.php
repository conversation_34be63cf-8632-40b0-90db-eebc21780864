<?php
// API para gestionar inscripciones de estudiantes en cursos
session_start();
header('Content-Type: application/json');

require_once 'Controlador/InscripcionController.php';

function errorJson($msg, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $msg]);
    exit;
}

// Debug de sesión
error_log('Sesión en API: ' . json_encode($_SESSION));
error_log('Headers recibidos: ' . json_encode(getallheaders()));

// Validar inicio de sesión y rol de maestro
if (!isset($_SESSION['usuario_id'])) {
    errorJson('Sesión no iniciada. Usuario ID no encontrado.', 401);
}

if ($_SESSION['rol'] !== 'maestro') {
    errorJson('Acceso denegado. Rol requerido: maestro. Rol actual: ' . ($_SESSION['rol'] ?? 'undefined'), 403);
}

$controller = new InscripcionController();
$maestroId = $_SESSION['usuario_id'];

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';
$cursoId = $_GET['curso_id'] ?? null;

if (!$cursoId) {
    errorJson('curso_id requerido');
}

try {
    switch ($method) {
        case 'GET':
            if ($action === 'inscritos') {
                $data = $controller->obtenerInscritos($cursoId);
                // Log para debugging
                error_log("Inscritos para curso $cursoId: " . json_encode($data));
                echo json_encode(['success' => true, 'data' => $data]);
            } elseif ($action === 'disponibles') {
                $data = $controller->obtenerDisponibles($cursoId);
                // Log para debugging
                error_log("Disponibles para curso $cursoId: " . json_encode($data));
                echo json_encode(['success' => true, 'data' => $data, 'debug' => [
                    'curso_id' => $cursoId,
                    'count' => count($data),
                    'sample' => array_slice($data, 0, 2) // Muestra primeros 2 registros
                ]]);
            } else {
                errorJson('Acción GET no válida');
            }
            break;

        case 'POST':
            if ($action === 'agregar') {
                $input = file_get_contents('php://input');
                $body = json_decode($input, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    errorJson('JSON inválido');
                }
                $estudianteId = $body['estudiante_id'] ?? null;
                if (!$estudianteId) {
                    errorJson('estudiante_id requerido');
                }
                if ($controller->agregarEstudiante($cursoId, $estudianteId)) {
                    echo json_encode(['success' => true, 'mensaje' => 'Estudiante agregado']);
                } else {
                    errorJson('No se pudo agregar (posible duplicado)');
                }
            } elseif ($action === 'eliminar') {
                $input = file_get_contents('php://input');
                $body = json_decode($input, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    errorJson('JSON inválido');
                }
                $estudianteId = $body['estudiante_id'] ?? null;
                if (!$estudianteId) {
                    errorJson('estudiante_id requerido');
                }
                if ($controller->eliminarEstudiante($cursoId, $estudianteId)) {
                    echo json_encode(['success' => true, 'mensaje' => 'Estudiante eliminado del curso']);
                } else {
                    errorJson('No se pudo eliminar');
                }
            } else {
                errorJson('Acción POST no válida');
            }
            break;
        default:
            errorJson('Método no permitido', 405);
    }
} catch (Exception $e) {
    errorJson('Error interno: '.$e->getMessage(), 500);
}
?>
