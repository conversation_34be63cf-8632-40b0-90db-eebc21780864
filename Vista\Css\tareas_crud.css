/* Estilos específicos para la página de CRUD de tareas */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Sección de acciones */
  .section-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 15px;
  }
  
  .search-container {
    position: relative;
    flex: 1;
    max-width: 300px;
  }
  
  .search-input {
    padding: 10px 15px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    width: 100%;
    height: 40px;
  }
  
  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
  }
  
  .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 15px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
  }
  
  .action-btn:hover {
    background-color: #1e40af;
  }
  
  .create-task-btn {
    background-color: var(--success-color);
  }
  
  .create-task-btn:hover {
    background-color: #3d8b40;
  }
  
  .view-mode-btn {
    background-color: var(--info-color);
  }
  
  .view-mode-btn:hover {
    background-color: #0b7dda;
  }
  
  /* Tabla de tareas */
  .tasks-table-container {
    margin-top: 20px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  .tasks-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  
  .tasks-table th,
  .tasks-table td {
    padding: 18px 20px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
  }
  
  .tasks-table th {
    background-color: var(--secondary-color);
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
  }
  
  .tasks-table th:nth-child(1) { width: 18%; } /* Título */
  .tasks-table th:nth-child(2) { width: 10%; } /* Tipo */
  .tasks-table th:nth-child(3) { width: 18%; } /* Semana */
  .tasks-table th:nth-child(4) { width: 12%; } /* Fecha */
  .tasks-table th:nth-child(5) { width: 10%; } /* Hora */
  .tasks-table th:nth-child(6) { width: 8%; } /* Puntos */
  .tasks-table th:nth-child(7) { width: 12%; } /* Entregas */
  .tasks-table th:nth-child(8) { width: 12%; } /* Acciones */
  
  .tasks-table tbody tr {
    transition: var(--transition);
  }
  
  .tasks-table tbody tr:hover {
    background-color: var(--secondary-color);
  }
  
  .tasks-table tbody tr:last-child td {
    border-bottom: none;
  }
  
  /* Estilos para las celdas de la tabla */
  .task-title {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .task-week {
    font-size: 0.9rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Estilos para tipo de contenido */
  .content-type {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: fit-content;
  }

  .content-type-task {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(33, 150, 243, 0.2);
  }

  .content-type-exam {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.2);
  }

  .task-due-date {
    font-size: 0.9rem;
    color: var(--text-color);
  }

  .task-due-time {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
    text-align: center;
  }
  
  .task-points {
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
  }
  
  .task-status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    width: fit-content;
  }
  
  .task-status.active {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
  }
  
  .task-status.draft {
    background-color: rgba(158, 158, 158, 0.1);
    color: #757575;
  }
  
  .task-status.closed {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .task-submissions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
  }
  
  .task-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
  }
  
  .task-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .task-action-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .task-action-btn.edit-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .task-action-btn.delete-btn:hover {
    color: var(--danger-color);
    border-color: var(--danger-color);
  }
  
  .task-action-btn.view-btn:hover {
    color: var(--info-color);
    border-color: var(--info-color);
  }
  
  /* Paginación */
  .pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  
  .pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .pagination-info {
    font-size: 0.95rem;
    color: var(--text-color);
  }
  
  /* Modal de formulario */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
  }
  
  .form-group textarea {
    resize: vertical;
  }

  .required {
    color: var(--danger-color);
  }

  /* Editor de texto enriquecido */
  .rich-text-toolbar {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    flex-wrap: wrap;
  }

  .toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    font-weight: 600;
  }

  .toolbar-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .toolbar-btn.active {
    background-color: var(--primary-color);
    color: white;
  }

  .toolbar-separator {
    width: 1px;
    height: 20px;
    background-color: var(--border-color);
    margin: 0 5px;
  }

  .rich-text-editor {
    min-height: 150px;
    max-height: 300px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 0 0 5px 5px;
    background-color: white;
    overflow-y: auto;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .rich-text-editor:focus {
    outline: none;
    border-color: var(--primary-color);
  }

  .rich-text-editor[placeholder]:empty:before {
    content: attr(placeholder);
    color: var(--text-light);
    font-style: italic;
  }

  .rich-text-editor img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 10px 0;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
  }
  
  .btn-secondary,
  .btn-primary,
  .btn-danger {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-color);
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  .btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #d32f2f;
  }
  
  /* Modal de confirmación */
  .confirmation-modal {
    max-width: 450px;
  }
  
  .confirmation-message {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    text-align: center;
  }
  
  .confirmation-message p {
    margin: 0;
    font-size: 1rem;
    color: var(--text-color);
    line-height: 1.5;
  }
  
  /* Mensaje de tabla vacía */
  .empty-table-message {
    text-align: center;
    padding: 30px 0;
    color: var(--text-light);
    font-size: 1rem;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .section-actions {
      flex-wrap: wrap;
    }
    
    .search-container {
      order: 3;
      max-width: 100%;
      width: 100%;
    }
    
    .tasks-table {
      display: block;
      overflow-x: auto;
      white-space: nowrap;
      table-layout: auto;
    }
    
    .tasks-table th, 
    .tasks-table td {
      min-width: 120px;
    }
    
    .tasks-table th:last-child, 
    .tasks-table td:last-child {
      min-width: 150px;
    }
    
    .form-row {
      flex-direction: column;
      gap: 20px;
    }
  }
  
  @media (max-width: 768px) {
    .section-actions {
      flex-direction: column;
      align-items: stretch;
    }
    
    .action-btn {
      width: 100%;
      justify-content: center;
    }
    
    .task-actions {
      flex-direction: row;
      justify-content: center;
    }
    
    .form-actions {
      flex-direction: column;
    }
    
    .btn-secondary,
    .btn-primary,
    .btn-danger {
      width: 100%;
    }
    
    .modal-content {
      width: 95%;
    }

    .task-info-grid {
      flex-direction: column;
      gap: 15px;
    }

    .task-info-item {
      min-width: auto;
    }
  }
  
  /* Modal de vista de tarea */
  .task-view-modal {
    max-width: 800px;
  }

  .task-view-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
  }

  .task-description-section h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
  }

  .task-description-content {
    padding: 20px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
  }

  .task-image-section {
    text-align: center;
    margin: 20px 0;
  }

  .task-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
  }

  .task-info-grid {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .task-info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    flex: 1;
    min-width: 200px;
  }

  .task-info-item .material-icons {
    font-size: 24px;
    color: var(--primary-color);
  }

  .task-info-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .task-info-label {
    font-size: 0.85rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .task-info-value {
    font-size: 0.95rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .task-options-section h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
  }

  .task-options-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
  }

  /* Eliminar estilos de notificación */
  .notification {
    display: none !important;
  }

  /* Vista de tarea - Estilos consistentes */
  .assignment-container {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
  }

  .assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px 10px 0 0;
  }

  .assignment-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
  }

  .assignment-title .material-icons {
    font-size: 1.8rem;
  }

  .grade-pending {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .assignment-content {
    padding: 0;
  }

  .assignment-section {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
  }

  .assignment-section:last-child {
    border-bottom: none;
  }

  .assignment-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .assignment-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
  }

  .assignment-image {
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .assignment-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .meta-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .meta-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .meta-label .material-icons {
    font-size: 1.2rem;
    color: var(--primary-color);
  }

  .meta-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
  }

  .submission-section {
    padding: 0;
  }

  .submission-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .task-status-info {
    margin-bottom: 20px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }

  .status-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
  }

  .status-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .status-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .status-value {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
  }

  .submission-options {
    margin-top: 20px;
  }
