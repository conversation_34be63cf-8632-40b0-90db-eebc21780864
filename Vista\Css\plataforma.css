/* Estilos para la Plataforma Educativa */

/* Estilos generales */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --header-height: 70px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: "Poppins", "Segoe UI", sans-serif;
  color: var(--text-color);
  background-color: var(--secondary-color);
  min-height: 100vh;
  display: flex;
  margin: 0;
}

.plataforma-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* Estilos del menú lateral */
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background-color: white;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  z-index: 100;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  width: auto;
}

.menu-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: var(--transition);
}

.menu-toggle:hover {
  background-color: var(--secondary-color);
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.user-info {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-bottom: 1px solid var(--border-color);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--primary-light);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.user-details p {
  font-size: 0.8rem;
  color: var(--text-light);
}

.sidebar-menu {
  padding: 20px 0;
}

.sidebar-menu ul {
  list-style: none;
}

.sidebar-menu li {
  margin-bottom: 5px;
}

.sidebar-menu li a {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  border-left: 3px solid transparent;
}

.sidebar-menu li.active a {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.sidebar-menu li a:hover {
  background-color: var(--secondary-color);
}

.sidebar-menu li.separator {
  height: 1px;
  background-color: var(--border-color);
  margin: 15px 20px;
}

.sidebar.collapsed .user-details,
.sidebar.collapsed .sidebar-menu li a span:not(.material-icons) {
  display: none;
}

.sidebar.collapsed .user-info {
  justify-content: center;
  padding: 20px 0;
}

.sidebar.collapsed .sidebar-menu li a {
  justify-content: center;
  padding: 15px 0;
}

.sidebar.collapsed .logo {
  display: none;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
}

/* Estilos del contenido principal */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.content-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.header-left p {
  font-size: 0.9rem;
  color: var(--text-light);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-btn {
  background: none;
  border: none;
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: var(--transition);
}

.notification-btn:hover {
  background-color: var(--secondary-color);
}

.notification-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: var(--danger-color);
  color: white;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-body {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* Secciones del dashboard */
.dashboard-section {
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.view-all {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

/* Anuncios */
.announcement-section {
  padding: 0;
  overflow: hidden;
}

.announcement-banner {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #fff8e1;
  border-left: 4px solid var(--warning-color);
}

.announcement-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--warning-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.announcement-icon .material-icons {
  color: white;
  font-size: 1.8rem;
}

.announcement-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.announcement-content p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
}

.announcement-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.announcement-link:hover {
  text-decoration: underline;
}

/* Tarjetas de actividad */
.activity-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.activity-card {
  background-color: var(--primary-light);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: var(--transition);
}

.activity-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.activity-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon .material-icons {
  color: white;
  font-size: 1.8rem;
}

.activity-info h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.activity-count {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Lista de tareas */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 10px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.task-item:hover {
  box-shadow: var(--shadow-sm);
}

.task-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.task-status.pending {
  background-color: var(--warning-color);
}

.task-status.completed {
  background-color: var(--success-color);
}

.task-content {
  flex: 1;
}

.task-content h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.task-content p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.8rem;
}

.task-course {
  color: var(--primary-color);
  font-weight: 500;
}

.task-date {
  color: var(--text-light);
}

.task-actions {
  display: flex;
  gap: 10px;
}

.task-action-btn {
  background: none;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.task-action-btn:hover {
  background-color: var(--secondary-color);
  color: var(--primary-color);
}

/* Horario */
.schedule {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  border-radius: 8px;
  border-left: 3px solid transparent;
  transition: var(--transition);
}

.schedule-item:hover {
  background-color: var(--secondary-color);
}

.schedule-item.current {
  background-color: var(--primary-light);
  border-left-color: var(--primary-color);
}

.schedule-time {
  width: 100px;
  flex-shrink: 0;
  font-size: 0.9rem;
  color: var(--text-light);
}

.schedule-content h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 3px;
}

.schedule-content p {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Recursos destacados */
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.resource-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  text-decoration: none;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.resource-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.resource-icon .material-icons {
  color: var(--primary-color);
  font-size: 1.8rem;
}

.resource-info h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.resource-info p {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Filtro de cursos */
.filter-section {
  margin-bottom: 30px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: white;
  border-radius: 10px;
  padding: 15px 20px;
  box-shadow: var(--shadow-sm);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: var(--secondary-color);
  border-radius: 5px;
  padding: 10px 15px;
}

.search-box .material-icons {
  color: var(--text-light);
}

.search-box input {
  flex: 1;
  border: none;
  background: none;
  font-size: 0.9rem;
  color: var(--text-color);
  outline: none;
}

.filter-options select {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 0.9rem;
  color: var(--text-color);
  background-color: white;
  outline: none;
  cursor: pointer;
}

/* Cursos - Nuevo diseño */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); /* Aumentado de 280px a 320px */
  gap: 20px;
}

.course-card-new {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  text-decoration: none;
  color: var(--text-color);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  min-width: 300px; /* Añadido para asegurar un ancho mínimo */
}

.course-card-new:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.course-card-header {
  height: 160px;
  position: relative;
  overflow: hidden;
}

.course-card-header img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-card-new:hover .course-card-header img {
  transform: scale(1.05);
}

.course-icon-new {
  position: absolute;
  bottom: 15px;
  left: 15px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  z-index: 1;
}

.course-icon-new .material-icons {
  color: var(--text-color);
  font-size: 1.5rem;
}

.course-card-body {
  padding: 20px;
  flex: 1;
}

.course-card-body h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.course-teacher {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 15px;
}

.course-schedule {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.course-time {
  font-size: 0.9rem;
  color: var(--text-light);
}

.course-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
}

.course-progress {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.progress-bar {
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--success-color);
  border-radius: 3px;
}

.progress-text {
  font-size: 0.8rem;
  color: var(--text-light);
  text-align: right;
}

/* Encabezado del curso */
.course-header {
  padding: 30px;
  color: white;
}

.course-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: var(--transition);
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.course-title h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: white;
}

.course-title p {
  font-size: 1rem;
  opacity: 0.8;
}

.course-header-right {
  display: flex;
  align-items: center;
}

.course-schedule-info {
  display: flex;
  gap: 20px;
}

.schedule-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10px 15px;
  border-radius: 8px;
}

.day-label {
  font-weight: 500;
  margin-bottom: 5px;
}

.day-time {
  font-size: 0.85rem;
  opacity: 0.8;
}

/* Navegación del curso */
.course-navigation {
  background-color: white;
  padding: 0 30px;
  box-shadow: var(--shadow-sm);
}

.course-nav-tabs {
  display: flex;
  gap: 30px;
}

.course-tab {
  padding: 15px 0;
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: var(--transition);
}

.course-tab:hover {
  color: var(--primary-color);
}

.course-tab.active {
  color: var(--primary-color);
}

.course-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px 3px 0 0;
}

/* Sección de videoconferencia */
.meet-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  background-color: var(--secondary-color);
  border-radius: 8px;
  padding: 20px;
}

.no-meet-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
}

.no-meet-message .material-icons {
  font-size: 3rem;
  opacity: 0.5;
}

.no-meet-message p {
  font-size: 1rem;
}

/* Sección de videoconferencia activa */
.meet-section.active-meet {
  background-color: #e8f5e9;
  min-height: auto;
  padding: 0;
}

.meet-info {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  width: 100%;
}

.meet-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #4caf50;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.meet-icon .material-icons {
  color: white;
  font-size: 1.8rem;
}

.meet-details {
  flex: 1;
}

.meet-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.meet-details p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
}

.meet-link {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 8px 16px;
  background-color: var(--primary-light);
  border-radius: 20px;
  transition: var(--transition);
}

.meet-link:hover {
  background-color: #d4e6f9;
}

/* Contenido del curso */
.course-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.week-container {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.week-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--secondary-color);
  cursor: pointer;
  transition: var(--transition);
}

.week-header:hover {
  background-color: #e8eef7;
}

.week-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
}

.week-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out, padding 0.3s ease-out;
  padding: 0 20px;
}

.week-content.expanded {
  max-height: 1000px;
  padding: 10px 20px;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.content-item:last-child {
  border-bottom: none;
}

.content-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.content-icon.pdf {
  background-color: #ffebee;
}

.content-icon.pdf .material-icons {
  color: #f44336;
}

.content-icon.ppt {
  background-color: #fff8e1;
}

.content-icon.ppt .material-icons {
  color: #ff9800;
}

.content-icon.task {
  background-color: #e8f5e9;
}

.content-icon.task .material-icons {
  color: #4caf50;
}

.content-icon.link {
  background-color: #e3f2fd;
}

.content-icon.link .material-icons {
  color: #2196f3;
}

.content-icon.announcement {
  background-color: #ede7f6;
}

.content-icon.announcement .material-icons {
  color: #673ab7;
}

.content-icon.video {
  background-color: #ffebee;
}

.content-icon.video .material-icons {
  color: #e91e63;
}

.content-icon.document {
  background-color: #f3e5f5;
}

.content-icon.document .material-icons {
  color: #9c27b0;
}

.content-icon.presentation {
  background-color: #fff3e0;
}

.content-icon.presentation .material-icons {
  color: #ff9800;
}

.content-icon.participation {
  background-color: #e0f2f1;
}

.content-icon.participation .material-icons {
  color: #009688;
}

.content-icon.exam {
  background-color: #fce4ec;
}

.content-icon.exam .material-icons {
  color: #e91e63;
}

/* Estilos básicos para modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.modal-close-btn:hover {
  background-color: #f5f5f5;
}

.modal-body {
  padding: 24px;
}

/* Estilos para modales de contenido */
.video-container {
  margin-bottom: 20px;
}

.video-description {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.video-description ul {
  margin: 10px 0;
  padding-left: 20px;
}

.link-info {
  text-align: center;
  padding: 20px;
}

.link-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #2196f3;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.3s;
}

.link-button:hover {
  background-color: #1976d2;
}

.document-viewer {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.document-actions {
  text-align: center;
}

.download-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #4caf50;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.3s;
}

.download-button:hover {
  background-color: #388e3c;
}

.presentation-viewer {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.presentation-actions {
  text-align: center;
}

.presentation-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.presentation-button:hover {
  background-color: #f57c00;
}

.participation-info {
  padding: 20px;
}

.participation-info ul {
  margin: 15px 0;
  padding-left: 20px;
}

.participation-grade {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grade-label {
  font-weight: 500;
}

.grade-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #666;
}

/* Estilos adicionales para modales de contenido */
.modal-large {
  max-width: 900px;
  width: 90%;
  max-height: 90vh;
}

.document-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.document-info {
  padding: 15px;
  background-color: var(--secondary-color);
  border-radius: 8px;
}

.document-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.document-meta span {
  font-size: 0.9rem;
  color: var(--text-light);
}

.document-type {
  background-color: var(--primary-light);
  color: var(--primary-color) !important;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.document-content {
  flex: 1;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.document-description-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #007bff;
}

.document-description-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.document-description-content {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  font-style: normal;
}

.document-description-content p {
  margin: 0 0 12px 0;
  color: #333;
  line-height: 1.6;
  font-style: normal;
}

.document-description-content ul {
  margin: 0;
  padding-left: 20px;
}

.document-description-content li {
  margin-bottom: 8px;
  color: #333;
  line-height: 1.5;
}

.document-placeholder {
  text-align: center;
  color: var(--text-light);
}

.document-placeholder .material-icons {
  font-size: 4rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.document-placeholder p {
  margin: 5px 0;
}

.document-filename {
  margin: 8px 0 0 0 !important;
  color: #007bff !important;
  font-size: 13px !important;
  font-weight: 500;
}

.document-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

/* Estilos para vista previa de estudiante */
.student-preview-container {
  max-width: 800px;
  margin: 0 auto;
}

.student-content-item {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.content-icon-large {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #666;
}

.content-icon-large .material-icons {
  font-size: 24px;
}

.video-preview .content-icon-large {
  background-color: #ffebee;
  color: #e91e63;
}

.link-preview .content-icon-large {
  background-color: #e3f2fd;
  color: #2196f3;
}

.presentation-preview .content-icon-large {
  background-color: #fff3e0;
  color: #ff9800;
}

.participation-preview .content-icon-large {
  background-color: #e0f2f1;
  color: #009688;
}

.content-info h2 {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.content-meta {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.content-body {
  line-height: 1.6;
  color: #444;
}

.content-body p {
  margin-bottom: 16px;
}

.content-url {
  margin-bottom: 15px;
}

.content-url a {
  color: #2196f3;
  text-decoration: none;
  word-break: break-all;
}

.content-url a:hover {
  text-decoration: underline;
}

.video-preview {
  margin-top: 15px;
}

.video-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
}

.video-thumbnail .play-icon {
  font-size: 48px;
  color: #2196f3;
  margin-bottom: 10px;
}

.video-thumbnail p {
  margin: 5px 0;
  color: #666;
}

.link-actions,
.presentation-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.btn-primary {
  background-color: #2196f3;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1976d2;
}

.btn-secondary {
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #f5f5f5;
}

.content-details {
  flex: 1;
}

.content-details h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.content-details p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.content-date {
  font-size: 0.8rem;
  color: var(--text-light);
}

.content-actions {
  display: flex;
  gap: 10px;
}

.content-action-btn {
  background: none;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.content-action-btn:hover {
  background-color: var(--secondary-color);
  color: var(--primary-color);
}

/* Mensaje de contenido vacío */
.empty-content {
  padding: 30px;
  text-align: center;
  color: var(--text-light);
}

.empty-content .material-icons {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-content p {
  font-size: 1rem;
}

/* Estudiantes matriculados */
.student-count {
  font-size: 0.9rem;
  color: var(--text-light);
  background-color: var(--secondary-color);
  padding: 5px 10px;
  border-radius: 20px;
}

.students-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.student-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  transition: var(--transition);
}

.student-item:hover {
  background-color: var(--secondary-color);
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--primary-light);
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-info h4 {
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 3px;
}

.student-info p {
  font-size: 0.8rem;
  color: var(--text-light);
}

.view-more-container {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.view-more-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 8px 15px;
  border-radius: 20px;
  transition: var(--transition);
}

.view-more-btn:hover {
  background-color: var(--primary-light);
}

/* Estilos para la vista de tarea */
.assignment-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 30px;
}

.assignment-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assignment-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.assignment-title .material-icons {
  color: var(--primary-color);
}

.assignment-actions {
  display: flex;
  gap: 10px;
}

.assignment-action-btn {
  padding: 8px 16px;
  border-radius: 5px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: var(--transition);
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.primary-btn:hover {
  background-color: #1e40af;
}

.secondary-btn {
  background-color: white;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.secondary-btn:hover {
  background-color: var(--secondary-color);
}

.assignment-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.assignment-tab {
  padding: 15px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.assignment-tab.active {
  color: var(--primary-color);
}

.assignment-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.assignment-content {
  padding: 30px;
}

.assignment-section {
  margin-bottom: 30px;
}

.assignment-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.assignment-description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 20px;
}

.assignment-image {
  max-width: 100%;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid var(--border-color);
}

.assignment-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.meta-label {
  font-size: 0.9rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
  gap: 5px;
}

.meta-label .material-icons {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.meta-value {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
}

.meta-value a {
  color: var(--primary-color);
  text-decoration: none;
}

.meta-value a:hover {
  text-decoration: underline;
}

.submission-section {
  background-color: var(--secondary-color);
  border-radius: 8px;
  padding: 20px;
}

.submission-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.submission-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background-color: white;
  cursor: pointer;
  transition: var(--transition);
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

.file-upload-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.file-upload-text {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 10px;
}

.file-upload-hint {
  font-size: 0.85rem;
  color: var(--text-light);
}

.text-submission-area {
  margin-top: 20px;
}

.text-submission-label {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--text-color);
}

.text-submission-textarea {
  width: 100%;
  min-height: 150px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--text-color);
  resize: vertical;
  font-family: inherit;
}

.submission-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Botón de menú móvil */
.mobile-menu-btn {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-color);
  transition: var(--transition);
}

.mobile-menu-btn:hover {
  background-color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 992px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    transform: translateX(-100%);
    z-index: 1000;
    height: 100%;
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .content-header {
    padding: 15px 20px;
    height: auto;
  }

  .content-body {
    padding: 20px;
  }

  .course-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .course-header-right {
    width: 100%;
  }

  .course-schedule-info {
    width: 100%;
    justify-content: space-between;
  }

  .meet-info {
    flex-direction: column;
    text-align: center;
  }

  .meet-icon {
    margin: 0 auto;
  }

  .assignment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .assignment-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .assignment-meta {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .activity-cards {
    grid-template-columns: 1fr;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .course-nav-tabs {
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .students-list {
    grid-template-columns: 1fr;
  }

  .assignment-tabs {
    overflow-x: auto;
  }

  .assignment-tab {
    white-space: nowrap;
  }
}