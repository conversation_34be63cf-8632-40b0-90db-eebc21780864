document.addEventListener("DOMContentLoaded", () => {
    // Datos de ejemplo para estudiantes
    const students = [
      { id: 1, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 2, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 3, name: "<PERSON><PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 4, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 5, name: "<PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 6, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 7, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 8, name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 9, name: "<PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
      { id: 10, name: "<PERSON><PERSON><PERSON><PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
    ]
  
    // <PERSON>tos de ejemplo para asistencias
    const attendanceData = {
      "2025-03-22": {
        1: "present",
        2: "present",
        3: "absent",
        4: "late",
        5: "present",
        6: "excused",
        7: "present",
        8: "present",
        9: "late",
        10: "present",
      },
      "2025-03-20": {
        1: "present",
        2: "present",
        3: "present",
        4: "present",
        5: "present",
        6: "present",
        7: "present",
        8: "absent",
        9: "present",
        10: "late",
      },
      "2025-03-18": {
        1: "present",
        2: "absent",
        3: "present",
        4: "present",
        5: "excused",
        6: "present",
        7: "late",
        8: "present",
        9: "present",
        10: "present",
      },
      "2025-03-15": {
        1: "late",
        2: "present",
        3: "present",
        4: "absent",
        5: "present",
        6: "present",
        7: "absent",
        8: "present",
        9: "excused",
        10: "present",
      },
      "2025-03-13": {
        1: "present",
        2: "present",
        3: "late",
        4: "present",
        5: "absent",
        6: "present",
        7: "present",
        8: "present",
        9: "present",
        10: "excused",
      },
      "2025-03-11": {
        1: "present",
        2: "late",
        3: "present",
        4: "present",
        5: "present",
        6: "absent",
        7: "present",
        8: "excused",
        9: "present",
        10: "present",
      },
      "2025-03-08": {
        1: "absent",
        2: "present",
        3: "present",
        4: "late",
        5: "present",
        6: "present",
        7: "present",
        8: "present",
        9: "present",
        10: "present",
      },
      "2025-03-06": {
        1: "present",
        2: "present",
        3: "present",
        4: "present",
        5: "late",
        6: "present",
        7: "excused",
        8: "present",
        9: "absent",
        10: "present",
      },
    }
  
    // Fechas de clase
    const classDates = ["2025-03-06", "2025-03-08", "2025-03-11", "2025-03-13", "2025-03-15", "2025-03-18", "2025-03-20", "2025-03-22", "2025-03-25", "2025-03-27"]
  
    // Fecha actual seleccionada (por defecto la fecha actual)
    let currentDate = new Date("2025-03-22")
  
    // Elementos DOM
    const attendanceList = document.getElementById("attendance-list")
    const currentDateDisplay = document.querySelector(".date-display")
    const prevDateBtn = document.getElementById("prev-date")
    const nextDateBtn = document.getElementById("next-date")
    const calendarBtn = document.getElementById("open-calendar-btn")
    const calendarModal = document.getElementById("calendar-modal")
    const selectDateBtn = document.getElementById("select-date-btn")
    const datePicker = document.getElementById("date-picker")
    const presentCount = document.getElementById("present-count")
    const absentCount = document.getElementById("absent-count")
    const lateCount = document.getElementById("late-count")
    const excusedCount = document.getElementById("excused-count")
    const studentFilter = document.getElementById("student-filter")
    const monthFilter = document.getElementById("month-filter")
    const attendanceCalendar = document.getElementById("attendance-calendar")
    const saveAttendanceBtn = document.getElementById("save-attendance-btn")
    const resetAttendanceBtn = document.getElementById("reset-attendance-btn")
    const historyDetailModal = document.getElementById("history-detail-modal")
    const changeStatusModal = document.getElementById("change-status-modal")
  
    // Inicializar la página
    init()
  
    function init() {
      // Cargar la lista de estudiantes
      loadStudentList()
  
      // Actualizar la fecha mostrada
      updateDateDisplay()
  
      // Cargar el calendario de historial
      loadAttendanceCalendar()
  
      // Cargar opciones de estudiantes en el filtro
      loadStudentFilter()
  
      // Inicializar el selector de fecha
      initDatePicker()
  
      // Actualizar contadores
      updateAttendanceCounts()
  
      // Configurar event listeners
      setupEventListeners()
    }
  
    function loadStudentList() {
      attendanceList.innerHTML = ""
  
      const dateString = formatDateForData(currentDate)
      const dayAttendance = attendanceData[dateString] || {}
  
      students.forEach((student) => {
        const status = dayAttendance[student.id] || ""
  
        const row = document.createElement("tr")
        row.innerHTML = `
              <td>
                  <div class="student-info">
                      <div class="student-avatar">
                          <img src="${student.avatar}" alt="${student.name}">
                      </div>
                      <div class="student-details">
                          <span class="student-name">${student.name}</span>
                      </div>
                  </div>
              </td>
              <td>
                  <div class="attendance-status-selector" data-student-id="${student.id}">
                      <div class="status-option present ${status === "present" ? "active" : ""}" data-status="present" onclick="this.parentElement.querySelectorAll('.status-option').forEach(opt => opt.classList.remove('active')); this.classList.add('active'); updateAttendance('${student.id}', 'present');">Presente</div>
                      <div class="status-option absent ${status === "absent" ? "active" : ""}" data-status="absent" onclick="this.parentElement.querySelectorAll('.status-option').forEach(opt => opt.classList.remove('active')); this.classList.add('active'); updateAttendance('${student.id}', 'absent');">Faltó</div>
                      <div class="status-option late ${status === "late" ? "active" : ""}" data-status="late" onclick="this.parentElement.querySelectorAll('.status-option').forEach(opt => opt.classList.remove('active')); this.classList.add('active'); updateAttendance('${student.id}', 'late');">Tardanza</div>
                      <div class="status-option excused ${status === "excused" ? "active" : ""}" data-status="excused" onclick="this.parentElement.querySelectorAll('.status-option').forEach(opt => opt.classList.remove('active')); this.classList.add('active'); updateAttendance('${student.id}', 'excused');">Justificado</div>
                  </div>
              </td>
          `
  
        attendanceList.appendChild(row)
      })
  
      // Definir la función updateAttendance en el ámbito global
      window.updateAttendance = (studentId, status) => {
        const dateString = formatDateForData(currentDate)
        if (!attendanceData[dateString]) {
          attendanceData[dateString] = {}
        }
        attendanceData[dateString][studentId] = status
  
        // Actualizar contadores
        updateAttendanceCounts()
  
        // Actualizar calendario
        loadAttendanceCalendar()
      }
    }
  
    function updateDateDisplay() {
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      currentDateDisplay.textContent = currentDate.toLocaleDateString("es-ES", options)
    }
  
    function formatDateForData(date) {
      return date.toISOString().split("T")[0]
    }
  
    function updateAttendanceCounts() {
      const dateString = formatDateForData(currentDate)
      const dayAttendance = attendanceData[dateString] || {}
  
      let present = 0
      let absent = 0
      let late = 0
      let excused = 0
  
      Object.values(dayAttendance).forEach((status) => {
        if (status === "present") present++
        else if (status === "absent") absent++
        else if (status === "late") late++
        else if (status === "excused") excused++
      })
  
      presentCount.textContent = present
      absentCount.textContent = absent
      lateCount.textContent = late
      excusedCount.textContent = excused
    }
  
    function loadStudentFilter() {
      studentFilter.innerHTML = '<option value="all">Todos los estudiantes</option>'
  
      students.forEach((student) => {
        const option = document.createElement("option")
        option.value = student.id
        option.textContent = student.name
        studentFilter.appendChild(option)
      })
    }
  
    function loadAttendanceCalendar() {
      attendanceCalendar.innerHTML = ""
  
      // Crear encabezados de días de la semana
      const weekdays = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]
      weekdays.forEach((day) => {
        const dayHeader = document.createElement("div")
        dayHeader.className = "calendar-weekday"
        dayHeader.textContent = day
        attendanceCalendar.appendChild(dayHeader)
      })
  
      // Obtener el mes seleccionado
      const selectedMonth = Number.parseInt(monthFilter.value)
      const year = 2025
  
      // Crear días del mes
      const daysInMonth = new Date(year, selectedMonth, 0).getDate()
      const firstDay = new Date(year, selectedMonth - 1, 1).getDay()
  
      // Agregar espacios en blanco para los días anteriores al primer día del mes
      for (let i = 0; i < firstDay; i++) {
        const emptyDay = document.createElement("div")
        emptyDay.className = "calendar-day disabled"
        attendanceCalendar.appendChild(emptyDay)
      }
  
      // Agregar los días del mes
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, selectedMonth - 1, day)
        const dateString = formatDateForData(date)
        const isClassDay = classDates.includes(dateString)
        const isToday = dateString === formatDateForData(currentDate)
  
        const calendarDay = document.createElement("div")
        calendarDay.className = `calendar-day ${isClassDay ? "has-class" : ""} ${isToday ? "today" : ""}`
        calendarDay.dataset.date = dateString
  
        const dayNumber = document.createElement("div")
        dayNumber.className = "day-number"
        dayNumber.textContent = day
  
        const dayStatus = document.createElement("div")
        dayStatus.className = "day-status"
  
        if (isClassDay && attendanceData[dateString]) {
          // Filtrar por estudiante si es necesario
          const selectedStudent = studentFilter.value
          const statusCounts = { present: 0, absent: 0, late: 0, excused: 0 }
  
          if (selectedStudent === "all") {
            // Contar todos los estados
            Object.values(attendanceData[dateString]).forEach((status) => {
              statusCounts[status]++
            })
          } else {
            // Solo mostrar el estado del estudiante seleccionado
            const studentStatus = attendanceData[dateString][selectedStudent]
            if (studentStatus) {
              statusCounts[studentStatus] = 1
            }
          }
  
          // Crear puntos de estado
          if (statusCounts.present > 0) {
            const dot = document.createElement("div")
            dot.className = "status-dot present"
            dayStatus.appendChild(dot)
          }
          if (statusCounts.absent > 0) {
            const dot = document.createElement("div")
            dot.className = "status-dot absent"
            dayStatus.appendChild(dot)
          }
          if (statusCounts.late > 0) {
            const dot = document.createElement("div")
            dot.className = "status-dot late"
            dayStatus.appendChild(dot)
          }
          if (statusCounts.excused > 0) {
            const dot = document.createElement("div")
            dot.className = "status-dot excused"
            dayStatus.appendChild(dot)
          }
        }
  
        calendarDay.appendChild(dayNumber)
        calendarDay.appendChild(dayStatus)
  
        if (isClassDay) {
          calendarDay.addEventListener("click", () => {
            // Mostrar el modal de detalle de asistencia
            showHistoryDetailModal(dateString)
          })
        }
  
        attendanceCalendar.appendChild(calendarDay)
      }
    }
  
    function showHistoryDetailModal(dateString) {
      // Crear el contenido del modal
      const date = new Date(dateString)
      const formattedDate = date.toLocaleDateString("es-ES", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      })
  
      // Obtener los datos de asistencia para esa fecha
      const dayAttendance = attendanceData[dateString] || {}
  
      // Crear la tabla de asistencia
      let tableContent = `
        <div class="history-detail-header">
          <h3>Asistencia del ${formattedDate}</h3>
        </div>
        <div class="history-detail-content">
          <table class="attendance-detail-table">
            <thead>
              <tr>
                <th>Estudiante</th>
                <th>Estado</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
      `
  
      students.forEach((student) => {
        const status = dayAttendance[student.id] || ""
        let statusText = ""
        let statusClass = ""
  
        if (status === "present") {
          statusText = "Presente"
          statusClass = "present"
        } else if (status === "absent") {
          statusText = "Ausente"
          statusClass = "absent"
        } else if (status === "late") {
          statusText = "Tardanza"
          statusClass = "late"
        } else if (status === "excused") {
          statusText = "Justificado"
          statusClass = "excused"
        } else {
          statusText = "No registrado"
        }
  
        tableContent += `
          <tr>
            <td>
              <div class="student-info">
                <div class="student-avatar">
                  <img src="${student.avatar}" alt="${student.name}">
                </div>
                <div class="student-details">
                  <span class="student-name">${student.name}</span>
                </div>
              </div>
            </td>
            <td>
              <div class="status-badge ${statusClass}">${statusText}</div>
            </td>
            <td>
              <button class="action-btn change-status-btn" onclick="showChangeStatusModal('${dateString}', ${student.id}, '${status}')">
                <span class="material-icons">edit</span>
                Cambiar
              </button>
            </td>
          </tr>
        `
      })
  
      tableContent += `
            </tbody>
          </table>
        </div>
        <div class="modal-actions">
          <button class="btn-secondary modal-close-btn">Cerrar</button>
        </div>
      `
  
      // Crear el modal si no existe
      if (!historyDetailModal) {
        const modal = document.createElement("div")
        modal.id = "history-detail-modal"
        modal.className = "modal-overlay"
        modal.innerHTML = `
          <div class="modal-content history-detail-container">
            ${tableContent}
          </div>
        `
        document.body.appendChild(modal)
  
        // Agregar event listener para cerrar el modal
        modal.querySelector(".modal-close-btn").addEventListener("click", () => {
          modal.classList.remove("active")
        })
      } else {
        historyDetailModal.querySelector(".modal-content").innerHTML = tableContent
  
        // Agregar event listener para cerrar el modal
        historyDetailModal.querySelector(".modal-close-btn").addEventListener("click", () => {
          historyDetailModal.classList.remove("active")
        })
      }
  
      // Mostrar el modal
      document.getElementById("history-detail-modal").classList.add("active")
  
      // Definir la función showChangeStatusModal en el ámbito global
      window.showChangeStatusModal = (dateString, studentId, currentStatus) => {
        // Obtener el nombre del estudiante
        const student = students.find((s) => s.id == studentId)
  
        // Crear el contenido del modal
        const modalContent = `
          <div class="modal-header">
            <h3>Cambiar estado de asistencia</h3>
            <button class="modal-close-btn">
              <span class="material-icons">close</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="student-profile">
              <div class="student-avatar large">
                <img src="${student.avatar}" alt="${student.name}">
              </div>
              <div class="student-details">
                <h4>${student.name}</h4>
              </div>
            </div>
            
            <div class="form-group">
              <label>Estado actual:</label>
              <div class="status-badge ${currentStatus || "none"}">${getStatusText(currentStatus)}</div>
            </div>
            
            <div class="form-group">
              <label>Nuevo estado:</label>
              <div class="status-options">
                <div class="status-option present ${currentStatus === "present" ? "active" : ""}" data-status="present" onclick="selectNewStatus(this)">Presente</div>
                <div class="status-option absent ${currentStatus === "absent" ? "active" : ""}" data-status="absent" onclick="selectNewStatus(this)">Ausente</div>
                <div class="status-option late ${currentStatus === "late" ? "active" : ""}" data-status="late" onclick="selectNewStatus(this)">Tardanza</div>
                <div class="status-option excused ${currentStatus === "excused" ? "active" : ""}" data-status="excused" onclick="selectNewStatus(this)">Justificado</div>
              </div>
            </div>
            
            <div class="form-actions">
              <button class="btn-secondary modal-close-btn">Cancelar</button>
              <button class="btn-primary" onclick="saveStatusChange('${dateString}', ${studentId})">Guardar cambio</button>
            </div>
          </div>
        `
  
        // Crear el modal si no existe
        if (!changeStatusModal) {
          const modal = document.createElement("div")
          modal.id = "change-status-modal"
          modal.className = "modal-overlay"
          modal.innerHTML = `<div class="modal-content">${modalContent}</div>`
          document.body.appendChild(modal)

          // Agregar event listeners para cerrar el modal
          modal.querySelectorAll(".modal-close-btn").forEach(btn => {
            btn.addEventListener("click", () => {
              modal.classList.remove("active")
            })
          })
        } else {
          changeStatusModal.querySelector(".modal-content").innerHTML = modalContent

          // Agregar event listeners para cerrar el modal
          changeStatusModal.querySelectorAll(".modal-close-btn").forEach(btn => {
            btn.addEventListener("click", () => {
              changeStatusModal.classList.remove("active")
            })
          })
        }
  
        // Mostrar el modal
        document.getElementById("change-status-modal").classList.add("active")
      }
  
      // Función para obtener el texto del estado
      function getStatusText(status) {
        if (status === "present") return "Presente"
        if (status === "absent") return "Ausente"
        if (status === "late") return "Tardanza"
        if (status === "excused") return "Justificado"
        return "No registrado"
      }
  
      // Definir la función selectNewStatus en el ámbito global
      window.selectNewStatus = (element) => {
        // Quitar la clase active de todas las opciones
        document.querySelectorAll("#change-status-modal .status-option").forEach((opt) => {
          opt.classList.remove("active")
        })

        // Agregar la clase active a la opción seleccionada
        element.classList.add("active")
      }
  
      // Definir la función saveStatusChange en el ámbito global
      window.saveStatusChange = (dateString, studentId) => {
        // Obtener el nuevo estado
        const newStatus = document.querySelector("#change-status-modal .status-option.active").dataset.status

        // Obtener el nombre del estudiante para el mensaje
        const student = students.find((s) => s.id == studentId)
        const statusText = getStatusText(newStatus)

        // Actualizar el estado en los datos
        if (!attendanceData[dateString]) {
          attendanceData[dateString] = {}
        }
        attendanceData[dateString][studentId] = newStatus

        // Cerrar el modal de cambio de estado
        document.getElementById("change-status-modal").classList.remove("active")

        // Actualizar el modal de detalle de asistencia
        showHistoryDetailModal(dateString)

        // Actualizar el calendario
        loadAttendanceCalendar()

        // Si estamos viendo la fecha actual, actualizar también la vista principal
        if (dateString === formatDateForData(currentDate)) {
          loadStudentList()
          updateAttendanceCounts()
        }

        // Mostrar mensaje de éxito
        const successMessage = document.createElement("div")
        successMessage.className = "success-message"
        successMessage.innerHTML = `
          <span class="material-icons">check_circle</span>
          <div class="success-details">
            <strong>Cambio guardado correctamente</strong>
            <p>${student.name}: ${statusText}</p>
          </div>
        `

        // Agregar el mensaje al modal
        const modalContent = document.querySelector("#history-detail-modal .modal-content")
        modalContent.insertBefore(successMessage, modalContent.firstChild)

        // Eliminar el mensaje después de 3 segundos
        setTimeout(() => {
          successMessage.remove()
        }, 3000)
      }
    }
  
    function initDatePicker() {
      // Crear el calendario para el selector de fecha
      updateDatePicker()
    }
  
    function updateDatePicker() {
      datePicker.innerHTML = ""
  
      // Crear encabezado del calendario
      const calendarHeader = document.createElement("div")
      calendarHeader.className = "calendar-header"
  
      const calendarTitle = document.createElement("div")
      calendarTitle.className = "calendar-title"
      calendarTitle.textContent = currentDate.toLocaleDateString("es-ES", { month: "long", year: "numeric" })
  
      const calendarNav = document.createElement("div")
      calendarNav.className = "calendar-nav"
  
      const prevMonthBtn = document.createElement("button")
      prevMonthBtn.className = "calendar-nav-btn"
      prevMonthBtn.innerHTML = '<span class="material-icons">chevron_left</span>'
      prevMonthBtn.addEventListener("click", () => {
        currentDate.setMonth(currentDate.getMonth() - 1)
        updateDatePicker()
      })
  
      const nextMonthBtn = document.createElement("button")
      nextMonthBtn.className = "calendar-nav-btn"
      nextMonthBtn.innerHTML = '<span class="material-icons">chevron_right</span>'
      nextMonthBtn.addEventListener("click", () => {
        currentDate.setMonth(currentDate.getMonth() + 1)
        updateDatePicker()
      })
  
      calendarNav.appendChild(prevMonthBtn)
      calendarNav.appendChild(nextMonthBtn)
  
      calendarHeader.appendChild(calendarTitle)
      calendarHeader.appendChild(calendarNav)
  
      datePicker.appendChild(calendarHeader)
  
      // Crear encabezados de días de la semana
      const weekdays = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]
      weekdays.forEach((day) => {
        const dayHeader = document.createElement("div")
        dayHeader.className = "calendar-weekday"
        dayHeader.textContent = day
        datePicker.appendChild(dayHeader)
      })
  
      // Obtener el mes actual
      const year = currentDate.getFullYear()
      const month = currentDate.getMonth()
  
      // Crear días del mes
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      const firstDay = new Date(year, month, 1).getDay()
  
      // Agregar espacios en blanco para los días anteriores al primer día del mes
      for (let i = 0; i < firstDay; i++) {
        const emptyDay = document.createElement("div")
        emptyDay.className = "calendar-date disabled"
        datePicker.appendChild(emptyDay)
      }
  
      // Agregar los días del mes
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day)
        const dateString = formatDateForData(date)
        const isClassDay = classDates.includes(dateString)
        const isToday = dateString === formatDateForData(new Date())
        const isSelected = dateString === formatDateForData(currentDate)
  
        const calendarDate = document.createElement("div")
        calendarDate.className = `calendar-date ${isClassDay ? "has-class" : ""} ${isToday ? "today" : ""} ${isSelected ? "selected" : ""}`
        calendarDate.textContent = day
        calendarDate.dataset.date = dateString
  
        calendarDate.addEventListener("click", () => {
          // Quitar la clase selected de todas las fechas
          document.querySelectorAll(".calendar-date.selected").forEach((el) => {
            el.classList.remove("selected")
          })
  
          // Agregar la clase selected a la fecha seleccionada
          calendarDate.classList.add("selected")
  
          // Actualizar la fecha seleccionada
          currentDate = new Date(dateString)
        })
  
        datePicker.appendChild(calendarDate)
      }
    }
  
    function setupEventListeners() {
      // Navegación de fechas
      prevDateBtn.addEventListener("click", () => {
        // Encontrar la fecha de clase anterior
        const currentDateString = formatDateForData(currentDate)
        const currentIndex = classDates.indexOf(currentDateString)
  
        if (currentIndex > 0) {
          currentDate = new Date(classDates[currentIndex - 1])
          updateDateDisplay()
          loadStudentList()
          updateAttendanceCounts()
          loadAttendanceCalendar()
        }
      })
  
      nextDateBtn.addEventListener("click", () => {
        // Encontrar la fecha de clase siguiente
        const currentDateString = formatDateForData(currentDate)
        const currentIndex = classDates.indexOf(currentDateString)
  
        if (currentIndex < classDates.length - 1) {
          currentDate = new Date(classDates[currentIndex + 1])
          updateDateDisplay()
          loadStudentList()
          updateAttendanceCounts()
          loadAttendanceCalendar()
        }
      })
  
      // Abrir modal de calendario
      calendarBtn.addEventListener("click", () => {
        calendarModal.classList.add("active")
        updateDatePicker()
      })
  
      // Cerrar modales
      document.querySelectorAll(".modal-close-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          document.querySelectorAll(".modal-overlay").forEach((modal) => {
            modal.classList.remove("active")
          })
        })
      })
  
      // Seleccionar fecha en el modal
      selectDateBtn.addEventListener("click", () => {
        updateDateDisplay()
        loadStudentList()
        updateAttendanceCounts()
        loadAttendanceCalendar()
        calendarModal.classList.remove("active")
      })
  
      // Cambiar filtros de historial
      monthFilter.addEventListener("change", loadAttendanceCalendar)
      studentFilter.addEventListener("change", loadAttendanceCalendar)
  
      // Guardar asistencia
      saveAttendanceBtn.addEventListener("click", () => {
        // Obtener información de la fecha y asistencia
        const dateString = formatDateForData(currentDate)
        const formattedDate = currentDate.toLocaleDateString("es-ES", {
          weekday: "long",
          day: "numeric",
          month: "long"
        })
        const dayAttendance = attendanceData[dateString] || {}
        const totalRegistered = Object.keys(dayAttendance).length
        const totalStudents = students.length

        // Crear mensaje de éxito
        const successMessage = document.createElement("div")
        successMessage.style.cssText = `
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 15px;
          margin: 20px 0;
          background-color: rgba(76, 175, 80, 0.1);
          color: #4caf50;
          border: 1px solid #4caf50;
          border-radius: 8px;
          font-family: 'Poppins', sans-serif;
        `

        successMessage.innerHTML = `
          <span class="material-icons">check_circle</span>
          <div>
            <strong>Asistencia guardada correctamente</strong><br>
            <small>${formattedDate} - ${totalRegistered} de ${totalStudents} estudiantes registrados</small>
          </div>
        `

        // Insertar después de la sección de asistencias
        const dashboardSection = document.querySelector(".dashboard-section")
        dashboardSection.appendChild(successMessage)

        // Eliminar después de 4 segundos
        setTimeout(() => {
          successMessage.remove()
        }, 4000)
      })
  
      // Restablecer asistencia
      resetAttendanceBtn.addEventListener("click", () => {
        // Limpiar todos los datos de asistencia para la fecha actual
        const dateString = formatDateForData(currentDate)
        delete attendanceData[dateString]

        // Recargar la lista de estudiantes (sin selecciones)
        loadStudentList()

        // Actualizar contadores (todos en 0)
        updateAttendanceCounts()

        // Actualizar calendario
        loadAttendanceCalendar()

        // Crear mensaje de restablecimiento
        const resetMessage = document.createElement("div")
        resetMessage.style.cssText = `
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 15px;
          margin: 20px 0;
          background-color: rgba(255, 152, 0, 0.1);
          color: #ff9800;
          border: 1px solid #ff9800;
          border-radius: 8px;
          font-family: 'Poppins', sans-serif;
        `

        resetMessage.innerHTML = `
          <span class="material-icons">refresh</span>
          <div>
            <strong>Asistencia restablecida</strong><br>
            <small>Todas las selecciones han sido eliminadas</small>
          </div>
        `

        // Insertar después de la sección de asistencias
        const dashboardSection = document.querySelector(".dashboard-section")
        dashboardSection.appendChild(resetMessage)

        // Eliminar después de 3 segundos
        setTimeout(() => {
          resetMessage.remove()
        }, 3000)
      })
    }
  })
  
  