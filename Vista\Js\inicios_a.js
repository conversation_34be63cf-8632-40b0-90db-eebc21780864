document.addEventListener("DOMContentLoaded", () => {
    // Actualizar fecha actual
    updateCurrentDate()
  
    // Configurar event listeners
    setupEventListeners()
  
    // Inicializar tooltips
    initTooltips()
  })
  
  // Función para actualizar la fecha actual
  function updateCurrentDate() {
    const currentDateElement = document.querySelector(".current-date")
    if (currentDateElement) {
      const now = new Date()
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      currentDateElement.textContent = now.toLocaleDateString("es-ES", options)
    }
  }
  
  // Configurar event listeners
  function setupEventListeners() {
    // Event listeners para botones de acción
    const actionButtons = document.querySelectorAll(".action-btn")
    actionButtons.forEach((btn) => {
      btn.addEventListener("click", handleActionButtonClick)
    })
  
    // Event listeners para botones "Ver todo"
    const viewAllButtons = document.querySelectorAll(".view-all-btn")
    viewAllButtons.forEach((btn) => {
      btn.addEventListener("click", handleViewAllClick)
    })
  
    // Event listener para el botón de notificaciones
    const notificationBtn = document.querySelector(".notification-btn")
    if (notificationBtn) {
      notificationBtn.addEventListener("click", toggleNotificationsPanel)
    }
  }
  
  // Manejar clics en botones de acción
  function handleActionButtonClick(e) {
    const button = e.currentTarget
    const action = getButtonAction(button)
    const itemType = getItemType(button)
    const itemId = getItemId(button)
  
    console.log(`Acción: ${action}, Tipo: ${itemType}, ID: ${itemId}`)
  
    // Implementar lógica según el tipo de acción
    switch (action) {
      case "view":
        viewItem(itemType, itemId)
        break
      case "edit":
        editItem(itemType, itemId)
        break
      case "approve":
        approveItem(itemType, itemId)
        break
      case "reject":
        rejectItem(itemType, itemId)
        break
      case "delete":
        deleteItem(itemType, itemId)
        break
      case "publish":
        publishItem(itemType, itemId)
        break
    }
  }
  
  // Obtener el tipo de acción del botón
  function getButtonAction(button) {
    if (button.classList.contains("view-btn")) return "view"
    if (button.classList.contains("edit-btn")) return "edit"
    if (button.classList.contains("approve-btn")) return "approve"
    if (button.classList.contains("reject-btn")) return "reject"
    if (button.classList.contains("delete-btn")) return "delete"
    if (button.classList.contains("publish-btn")) return "publish"
    return "unknown"
  }
  
  // Obtener el tipo de elemento
  function getItemType(button) {
    const parentItem = button.closest(".admission-item, .announcement-item, .user-item")
  
    if (parentItem.classList.contains("admission-item")) return "admission"
    if (parentItem.classList.contains("announcement-item")) return "announcement"
    if (parentItem.classList.contains("user-item")) return "user"
  
    return "unknown"
  }
  
  // Obtener el ID del elemento (simulado)
  function getItemId(button) {
    // En una implementación real, esto podría venir de un atributo data-id
    return Math.floor(Math.random() * 1000)
  }
  
  // Funciones para manejar acciones (simuladas)
  function viewItem(type, id) {
    alert(`Ver ${type} con ID: ${id}`)
  }
  
  function editItem(type, id) {
    alert(`Editar ${type} con ID: ${id}`)
  }
  
  function approveItem(type, id) {
    if (type === "admission") {
      if (confirm(`¿Está seguro que desea aprobar la solicitud de admisión #${id}?`)) {
        // Aquí iría la lógica para aprobar la solicitud
        const item = document.querySelector(`.admission-item:nth-child(${(id % 3) + 1})`)
        if (item) {
          const statusElement = item.querySelector(".admission-status")
          statusElement.textContent = "Aprobada"
          statusElement.classList.remove("pending", "in-review")
          statusElement.classList.add("approved")
  
          // Actualizar contador de solicitudes pendientes
          updatePendingAdmissionsCount()
        }
      }
    }
  }
  
  function rejectItem(type, id) {
    if (type === "admission") {
      if (confirm(`¿Está seguro que desea rechazar la solicitud de admisión #${id}?`)) {
        // Aquí iría la lógica para rechazar la solicitud
        const item = document.querySelector(`.admission-item:nth-child(${(id % 3) + 1})`)
        if (item) {
          const statusElement = item.querySelector(".admission-status")
          statusElement.textContent = "Rechazada"
          statusElement.classList.remove("pending", "in-review")
          statusElement.classList.add("rejected")
  
          // Actualizar contador de solicitudes pendientes
          updatePendingAdmissionsCount()
        }
      }
    }
  }
  
  function deleteItem(type, id) {
    if (confirm(`¿Está seguro que desea eliminar este ${type} con ID: ${id}?`)) {
      // Aquí iría la lógica para eliminar el elemento
      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} eliminado correctamente.`)
    }
  }
  
  function publishItem(type, id) {
    if (type === "announcement") {
      if (confirm(`¿Está seguro que desea publicar este anuncio con ID: ${id}?`)) {
        // Aquí iría la lógica para publicar el anuncio
        alert("Anuncio publicado correctamente.")
      }
    }
  }
  
  // Manejar clics en botones "Ver todo"
  function handleViewAllClick(e) {
    const button = e.currentTarget
    const section = button.closest(".dashboard-section")
    const sectionType = getSectionType(section)
  
    // Redirigir a la página correspondiente
    switch (sectionType) {
      case "activity":
        window.location.href = "actividad_a.html"
        break
      case "admissions":
        window.location.href = "solicitudes_a.html"
        break
      case "announcements":
        window.location.href = "anuncios_a.html"
        break
      case "users":
        window.location.href = "usuarios_a.html"
        break
    }
  }
  
  // Obtener el tipo de sección
  function getSectionType(section) {
    if (section.classList.contains("recent-activity")) return "activity"
    if (section.classList.contains("recent-admissions")) return "admissions"
    if (section.classList.contains("pending-announcements")) return "announcements"
    if (section.classList.contains("recent-users")) return "users"
    return "unknown"
  }
  
  // Actualizar contador de solicitudes pendientes
  function updatePendingAdmissionsCount() {
    const pendingAdmissions = document.querySelectorAll(".admission-status.pending").length
    const admissionsCountElement = document.querySelector(".stat-card .stat-details h3")
  
    if (admissionsCountElement) {
      admissionsCountElement.textContent = pendingAdmissions
    }
  }
  
  // Mostrar/ocultar panel de notificaciones (simulado)
  function toggleNotificationsPanel() {
    alert("Panel de notificaciones (funcionalidad pendiente)")
  }
  
  // Inicializar tooltips (simulado)
  function initTooltips() {
    // En una implementación real, aquí se inicializarían los tooltips
    console.log("Tooltips inicializados")
  }
  
  