document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn")
    const changePasswordBtn = document.querySelector(".change-password-btn")
    const changeAvatarBtn = document.querySelector(".change-avatar-btn")
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn")
    const modals = document.querySelectorAll(".modal-overlay")
    const fotoInput = document.getElementById("foto-input")
    
    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const section = button.getAttribute("data-section")
        const modal = document.getElementById(`edit-${section}-modal`)
        if (modal) {
          modal.classList.add("active")
        }
      })
    })
    
    // Mostrar modal de cambio de contraseña
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener("click", () => {
        const modal = document.getElementById("change-password-modal")
        if (modal) {
          modal.classList.add("active")
        }
      })
    }
    
    // Manejar cambio de avatar
    if (changeAvatarBtn) {
      changeAvatarBtn.addEventListener("click", () => {
        fotoInput.click()
      })
    }

    // Manejar selección de archivo
    if (fotoInput) {
      fotoInput.addEventListener("change", (e) => {
          if (e.target.files && e.target.files[0]) {
          subirFotoPerfil(e.target.files[0])
          }
      })
    }
    
    // Cerrar modales con botones de cierre
    modalCloseButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Manejar envío de formularios
    const forms = document.querySelectorAll(".edit-form")
    forms.forEach((form) => {
      form.addEventListener("submit", (e) => {
        e.preventDefault()
        const formId = form.id
        
        switch (formId) {
          case 'personal-form':
            actualizarInformacionPersonal(form)
            break
          case 'account-form':
            actualizarInformacionCuenta(form)
            break
          case 'password-form':
            cambiarContraseña(form)
            break
        }
      })
    })
    
    // Función para subir foto de perfil
    async function subirFotoPerfil(archivo) {
      const formData = new FormData()
      formData.append('foto', archivo)

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=subir_foto', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Foto de perfil actualizada correctamente', 'success')
          // Recargar la página para mostrar la nueva foto
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al subir la foto', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al subir la foto', 'error')
      }
    }

    // Función para actualizar información personal
    async function actualizarInformacionPersonal(form) {
      const formData = new FormData(form)

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=actualizar', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Información personal actualizada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          // Recargar la página para mostrar los cambios
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al actualizar la información', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al actualizar la información', 'error')
      }
    }

    // Función para actualizar información de cuenta
    async function actualizarInformacionCuenta(form) {
      const formData = new FormData(form)
      
      // Validar que no se estén enviando datos profesionales
      const camposProfesionales = ['cargo', 'departamento', 'especialidad', 'nivel_educativo', 'grado_tutor', 'tipo_apoderado'];
      let tieneDatosProfesionales = false;
      
      camposProfesionales.forEach(campo => {
        if (formData.has(campo) && formData.get(campo)) {
          tieneDatosProfesionales = true;
        }
      });
      
      if (tieneDatosProfesionales) {
        mostrarNotificacion('La información profesional no puede ser editada desde el perfil', 'error');
        return;
      }

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=actualizar', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Información de cuenta actualizada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          // Recargar la página para mostrar los cambios
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          mostrarNotificacion(result.error || 'Error al actualizar la información', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al actualizar la información', 'error')
      }
    }

    // Función para cambiar contraseña
    async function cambiarContraseña(form) {
      const formData = new FormData(form)
      const passwordNuevo = formData.get('password_nuevo')
      const passwordConfirmar = formData.get('password_confirmar')

      // Validar que las contraseñas coincidan
      if (passwordNuevo !== passwordConfirmar) {
        mostrarNotificacion('Las contraseñas nuevas no coinciden', 'error')
        return
      }

      // Validar longitud mínima
      if (passwordNuevo.length < 6) {
        mostrarNotificacion('La contraseña debe tener al menos 6 caracteres', 'error')
        return
      }

      try {
        const response = await fetch('../Controlador/PerfilController.php?action=cambiar_password', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.success) {
          mostrarNotificacion('Contraseña cambiada correctamente', 'success')
          cerrarModal(form.closest('.modal-overlay'))
          form.reset()
        } else {
          mostrarNotificacion(result.error || 'Error al cambiar la contraseña', 'error')
        }
      } catch (error) {
        console.error('Error:', error)
        mostrarNotificacion('Error al cambiar la contraseña', 'error')
      }
    }

    // Función para cerrar modal
    function cerrarModal(modal) {
      if (modal) {
        modal.classList.remove("active")
      }
    }

    // Función para mostrar notificaciones
    function mostrarNotificacion(mensaje, tipo) {
      // Crear elemento de notificación
      const notificacion = document.createElement('div')
      notificacion.className = `notificacion ${tipo}`
      notificacion.textContent = mensaje
      
      // Estilos básicos
      notificacion.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
      `
      
      // Color según tipo
      if (tipo === 'success') {
        notificacion.style.backgroundColor = '#4caf50'
      } else if (tipo === 'error') {
        notificacion.style.backgroundColor = '#f44336'
      } else {
        notificacion.style.backgroundColor = '#2196f3'
      }
      
      // Agregar al DOM
      document.body.appendChild(notificacion)
      
      // Mostrar con animación
      setTimeout(() => {
        notificacion.style.transform = 'translateX(0)'
      }, 100)
      
      // Ocultar después de 3 segundos
      setTimeout(() => {
        notificacion.style.transform = 'translateX(100%)'
        setTimeout(() => {
          if (notificacion.parentNode) {
            notificacion.parentNode.removeChild(notificacion)
          }
        }, 300)
      }, 3000)
    }
})
    