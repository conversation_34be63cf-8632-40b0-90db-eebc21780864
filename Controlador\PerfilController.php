<?php
require_once __DIR__ . '/../Modelo/Usuario.php';
require_once __DIR__ . '/../Modelo/Administrador.php';
require_once __DIR__ . '/../Modelo/Padre.php';

/**
 * Controlador para manejar las operaciones de perfil de usuarios
 */
class PerfilController {
    private $usuario;
    private $administrador;
    private $padre;

    public function __construct() {
        $this->usuario = new Usuario();
        $this->administrador = new Administrador();
        $this->padre = new Padre();
    }

    /**
     * Obtiene los datos completos del perfil del usuario actual
     * @return array
     */
    public function obtenerPerfilCompleto() {
        // Verificar si la sesión está iniciada
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['usuario_id'])) {
            return ['error' => 'Usuario no autenticado'];
        }

        $usuarioId = $_SESSION['usuario_id'];
        $rol = $_SESSION['rol'];

        try {
            // Obtener datos básicos del usuario
            $sql = "SELECT u.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.direccion, p.telefono, p.nombre_foto 
                    FROM usuarios u 
                    INNER JOIN personas p ON u.id = p.usuario_id 
                    WHERE u.id = :usuario_id";
            
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
            
            $datosBasicos = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$datosBasicos) {
                return ['error' => 'Usuario no encontrado'];
            }

            // Obtener información específica según el rol
            $informacionRol = [];
            switch ($rol) {
                case 'administrador':
                    $informacionRol = $this->obtenerDatosAdministrador($usuarioId);
                    break;
                case 'padre':
                    $informacionRol = $this->obtenerDatosPadre($usuarioId);
                    break;
                case 'maestro':
                    $informacionRol = $this->obtenerDatosMaestro($usuarioId);
                    break;
                case 'estudiante':
                    $informacionRol = $this->obtenerDatosEstudiante($usuarioId);
                    break;
            }

            // Calcular edad
            $edad = null;
            if ($datosBasicos['fecha_nacimiento']) {
                $fechaNac = new DateTime($datosBasicos['fecha_nacimiento']);
                $hoy = new DateTime();
                $edad = $hoy->diff($fechaNac)->y;
            }

            return [
                'success' => true,
                'datos_basicos' => $datosBasicos,
                'informacion_rol' => $informacionRol,
                'edad' => $edad,
                'rol' => $rol
            ];

        } catch (PDOException $e) {
            error_log("Error obteniendo perfil: " . $e->getMessage());
            return ['error' => 'Error al obtener datos del perfil'];
        }
    }

    /**
     * Actualiza los datos del perfil
     * @return array
     */
    public function actualizarPerfil() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        if (!isset($_SESSION['usuario_id'])) {
            return ['error' => 'Usuario no autenticado'];
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return ['error' => 'Método no permitido'];
        }
        $usuarioId = $_SESSION['usuario_id'];
        $rol = $_SESSION['rol'];
        try {
            $this->usuario->pdo->beginTransaction();

            // Actualizar datos básicos de persona SOLO si llegan y no están vacíos
            $camposPersona = ['nombres','apellido_paterno','apellido_materno','dni','fecha_nacimiento','sexo','direccion','telefono'];
            $setPersona = [];
            $paramsPersona = [];
            foreach ($camposPersona as $campo) {
                if (isset($_POST[$campo]) && $_POST[$campo] !== '') {
                    $setPersona[] = "$campo = :$campo";
                    $paramsPersona[":$campo"] = $_POST[$campo];
                }
            }
            if (!empty($setPersona)) {
                $sqlPersona = "UPDATE personas SET ".implode(", ", $setPersona).", updated_at = CURRENT_TIMESTAMP WHERE usuario_id = :usuario_id";
                $paramsPersona[':usuario_id'] = $usuarioId;
                $stmtPersona = $this->usuario->pdo->prepare($sqlPersona);
                $stmtPersona->execute($paramsPersona);
            }

            // NO permitir actualizar datos profesionales
            // switch ($rol) { ... }

            // Actualizar email SOLO si llega y no está vacío
            if (isset($_POST['email']) && $_POST['email'] !== '') {
                $sqlCheckEmail = "SELECT id FROM usuarios WHERE email = :email AND id != :usuario_id";
                $stmtCheckEmail = $this->usuario->pdo->prepare($sqlCheckEmail);
                $stmtCheckEmail->bindParam(':email', $_POST['email']);
                $stmtCheckEmail->bindParam(':usuario_id', $usuarioId);
                $stmtCheckEmail->execute();
                if ($stmtCheckEmail->fetch()) {
                    throw new Exception('El correo electrónico ya está en uso por otro usuario');
                }
                $sqlUsuario = "UPDATE usuarios SET email = :email, updated_at = CURRENT_TIMESTAMP WHERE id = :usuario_id";
                $stmtUsuario = $this->usuario->pdo->prepare($sqlUsuario);
                $stmtUsuario->bindParam(':email', $_POST['email']);
                $stmtUsuario->bindParam(':usuario_id', $usuarioId);
                $stmtUsuario->execute();
                $_SESSION['email'] = $_POST['email'];
            }
            // Actualizar nombre_usuario SOLO si llega y no está vacío
            if (isset($_POST['nombre_usuario']) && $_POST['nombre_usuario'] !== '') {
                $sqlCheckUsername = "SELECT id FROM usuarios WHERE nombre_usuario = :nombre_usuario AND id != :usuario_id";
                $stmtCheckUsername = $this->usuario->pdo->prepare($sqlCheckUsername);
                $stmtCheckUsername->bindParam(':nombre_usuario', $_POST['nombre_usuario']);
                $stmtCheckUsername->bindParam(':usuario_id', $usuarioId);
                $stmtCheckUsername->execute();
                if ($stmtCheckUsername->fetch()) {
                    throw new Exception('El nombre de usuario ya está en uso por otro usuario');
                }
                $sqlUsuario = "UPDATE usuarios SET nombre_usuario = :nombre_usuario, updated_at = CURRENT_TIMESTAMP WHERE id = :usuario_id";
                $stmtUsuario = $this->usuario->pdo->prepare($sqlUsuario);
                $stmtUsuario->bindParam(':nombre_usuario', $_POST['nombre_usuario']);
                $stmtUsuario->bindParam(':usuario_id', $usuarioId);
                $stmtUsuario->execute();
                $_SESSION['nombre_usuario'] = $_POST['nombre_usuario'];
            }
            $this->usuario->pdo->commit();
            // Actualizar datos en sesión solo si se actualizaron
            foreach ($camposPersona as $campo) {
                if (isset($_POST[$campo]) && $_POST[$campo] !== '') {
                    $_SESSION[$campo] = $_POST[$campo];
                }
            }
            return ['success' => true, 'mensaje' => 'Perfil actualizado correctamente'];
        } catch (Exception $e) {
            $this->usuario->pdo->rollBack();
            error_log("Error actualizando perfil: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        } catch (PDOException $e) {
            $this->usuario->pdo->rollBack();
            error_log("Error actualizando perfil: " . $e->getMessage());
            return ['error' => 'Error al actualizar el perfil'];
        }
    }

    /**
     * Cambia la contraseña del usuario
     * @return array
     */
    public function cambiarContraseña() {
        // Verificar si la sesión está iniciada
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['usuario_id'])) {
            return ['error' => 'Usuario no autenticado'];
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return ['error' => 'Método no permitido'];
        }

        $usuarioId = $_SESSION['usuario_id'];
        $passwordActual = $_POST['password_actual'];
        $passwordNuevo = $_POST['password_nuevo'];
        $passwordConfirmar = $_POST['password_confirmar'];

        // Validaciones
        if (empty($passwordNuevo)) {
            return ['error' => 'La nueva contraseña no puede estar vacía'];
        }
        if ($passwordNuevo !== $passwordConfirmar) {
            return ['error' => 'Las contraseñas nuevas no coinciden'];
        }
        if (strlen($passwordNuevo) < 6) {
            return ['error' => 'La contraseña debe tener al menos 6 caracteres'];
        }

        try {
            // Verificar contraseña actual
            $sql = "SELECT password FROM usuarios WHERE id = :usuario_id";
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
            $usuario = $stmt->fetch();
            if (!password_verify($passwordActual, $usuario['password'])) {
                return ['error' => 'La contraseña actual es incorrecta'];
            }
            // Actualizar contraseña
            $passwordHash = password_hash($passwordNuevo, PASSWORD_DEFAULT);
            if (!$passwordHash) {
                return ['error' => 'Error al generar el hash de la contraseña'];
            }
            $sqlUpdate = "UPDATE usuarios SET password = :password, updated_at = CURRENT_TIMESTAMP WHERE id = :usuario_id";
            $stmtUpdate = $this->usuario->pdo->prepare($sqlUpdate);
            $stmtUpdate->bindParam(':password', $passwordHash);
            $stmtUpdate->bindParam(':usuario_id', $usuarioId);
            $stmtUpdate->execute();
            return ['success' => true, 'mensaje' => 'Contraseña cambiada correctamente'];
        } catch (PDOException $e) {
            error_log("Error cambiando contraseña: " . $e->getMessage());
            return ['error' => 'Error al cambiar la contraseña'];
        }
    }

    /**
     * Sube una nueva foto de perfil
     * @return array
     */
    public function subirFotoPerfil() {
        // Verificar si la sesión está iniciada
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['usuario_id'])) {
            return ['error' => 'Usuario no autenticado'];
        }

        if (!isset($_FILES['foto']) || $_FILES['foto']['error'] !== UPLOAD_ERR_OK) {
            return ['error' => 'Error al subir la imagen'];
        }

        $usuarioId = $_SESSION['usuario_id'];
        $archivo = $_FILES['foto'];

        // Validar tipo de archivo
        $tiposPermitidos = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($archivo['type'], $tiposPermitidos)) {
            return ['error' => 'Tipo de archivo no permitido. Solo se permiten JPG, PNG y GIF'];
        }

        // Validar tamaño (máximo 5MB)
        if ($archivo['size'] > 5 * 1024 * 1024) {
            return ['error' => 'El archivo es demasiado grande. Máximo 5MB'];
        }

        try {
            // Leer el contenido del archivo
            $contenidoImagen = file_get_contents($archivo['tmp_name']);
            $tipoMime = $archivo['type'];
            $nombreArchivo = $archivo['name'];

            // Actualizar en base de datos como BLOB
            $sql = "UPDATE personas SET 
                    foto_perfil = :foto_perfil, 
                    tipo_foto = :tipo_foto,
                    nombre_foto = :nombre_foto,
                    updated_at = CURRENT_TIMESTAMP 
                    WHERE usuario_id = :usuario_id";
            
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':foto_perfil', $contenidoImagen, PDO::PARAM_LOB);
            $stmt->bindParam(':tipo_foto', $tipoMime);
            $stmt->bindParam(':nombre_foto', $nombreArchivo);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();

            // Actualizar sesión con el nombre del archivo
            $_SESSION['foto_perfil'] = $nombreArchivo;

            return [
                'success' => true, 
                'mensaje' => 'Foto de perfil actualizada correctamente',
                'foto_perfil' => $nombreArchivo
            ];

        } catch (PDOException $e) {
            error_log("Error subiendo foto: " . $e->getMessage());
            return ['error' => 'Error al actualizar la foto de perfil'];
        }
    }

    // Métodos privados para obtener datos específicos por rol
    private function obtenerDatosAdministrador($usuarioId) {
        $sql = "SELECT a.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM administradores a
                INNER JOIN personas p ON a.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->usuario->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function obtenerDatosPadre($usuarioId) {
        $sql = "SELECT pa.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM padres pa
                INNER JOIN personas p ON pa.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->usuario->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function obtenerDatosMaestro($usuarioId) {
        $sql = "SELECT m.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM maestros m
                INNER JOIN personas p ON m.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->usuario->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function obtenerDatosEstudiante($usuarioId) {
        $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM estudiantes e
                INNER JOIN personas p ON e.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->usuario->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function actualizarDatosAdministrador($usuarioId) {
        if (isset($_POST['cargo']) || isset($_POST['departamento'])) {
            $sql = "UPDATE administradores a 
                    INNER JOIN personas p ON a.persona_id = p.id 
                    SET a.cargo = :cargo, a.departamento = :departamento, a.updated_at = CURRENT_TIMESTAMP 
                    WHERE p.usuario_id = :usuario_id";
            
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':cargo', $_POST['cargo'] ?? '');
            $stmt->bindParam(':departamento', $_POST['departamento'] ?? '');
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
        }
    }

    private function actualizarDatosPadre($usuarioId) {
        if (isset($_POST['tipo_apoderado'])) {
            $sql = "UPDATE padres pa 
                    INNER JOIN personas p ON pa.persona_id = p.id 
                    SET pa.tipo_apoderado = :tipo_apoderado, pa.updated_at = CURRENT_TIMESTAMP 
                    WHERE p.usuario_id = :usuario_id";
            
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':tipo_apoderado', $_POST['tipo_apoderado']);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
        }
    }

    private function actualizarDatosMaestro($usuarioId) {
        if (isset($_POST['especialidad']) || isset($_POST['nivel_educativo']) || isset($_POST['grado_tutor'])) {
            $sql = "UPDATE maestros m 
                    INNER JOIN personas p ON m.persona_id = p.id 
                    SET m.especialidad = :especialidad, 
                        m.nivel_educativo = :nivel_educativo, 
                        m.grado_tutor = :grado_tutor, 
                        m.updated_at = CURRENT_TIMESTAMP 
                    WHERE p.usuario_id = :usuario_id";
            
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':especialidad', $_POST['especialidad'] ?? '');
            $stmt->bindParam(':nivel_educativo', $_POST['nivel_educativo'] ?? '');
            $stmt->bindParam(':grado_tutor', $_POST['grado_tutor'] ?? '');
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
        }
    }
}

// Procesar la acción solicitada
if (isset($_GET['action'])) {
    $perfil = new PerfilController();
    
    switch ($_GET['action']) {
        case 'obtener':
            header('Content-Type: application/json');
            echo json_encode($perfil->obtenerPerfilCompleto());
            break;
        case 'actualizar':
            header('Content-Type: application/json');
            echo json_encode($perfil->actualizarPerfil());
            break;
        case 'cambiar_password':
            header('Content-Type: application/json');
            echo json_encode($perfil->cambiarContraseña());
            break;
        case 'subir_foto':
            header('Content-Type: application/json');
            echo json_encode($perfil->subirFotoPerfil());
            break;
        default:
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Acción no válida']);
    }
}
?> 