<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Tareas</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/tareas.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Juan Pérez</h3>
                        <p>5° Primaria</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_e.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_e.html">
                                <span class="material-icons">book</span>
                                <span>Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="notas_e.html">
                                <span class="material-icons">grade</span>
                                <span>Notas</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="tareas_e.html">
                                <span class="material-icons">assignment</span>
                                <span>Tareas</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mis Tareas</h1>
                    <p class="current-date">Sábado, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Resumen de tareas -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Resumen de tareas</h2>
                    </div>
                    
                    <div class="task-summary-cards">
                        <div class="task-summary-card pending-card">
                            <div class="summary-icon">
                                <span class="material-icons">assignment_late</span>
                            </div>
                            <div class="summary-info">
                                <h3>Tareas pendientes</h3>
                                <p class="summary-count">3</p>
                                <p class="summary-text">Por entregar esta semana</p>
                            </div>
                        </div>
                        
                        <div class="task-summary-card completed-card">
                            <div class="summary-icon">
                                <span class="material-icons">assignment_turned_in</span>
                            </div>
                            <div class="summary-info">
                                <h3>Tareas completadas</h3>
                                <p class="summary-count">2</p>
                                <p class="summary-text">Entregadas este mes</p>
                            </div>
                        </div>
                        
                        <div class="task-summary-card late-card">
                            <div class="summary-icon">
                                <span class="material-icons">assignment_returned</span>
                            </div>
                            <div class="summary-info">
                                <h3>Tareas atrasadas</h3>
                                <p class="summary-count">1</p>
                                <p class="summary-text">Fecha límite vencida</p>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Filtro de tareas -->
                <section class="filter-section">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="Buscar tareas...">
                        </div>
                        <div class="filter-options">
                            <select>
                                <option value="all">Todas las tareas</option>
                                <option value="pending">Pendientes</option>
                                <option value="completed">Completadas</option>
                                <option value="graded">Calificadas</option>
                            </select>
                        </div>
                        <div class="filter-options">
                            <select>
                                <option value="all">Todos los cursos</option>
                                <option value="math">Matemáticas</option>
                                <option value="language">Comunicación</option>
                                <option value="science">Ciencia y Tecnología</option>
                                <option value="social">Personal Social</option>
                                <option value="english">Inglés</option>
                                <option value="art">Arte</option>
                                <option value="physical">Educación Física</option>
                            </select>
                        </div>
                    </div>
                </section>
                
                <!-- Tareas pendientes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas pendientes</h2>
                    </div>
                    
                    <div class="tasks-list">
                        <div class="task-item clickable-task" data-task="sumas-restas">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <h3>Sumas y restas</h3>
                                <p>Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                                <div class="task-meta">
                                    <span class="task-course">Matemáticas</span>
                                    <span class="task-date">Fecha límite: 23/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="sumas-restas">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="task-item clickable-task" data-task="analisis-texto">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <h3>Análisis de texto narrativo</h3>
                                <p>Lee el cuento "El zorro y el cóndor" y realiza un análisis de los personajes.</p>
                                <div class="task-meta">
                                    <span class="task-course">Comunicación</span>
                                    <span class="task-date">Fecha límite: 25/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="analisis-texto">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="task-item clickable-task" data-task="mapa-peru">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <h3>Mapa del Perú</h3>
                                <p>Dibuja el mapa del Perú y señala sus principales regiones naturales.</p>
                                <div class="task-meta">
                                    <span class="task-course">Ciencias Sociales</span>
                                    <span class="task-date">Fecha límite: 28/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="mapa-peru">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Tareas completadas -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas completadas</h2>
                    </div>
                    
                    <div class="tasks-list">
                        <div class="task-item clickable-task" data-task="vocabulario-ingles">
                            <div class="task-status completed"></div>
                            <div class="task-content">
                                <h3>Vocabulario en inglés</h3>
                                <p>Completa la lista de vocabulario sobre animales en inglés.</p>
                                <div class="task-meta">
                                    <span class="task-course">Inglés</span>
                                    <span class="task-date">Entregado: 18/03/2025</span>
                                    <span class="task-grade">Calificación: 18/20</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="vocabulario-ingles">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">comment</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="task-item clickable-task" data-task="experimento-plantas">
                            <div class="task-status completed"></div>
                            <div class="task-content">
                                <h3>Experimento con plantas</h3>
                                <p>Realiza el experimento de germinación de semillas y documenta el proceso.</p>
                                <div class="task-meta">
                                    <span class="task-course">Ciencias Naturales</span>
                                    <span class="task-date">Entregado: 15/03/2025</span>
                                    <span class="task-grade">Calificación: 19/20</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="experimento-plantas">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">comment</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Tareas atrasadas -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas atrasadas</h2>
                    </div>

                    <div class="tasks-list">
                        <div class="task-item clickable-task" data-task="multiplicacion-division">
                            <div class="task-status late"></div>
                            <div class="task-content">
                                <h3>Multiplicación y división</h3>
                                <p>Resuelve los ejercicios de multiplicación y división de la pág. 25.</p>
                                <div class="task-meta">
                                    <span class="task-course">Matemáticas</span>
                                    <span class="task-date">Fecha límite: 20/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="multiplicacion-division">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>

                        <div class="task-item clickable-task" data-task="proyecto-arte">
                            <div class="task-status late"></div>
                            <div class="task-content">
                                <h3>Proyecto de arte</h3>
                                <p>Crea una obra de arte utilizando materiales reciclados.</p>
                                <div class="task-meta">
                                    <span class="task-course">Arte</span>
                                    <span class="task-date">Fecha límite: 18/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="proyecto-arte">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Vista de tarea (oculta por defecto) -->
    <div id="task-view" class="modal-overlay">
        <div class="modal-content assignment-container">
            <div class="assignment-header">
                <h1 class="assignment-title">
                    <span class="material-icons">assignment</span>
                    Tarea: Sumas y restas
                </h1>
                <span class="grade-pending">Calificación: --</span>
            </div>
            
            <div class="assignment-content">
                <div class="assignment-section">
                    <h2 class="assignment-section-title">Descripción de la tarea</h2>
                    <p class="assignment-description">Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                    <img src="unnie.png" alt="Ejercicios de sumas y restas" class="assignment-image">
                </div>
                
                <div class="assignment-section">
                    <div class="assignment-meta">
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">event</span>
                                Fecha de entrega
                            </div>
                            <div class="meta-value">23/03/2025, 12:00 AM</div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">grade</span>
                                Puntos
                            </div>
                            <div class="meta-value">10 puntos máximos</div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">person</span>
                                Asignado por
                            </div>
                            <div class="meta-value">Prof. Carlos García</div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">book</span>
                                Curso
                            </div>
                            <div class="meta-value">Matemáticas</div>
                        </div>
                    </div>
                </div>
                
                <div class="assignment-section">
                    <div class="submission-section">
                        <h2 class="submission-title">Entrega de tarea</h2>
                        <div class="submission-status">
                            <span class="material-icons">schedule</span>
                            <span>Pendiente - Tienes hasta el 23/03/2025 para entregar esta tarea.</span>
                        </div>
                        <div class="submission-options">
                            <div class="file-upload-area" onclick="document.getElementById('task-file-input').click()">
                                <input type="file" id="task-file-input" accept=".pdf,.jpg,.jpeg,.png" style="display: none;" onchange="handleTaskFileSelect(this)">
                                <span class="material-icons file-upload-icon">cloud_upload</span>
                                <p class="file-upload-text">Arrastra y suelta archivos aquí o haz clic para seleccionar archivos</p>
                                <p class="file-upload-hint">Formatos aceptados: PDF, JPG, PNG. Tamaño máximo: 10MB</p>
                                <div id="task-selected-files" class="selected-files-list"></div>
                            </div>
                            
                            <div class="text-submission-area">
                                <label class="text-submission-label">Puedes añadir comentarios a tu entrega:</label>
                                <textarea class="text-submission-textarea" placeholder="Escribe tus comentarios aquí..."></textarea>
                            </div>
                            
                            <div class="submission-actions">
                                <button class="assignment-action-btn secondary-btn" id="close-task-btn">Cancelar</button>
                                <button class="assignment-action-btn primary-btn">Entregar tarea</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/tareas.js"></script>
</body>
</html>