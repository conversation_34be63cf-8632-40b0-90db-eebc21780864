<!DOCTYPE html>
<html lang="es">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Plataforma Educativa - Gestión de Usuarios</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
<link rel="stylesheet" href="./Css/plataforma.css">
<link rel="stylesheet" href="./Css/admin.css">
<link rel="stylesheet" href="./Css/usuarios_a.css">
</head>
<body>
<div class="plataforma-container">
  <!-- Menú lateral -->
  <aside class="sidebar">
    <div class="sidebar-header">
      <div class="logo">
        <img src="./img/logo-escuela.svg" alt="Logo Escuela">
      </div>
      <button class="menu-toggle" id="menu-toggle">
        <span class="material-icons">menu</span>
      </button>
    </div>
    
    <div class="sidebar-content">
      <div class="user-info">
        <div class="user-avatar">
          <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
        </div>
        <div class="user-details">
          <h3>Admin Sistema</h3>
          <p>Administrador</p>
        </div>
      </div>
      
      <nav class="sidebar-menu">
        <ul>
          <li>
            <a href="inicio_a.html">
              <span class="material-icons">dashboard</span>
              <span>Inicio</span>
            </a>
          </li>
          <li>
            <a href="perfil_a.html">
              <span class="material-icons">person</span>
              <span>Perfil</span>
            </a>
          </li>
          <li class="active">
            <a href="usuarios_a.html">
              <span class="material-icons">people</span>
              <span>Usuarios</span>
            </a>
          </li>
          <li>
            <a href="anuncios_admin.html">
              <span class="material-icons">campaign</span>
              <span>Anuncios</span>
            </a>
          </li>
          <li>
            <a href="admision_a.html">
              <span class="material-icons">how_to_reg</span>
              <span>Solicitudes de Admisión</span>
            </a>
          </li>
          <li>
            <a href="configuracion_admin.html">
              <span class="material-icons">settings</span>
              <span>Configuración</span>
            </a>
          </li>
          <li class="separator"></li>
          <li>
            <a href="intranet.html">
              <span class="material-icons">logout</span>
              <span>Cerrar Sesión</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </aside>
  
  <!-- Contenido principal -->
  <main class="main-content">
    <header class="content-header">
      <div class="header-left">
        <h1>Gestión de Usuarios</h1>
        <p class="current-date">Lunes, 22 de marzo de 2025</p>
      </div>
      <div class="header-right">
        <div class="notifications">
          <button class="notification-btn">
            <span class="material-icons">notifications</span>
            <span class="notification-badge">5</span>
          </button>
        </div>
      </div>
    </header>
    
    <div class="content-body">
      <!-- Barra de herramientas -->
      <div class="users-toolbar">
        <div class="toolbar-left">
          <div class="search-container">
            <input type="text" class="search-input" placeholder="Buscar usuarios...">
            <span class="material-icons search-icon">search</span>
          </div>
          <div class="filter-container">
            <select id="role-filter" class="filter-select">
              <option value="all">Todos los roles</option>
              <option value="student">Estudiantes</option>
              <option value="teacher">Maestros</option>
              <option value="parent">Padres</option>
              <option value="admin">Administradores</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="account-example-btn">
            <span class="material-icons">help_outline</span>
            Ver ejemplo
          </button>
          <button class="create-user-btn">
            <span class="material-icons">person_add</span>
            Crear Usuario
          </button>
          <button class="export-excel-btn">
            <span class="material-icons">table_chart</span>
            Exportar Excel
          </button>
        </div>
      </div>
      
      <!-- Tabla de usuarios -->
      <div class="users-table-container">
        <table class="users-table">
          <thead>
            <tr>
              <th class="user-col">Usuario</th>
              <th class="role-col">Rol</th>
              <th class="email-col">Correo Electrónico</th>
              <th class="date-col">Fecha de Registro</th>
              <th class="actions-col">Acciones</th>
            </tr>
          </thead>
          <tbody>
            <tr data-role="student">
              <td class="user-col">
                <div class="user-info">
                  <div class="user-avatar">
                    <img src="/placeholder.svg?height=40&width=40" alt="María López">
                  </div>
                  <div class="user-details">
                    <div class="user-name">María López</div>
                  </div>
                </div>
              </td>
              <td class="role-col">Estudiante</td>
              <td class="email-col"><EMAIL></td>
              <td class="date-col">22/03/2025</td>
              <td class="actions-col">
                <div class="user-actions">
                  <button class="action-btn view-btn" title="Ver detalles">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="action-btn edit-btn" title="Editar usuario">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="action-btn delete-btn" title="Eliminar usuario">
                    <span class="material-icons">delete</span>
                  </button>
                </div>
              </td>
            </tr>
            
            <tr data-role="teacher">
              <td class="user-col">
                <div class="user-info">
                  <div class="user-avatar">
                    <img src="/placeholder.svg?height=40&width=40" alt="Carlos García">
                  </div>
                  <div class="user-details">
                    <div class="user-name">Carlos García</div>
                  </div>
                </div>
              </td>
              <td class="role-col">Maestro</td>
              <td class="email-col"><EMAIL></td>
              <td class="date-col">15/02/2025</td>
              <td class="actions-col">
                <div class="user-actions">
                  <button class="action-btn view-btn" title="Ver detalles">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="action-btn edit-btn" title="Editar usuario">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="action-btn delete-btn" title="Eliminar usuario">
                    <span class="material-icons">delete</span>
                  </button>
                </div>
              </td>
            </tr>
            
            <tr data-role="parent">
              <td class="user-col">
                <div class="user-info">
                  <div class="user-avatar">
                    <img src="/placeholder.svg?height=40&width=40" alt="Roberto Pérez">
                  </div>
                  <div class="user-details">
                    <div class="user-name">Roberto Pérez</div>
                  </div>
                </div>
              </td>
              <td class="role-col">Padre/Madre</td>
              <td class="email-col"><EMAIL></td>
              <td class="date-col">10/03/2025</td>
              <td class="actions-col">
                <div class="user-actions">
                  <button class="action-btn view-btn" title="Ver detalles">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="action-btn edit-btn" title="Editar usuario">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="action-btn delete-btn" title="Eliminar usuario">
                    <span class="material-icons">delete</span>
                  </button>
                </div>
              </td>
            </tr>
            
            <tr data-role="admin">
              <td class="user-col">
                <div class="user-info">
                  <div class="user-avatar">
                    <img src="/placeholder.svg?height=40&width=40" alt="Laura Sánchez">
                  </div>
                  <div class="user-details">
                    <div class="user-name">Laura Sánchez</div>
                  </div>
                </div>
              </td>
              <td class="role-col">Administrador</td>
              <td class="email-col"><EMAIL></td>
              <td class="date-col">05/01/2025</td>
              <td class="actions-col">
                <div class="user-actions">
                  <button class="action-btn view-btn" title="Ver detalles">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="action-btn edit-btn" title="Editar usuario">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="action-btn delete-btn" title="Eliminar usuario">
                    <span class="material-icons">delete</span>
                  </button>
                </div>
              </td>
            </tr>
            
            <tr data-role="student">
              <td class="user-col">
                <div class="user-info">
                  <div class="user-avatar">
                    <img src="/placeholder.svg?height=40&width=40" alt="Diego Martínez">
                  </div>
                  <div class="user-details">
                    <div class="user-name">Diego Martínez</div>
                  </div>
                </div>
              </td>
              <td class="role-col">Estudiante</td>
              <td class="email-col"><EMAIL></td>
              <td class="date-col">18/02/2025</td>
              <td class="actions-col">
                <div class="user-actions">
                  <button class="action-btn view-btn" title="Ver detalles">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="action-btn edit-btn" title="Editar usuario">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="action-btn delete-btn" title="Eliminar usuario">
                    <span class="material-icons">delete</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Paginación -->
      <div class="pagination">
        <button class="pagination-btn prev-btn" disabled>
          <span class="material-icons">chevron_left</span>
        </button>
        <div class="pagination-pages">
          <button class="pagination-page active">1</button>
          <button class="pagination-page">2</button>
          <button class="pagination-page">3</button>
          <span class="pagination-ellipsis">...</span>
          <button class="pagination-page">10</button>
        </div>
        <button class="pagination-btn next-btn">
          <span class="material-icons">chevron_right</span>
        </button>
      </div>
    </div>
  </main>
</div>

<!-- Modal para crear/editar usuario -->
<div id="user-modal" class="modal-overlay">
  <div class="modal-content user-modal">
    <div class="modal-header">
      <h3 id="user-modal-title">Crear Nuevo Usuario</h3>
      <button class="modal-close-btn">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="modal-body">
      <form id="user-form">
        <div class="form-tabs">
          <button type="button" class="form-tab active" data-tab="basic-info">Información Básica</button>
          <button type="button" class="form-tab" data-tab="account-info">Información de Cuenta</button>
          <button type="button" class="form-tab" data-tab="role-info">Rol y Configuración</button>
        </div>
        
        <div class="form-tab-content active" id="basic-info">
          <div class="form-row">
            <div class="form-group">
              <label for="user-first-name">Nombre</label>
              <input type="text" id="user-first-name" required>
            </div>
            <div class="form-group">
              <label for="user-last-name">Apellidos</label>
              <input type="text" id="user-last-name" required>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="user-email">Correo Electrónico</label>
              <input type="email" id="user-email" required>
            </div>
            <div class="form-group">
              <label for="user-phone">Teléfono</label>
              <input type="tel" id="user-phone">
            </div>
          </div>
          
          <div class="form-group">
            <label for="user-address">Dirección</label>
            <input type="text" id="user-address">
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="user-birthdate">Fecha de Nacimiento</label>
              <input type="date" id="user-birthdate">
            </div>
            <div class="form-group">
              <label for="user-gender">Género</label>
              <select id="user-gender">
                <option value="">Seleccionar</option>
                <option value="male">Masculino</option>
                <option value="female">Femenino</option>
                <option value="other">Otro</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="form-tab-content" id="account-info">
          <div class="form-group">
            <label for="user-username">Nombre de Usuario</label>
            <input type="text" id="user-username" required>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="user-password">Contraseña</label>
              <input type="password" id="user-password" required>
            </div>
            <div class="form-group">
              <label for="user-confirm-password">Confirmar Contraseña</label>
              <input type="password" id="user-confirm-password" required>
            </div>
          </div>
          

          
          <!-- Removed account options section -->
        </div>
        
        <div class="form-tab-content" id="role-info">
          <div class="form-group">
            <label for="user-role">Rol</label>
            <select id="user-role" required>
              <option value="">Seleccionar rol</option>
              <option value="student">Estudiante</option>
              <option value="teacher">Maestro</option>
              <option value="parent">Padre/Madre</option>
              <option value="admin">Administrador</option>
            </select>
          </div>
          
          <!-- Removed student-specific fields -->
          
          <!-- Campos específicos para maestros -->
          <div class="role-specific-fields" id="teacher-fields" style="display: none;">
            <div class="form-group">
              <label for="teacher-specialty">Especialidad</label>
              <select id="teacher-specialty">
                <option value="">Seleccionar especialidad</option>
                <option value="matematicas">Matemáticas</option>
                <option value="ciencia-tecnologia">Ciencia y Tecnología</option>
                <option value="comunicacion">Comunicación</option>
                <option value="ingles">Inglés</option>
                <option value="arte">Arte</option>
                <option value="personal-social">Personal Social</option>
                <option value="educacion-fisica">Educación Física</option>
              </select>
            </div>

            <div class="form-group">
              <label for="teacher-tutor-grade">Grado del cual será tutor</label>
              <select id="teacher-tutor-grade">
                <option value="">Seleccionar grado</option>
                <option value="inicial-3">3 años - Inicial</option>
                <option value="inicial-4">4 años - Inicial</option>
                <option value="inicial-5">5 años - Inicial</option>
                <option value="primaria-1">1° Primaria</option>
                <option value="primaria-2">2° Primaria</option>
                <option value="primaria-3">3° Primaria</option>
                <option value="primaria-4">4° Primaria</option>
                <option value="primaria-5">5° Primaria</option>
                <option value="primaria-6">6° Primaria</option>
              </select>
            </div>
          </div>
          
          <!-- Campos específicos para padres -->
          <div class="role-specific-fields" id="parent-fields" style="display: none;">
            <div class="form-group">
              <label>Estudiantes relacionados</label>
              <div class="checkbox-group">
                <label>
                  <input type="checkbox" name="parent-students" value="1">
                  María López (3° Primaria)
                </label>
                <label>
                  <input type="checkbox" name="parent-students" value="2">
                  Diego Martínez (5° Primaria)
                </label>
                <label>
                  <input type="checkbox" name="parent-students" value="3">
                  Ana Rodríguez (1° Primaria)
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label for="parent-relationship">Relación</label>
              <select id="parent-relationship">
                <option value="father">Padre</option>
                <option value="mother">Madre</option>
                <option value="guardian">Tutor legal</option>
              </select>
            </div>
          </div>
          
          <!-- Removed permissions section -->
        </div>
        
        <!-- Botones para Información Básica -->
        <div class="form-actions step-buttons" id="basic-info-buttons">
          <button type="button" class="btn-secondary" onclick="closeCreateUserModal()">Cancelar</button>
          <button type="button" class="btn-primary" onclick="nextStep(1)">Siguiente</button>
        </div>

        <!-- Botones para Información de Cuenta -->
        <div class="form-actions step-buttons" id="account-info-buttons" style="display: none;">
          <button type="button" class="btn-secondary" onclick="previousStep(1)">Anterior</button>
          <button type="button" class="btn-primary" onclick="nextStep(2)">Siguiente</button>
        </div>

        <!-- Botones para Rol y Configuración -->
        <div class="form-actions step-buttons" id="role-config-buttons" style="display: none;">
          <button type="button" class="btn-secondary" onclick="closeCreateUserModal()">Cancelar</button>
          <button type="button" class="btn-secondary" onclick="previousStep(2)">Anterior</button>
          <button type="submit" class="btn-primary">Guardar Usuario</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal para ver detalles de usuario según su tipo -->
<div id="user-details-modal" class="modal-overlay">
  <div class="modal-content user-details-modal">
    <div class="modal-header">
      <h3 id="user-details-title">Detalles de Usuario</h3>
      <button class="modal-close-btn">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="modal-body">
      <!-- Contenedor para información del usuario -->
      <div class="user-profile-header">
        <div class="user-avatar large">
          <img src="/placeholder.svg?height=80&width=80" alt="Foto de usuario">
        </div>
        <div class="user-profile-info">
          <h2 id="detail-user-name">Nombre del Usuario</h2>
          <p id="detail-user-role">Rol del Usuario</p>
          <p id="detail-user-grade" style="color: var(--primary-color); font-weight: 500;"></p>
          <p id="detail-guardian-type" style="color: var(--text-light); font-size: 0.9rem;"></p>
        </div>
      </div>

      <!-- Contenido específico para estudiantes -->
      <div id="student-details" class="role-specific-details">
        <h3>Boleta de Calificaciones</h3>
        <div class="grades-report">
          <table class="grades-table">
            <thead>
              <tr>
                <th>Curso</th>
                <th>Maestro(a)</th>
                <th>1er Bimestre</th>
                <th>2do Bimestre</th>
                <th>3er Bimestre</th>
                <th>4to Bimestre</th>
                <th>Promedio Final</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Matemáticas</td>
                <td>Carlos García</td>
                <td>16</td>
                <td>17</td>
                <td>18</td>
                <td>17</td>
                <td>17</td>
              </tr>
              <tr>
                <td>Comunicación</td>
                <td>Ana Martínez</td>
                <td>15</td>
                <td>16</td>
                <td>17</td>
                <td>16</td>
                <td>16</td>
              </tr>
              <tr>
                <td>Ciencias</td>
                <td>Roberto Sánchez</td>
                <td>18</td>
                <td>17</td>
                <td>19</td>
                <td>18</td>
                <td>18</td>
              </tr>
              <tr>
                <td>Historia</td>
                <td>María López</td>
                <td>14</td>
                <td>15</td>
                <td>16</td>
                <td>15</td>
                <td>15</td>
              </tr>
              <tr>
                <td>Educación Física</td>
                <td>Jorge Ramírez</td>
                <td>19</td>
                <td>18</td>
                <td>19</td>
                <td>18</td>
                <td>19</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="attendance-summary">
          <h3>Resumen de Asistencia</h3>
          <div class="attendance-stats">
            <div class="attendance-stat">
              <span class="stat-value">92%</span>
              <span class="stat-label">Asistencia</span>
            </div>
            <div class="attendance-stat">
              <span class="stat-value">5</span>
              <span class="stat-label">Faltas</span>
            </div>
            <div class="attendance-stat">
              <span class="stat-value">3</span>
              <span class="stat-label">Tardanzas</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Contenido específico para profesores -->
      <div id="teacher-details" class="role-specific-details">
        <h3>Estado de Cursos</h3>
        <div class="courses-status">
          <table class="courses-table">
            <thead>
              <tr>
                <th>Curso</th>
                <th>Grado</th>
                <th>Estudiantes</th>
                <th>Asistencia</th>
                <th>Notas</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Matemáticas</td>
                <td>5° Primaria</td>
                <td>25</td>
                <td>48/50</td>
                <td><span class="status-badge complete">Cerrado</span></td>
              </tr>
              <tr>
                <td>Matemáticas</td>
                <td>6° Primaria</td>
                <td>24</td>
                <td>45/50</td>
                <td><span class="status-badge pending">Pendiente</span></td>
              </tr>
              <tr>
                <td>Ciencia y Tecnología</td>
                <td>5° Primaria</td>
                <td>25</td>
                <td>47/50</td>
                <td><span class="status-badge complete">Cerrado</span></td>
              </tr>
              <tr>
                <td>Ciencia y Tecnología</td>
                <td>6° Primaria</td>
                <td>24</td>
                <td>42/50</td>
                <td><span class="status-badge pending">Pendiente</span></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="teacher-attendance">
          <h3>Registro de Asistencia del Maestro</h3>
          <div class="attendance-stats">
            <div class="attendance-stat">
              <span class="stat-value">98%</span>
              <span class="stat-label">Asistencia</span>
            </div>
            <div class="attendance-stat">
              <span class="stat-value">2</span>
              <span class="stat-label">Faltas</span>
            </div>
            <div class="attendance-stat">
              <span class="stat-value">1</span>
              <span class="stat-label">Tardanzas</span>
            </div>
          </div>
          <div class="attendance-actions">
            <button class="btn-primary modify-attendance-btn" onclick="redirectToJustifications()">
              <span class="material-icons">edit</span>
              Modificar Asistencias
            </button>
          </div>
        </div>
      </div>

      <!-- Contenido específico para padres -->
      <div id="parent-details" class="role-specific-details">
        <h3>Información de Pago</h3>
        <div class="payment-status">
          <table class="payment-table">
            <thead>
              <tr>
                <th>Concepto</th>
                <th>Monto</th>
                <th>Fecha Límite</th>
                <th>Estado</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Matrícula 2025</td>
                <td>S/. 500.00</td>
                <td>15/01/2025</td>
                <td><span class="status-badge complete">Pagado</span></td>
                <td>
                  <button class="action-btn change-payment-btn" onclick="showChangePaymentModal('matricula-2025', 'complete')" title="Modificar estado">
                    <span class="material-icons">edit</span>
                  </button>
                </td>
              </tr>
              <tr>
                <td>Mensualidad Marzo</td>
                <td>S/. 450.00</td>
                <td>05/03/2025</td>
                <td><span class="status-badge complete">Pagado</span></td>
                <td>
                  <button class="action-btn change-payment-btn" onclick="showChangePaymentModal('marzo-2025', 'complete')" title="Modificar estado">
                    <span class="material-icons">edit</span>
                  </button>
                </td>
              </tr>
              <tr>
                <td>Mensualidad Abril</td>
                <td>S/. 450.00</td>
                <td>05/04/2025</td>
                <td><span class="status-badge pending">Pendiente</span></td>
                <td>
                  <button class="action-btn change-payment-btn" onclick="showChangePaymentModal('abril-2025', 'pending')" title="Modificar estado">
                    <span class="material-icons">edit</span>
                  </button>
                </td>
              </tr>
              <tr>
                <td>Mensualidad Mayo</td>
                <td>S/. 450.00</td>
                <td>05/05/2025</td>
                <td><span class="status-badge pending">Pendiente</span></td>
                <td>
                  <button class="action-btn change-payment-btn" onclick="showChangePaymentModal('mayo-2025', 'pending')" title="Modificar estado">
                    <span class="material-icons">edit</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Sección administrativa para modificar pagos -->
        <div class="admin-payment-section">
          <h4>Administración de Pagos</h4>
          <p class="admin-note">
            <span class="material-icons">info</span>
            Utilice esta sección para modificar el estado de los pagos realizados de manera presencial o por otros medios.
          </p>
        </div>
        
        <h3>Hijos Matriculados</h3>
        <div class="children-list">
          <div class="child-item">
            <div class="child-avatar">
              <img src="/placeholder.svg?height=50&width=50" alt="María López">
            </div>
            <div class="child-info">
              <h4>María López</h4>
              <p>5° Primaria</p>
            </div>
          </div>

          <div class="child-item">
            <div class="child-avatar">
              <img src="/placeholder.svg?height=50&width=50" alt="Juan López">
            </div>
            <div class="child-info">
              <h4>Juan López</h4>
              <p>3° Primaria</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Contenido específico para administradores -->
      <div id="admin-details" class="role-specific-details">
        <h3>Actividad Reciente</h3>
        <div class="admin-activity">
          <div class="activity-timeline">
            <div class="timeline-item">
              <div class="timeline-date">22/03/2025 - 10:30</div>
              <div class="timeline-content">
                <span class="material-icons">person_add</span>
                <span>Creó usuario: María López (Estudiante)</span>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">21/03/2025 - 15:45</div>
              <div class="timeline-content">
                <span class="material-icons">edit</span>
                <span>Modificó usuario: Carlos García (Maestro)</span>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">20/03/2025 - 09:15</div>
              <div class="timeline-content">
                <span class="material-icons">campaign</span>
                <span>Publicó anuncio: "Reunión de padres"</span>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">19/03/2025 - 14:20</div>
              <div class="timeline-content">
                <span class="material-icons">how_to_reg</span>
                <span>Aprobó solicitud de admisión: Familia Rodríguez</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn-secondary modal-close-btn">Cerrar</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para ejemplo de creación de cuenta -->
<div id="account-creation-example-modal" class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Ejemplo de Creación de Cuenta</h3>
      <button class="modal-close-btn">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="modal-body">
      <div class="creation-example">
        <div class="example-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>Información Básica</h4>
            <div class="example-form">
              <div class="form-row">
                <div class="form-group">
                  <label>Nombre</label>
                  <input type="text" value="Juan" disabled>
                </div>
                <div class="form-group">
                  <label>Apellidos</label>
                  <input type="text" value="Pérez García" disabled>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Correo Electrónico</label>
                  <input type="email" value="<EMAIL>" disabled>
                </div>
                <div class="form-group">
                  <label>Teléfono</label>
                  <input type="tel" value="+51 987 654 321" disabled>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="example-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>Información de Cuenta</h4>
            <div class="example-form">
              <div class="form-group">
                <label>Nombre de Usuario</label>
                <input type="text" value="juan.perez" disabled>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Contraseña</label>
                  <input type="password" value="********" disabled>
                </div>
                <div class="form-group">
                  <label>Confirmar Contraseña</label>
                  <input type="password" value="********" disabled>
                </div>
              </div>

            </div>
          </div>
        </div>
        
        <div class="example-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>Rol y Configuración</h4>
            <div class="example-form">
              <div class="form-group">
                <label>Rol</label>
                <select disabled>
                  <option value="teacher" selected>Maestro</option>
                </select>
              </div>
              <div class="form-group">
                <label>Especialidad</label>
                <select disabled>
                  <option value="matematicas" selected>Matemáticas</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <div class="example-step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>Confirmación</h4>
            <div class="confirmation-message">
              <span class="material-icons success-icon">check_circle</span>
              <p>Usuario creado exitosamente</p>
              <div class="user-created-info">
                <p><strong>Nombre:</strong> Juan Pérez García</p>
                <p><strong>Usuario:</strong> juan.perez</p>
                <p><strong>Rol:</strong> Maestro</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn-secondary modal-close-btn">Cerrar</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para cambiar estado de pago -->
<div id="change-payment-modal" class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Modificar Estado de Pago</h3>
      <button class="modal-close-btn">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="modal-body">
      <!-- El contenido se generará dinámicamente -->
    </div>
  </div>
</div>

<script src="./Js/plataforma.js"></script>
<script src="./Js/usuarios_a.js"></script>
</body>
</html>
