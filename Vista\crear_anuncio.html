<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - C<PERSON><PERSON></title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/admin.css">
  <link rel="stylesheet" href="./Css/crear_anuncio.css">
</head>
<body>
  <div class="plataforma-container">
    <!-- Menú lateral -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="./img/logo-escuela.svg" alt="Logo Escuela">
        </div>
        <button class="menu-toggle" id="menu-toggle">
          <span class="material-icons">menu</span>
        </button>
      </div>
      
      <div class="sidebar-content">
        <div class="user-info">
          <div class="user-avatar">
            <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
          </div>
          <div class="user-details">
            <h3>Admin Sistema</h3>
            <p>Administrador</p>
          </div>
        </div>
        
        <nav class="sidebar-menu">
          <ul>
            <li>
              <a href="inicio_a.html">
                <span class="material-icons">dashboard</span>
                <span>Inicio</span>
              </a>
            </li>
            <li>
              <a href="perfil_a.html">
                <span class="material-icons">person</span>
                <span>Perfil</span>
              </a>
            </li>
            <li>
              <a href="usuarios_a.html">
                <span class="material-icons">people</span>
                <span>Usuarios</span>
              </a>
            </li>
            <li class="active">
              <a href="anuncios_admin.html">
                <span class="material-icons">campaign</span>
                <span>Anuncios</span>
              </a>
            </li>
            <li>
              <a href="admision_a.html">
                <span class="material-icons">how_to_reg</span>
                <span>Solicitudes de Admisión</span>
              </a>
            </li>
            <li>
              <a href="configuracion_admin.html">
                <span class="material-icons">settings</span>
                <span>Configuración</span>
              </a>
            </li>
            <li class="separator"></li>
            <li>
              <a href="intranet.html">
                <span class="material-icons">logout</span>
                <span>Cerrar Sesión</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>
    
    <!-- Contenido principal -->
    <main class="main-content">
      <header class="content-header">
        <div class="header-left">
          <h1 id="page-title">Crear Anuncio</h1>
          <p class="current-date">Lunes, 22 de marzo de 2025</p>
        </div>
        <div class="header-right">
          <div class="notifications">
            <button class="notification-btn">
              <span class="material-icons">notifications</span>
              <span class="notification-badge">5</span>
            </button>
          </div>
        </div>
      </header>
      
      <div class="content-body">
        <section class="card">
          <div class="card-header">
            <h2 id="form-title">Nuevo Anuncio</h2>
            <p id="form-description">Complete el formulario para crear un nuevo anuncio</p>
          </div>
          <div class="card-body">
            <form id="crear-anuncio-form" class="crear-anuncio-form">
              <!-- Información básica -->
              <div class="form-section">
                <h3>Información Básica</h3>
                <div class="form-grid">
                  <div class="form-group">
                    <label for="titulo">Título del Anuncio *</label>
                    <input type="text" id="titulo" name="titulo" required>
                  </div>

                  <div class="form-group full-width">
                    <label for="descripcion-corta">Descripción Corta *</label>
                    <input type="text" id="descripcion-corta" name="descripcion-corta" maxlength="150" required>
                    <div class="input-help">Máximo 150 caracteres. Esta descripción aparecerá en las vistas previas.</div>
                  </div>
                </div>
              </div>
              
              <!-- Contenido -->
              <div class="form-section">
                <h3>Contenido</h3>
                <div class="form-group">
                  <label for="contenido">Contenido Completo *</label>
                  <div class="editor-toolbar">
                    <button type="button" class="toolbar-btn" data-format="bold" title="Negrita">
                      <span class="material-icons">format_bold</span>
                    </button>
                    <button type="button" class="toolbar-btn" data-format="italic" title="Cursiva">
                      <span class="material-icons">format_italic</span>
                    </button>
                    <button type="button" class="toolbar-btn" data-format="underline" title="Subrayado">
                      <span class="material-icons">format_underlined</span>
                    </button>
                    <span class="toolbar-separator"></span>
                    <button type="button" class="toolbar-btn" data-format="h2" title="Encabezado 2">
                      <span class="material-icons">title</span>
                    </button>
                    <button type="button" class="toolbar-btn" data-format="h3" title="Encabezado 3">
                      <span class="material-icons">text_fields</span>
                    </button>
                    <span class="toolbar-separator"></span>
                    <button type="button" class="toolbar-btn" data-format="ul" title="Lista con viñetas">
                      <span class="material-icons">format_list_bulleted</span>
                    </button>
                    <button type="button" class="toolbar-btn" data-format="ol" title="Lista numerada">
                      <span class="material-icons">format_list_numbered</span>
                    </button>
                    <span class="toolbar-separator"></span>
                    <button type="button" class="toolbar-btn" data-format="link" title="Insertar enlace">
                      <span class="material-icons">link</span>
                    </button>
                    <button type="button" class="toolbar-btn" data-format="image" title="Insertar imagen">
                      <span class="material-icons">image</span>
                    </button>
                  </div>
                  <div id="editor" class="content-editor" contenteditable="true"></div>
                  <textarea id="contenido" name="contenido" style="display: none;" required></textarea>
                </div>
              </div>
              
              <!-- Imagen principal -->
              <div class="form-section">
                <h3>Imagen Principal</h3>
                <div class="form-group">
                  <label for="imagen-principal">Seleccionar Imagen *</label>
                  <div class="file-upload-container">
                    <div id="imagen-preview" class="imagen-preview">
                      <span class="material-icons">image</span>
                      <p>Ninguna imagen seleccionada</p>
                    </div>
                    <div class="file-upload-actions">
                      <label for="imagen-principal" class="btn-secondary">
                        <span class="material-icons">upload_file</span>
                        <span>Seleccionar Imagen</span>
                      </label>
                      <input type="file" id="imagen-principal" name="imagen-principal" accept="image/*" style="display: none;" required>
                      <button type="button" id="eliminar-imagen" class="btn-outline" disabled>
                        <span class="material-icons">delete</span>
                        <span>Eliminar</span>
                      </button>
                    </div>
                    <div class="input-help">Formatos permitidos: JPG, PNG. Tamaño máximo: 2MB. Dimensiones recomendadas: 1200x630px.</div>
                  </div>
                </div>
              </div>
              
              <!-- Configuración de publicación simplificada -->
              <div class="form-section">
                <h3>Configuración de Publicación</h3>
                <div class="form-grid">
                  <div class="form-group">
                    <label for="estado">Estado *</label>
                    <select id="estado" name="estado" required>
                      <option value="borrador">Guardar como borrador</option>
                      <option value="publicado">Publicar inmediatamente</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <!-- Botones de acción -->
              <div class="form-actions">
                <button type="button" id="cancelar-btn" class="btn-secondary">
                  <span class="material-icons">close</span>
                  <span>Cancelar</span>
                </button>
                <button type="button" id="vista-previa-btn" class="btn-secondary">
                  <span class="material-icons">visibility</span>
                  <span>Vista Previa</span>
                </button>
                <button type="submit" id="guardar-btn" class="btn-primary">
                  <span class="material-icons">save</span>
                  <span>Guardar Anuncio</span>
                </button>
              </div>
            </form>
          </div>
        </section>
      </div>
    </main>
  </div>

  <!-- Modal de Vista Previa -->
  <div id="vista-previa-modal" class="modal-overlay">
    <div class="modal-content vista-previa-modal">
      <div class="modal-header">
        <h3>Vista Previa del Anuncio</h3>
        <button class="modal-close-btn">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="vista-previa-container">
          <div class="vista-previa-imagen">
            <img id="preview-imagen" src="/placeholder.svg?height=400&width=800" alt="Imagen del anuncio">
          </div>
          <div class="vista-previa-contenido">
            <div class="vista-previa-meta">
              <span id="preview-fecha" class="preview-fecha">22/03/2025</span>
            </div>
            <h2 id="preview-titulo" class="preview-titulo">Título del Anuncio</h2>
            <p id="preview-descripcion" class="preview-descripcion">Descripción corta del anuncio que aparecerá en las vistas previas.</p>
            <div id="preview-contenido" class="preview-contenido-completo">
              <p>Contenido completo del anuncio...</p>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn-primary modal-close-btn">
          <span class="material-icons">check</span>
          <span>Aceptar</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de Confirmación -->
  <div id="confirmacion-modal" class="modal-overlay">
    <div class="modal-content confirmacion-modal">
      <div class="modal-header">
        <h3>Confirmar Acción</h3>
        <button class="modal-close-btn">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="confirmacion-icon">
          <span class="material-icons">help</span>
        </div>
        <p id="confirmacion-mensaje">¿Está seguro que desea cancelar? Los cambios no guardados se perderán.</p>
      </div>
      <div class="modal-footer">
        <button type="button" id="confirmar-cancelar-btn" class="btn-secondary modal-close-btn">
          <span>No, volver al formulario</span>
        </button>
        <button type="button" id="confirmar-continuar-btn" class="btn-primary">
          <span>Sí, continuar</span>
        </button>
      </div>
    </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/crear_anuncio.js"></script>
</body>
</html>