<?php
session_start();
require_once '../Modelo/Usuario.php';

/**
 * Controlador para manejar la visualización de imágenes desde la base de datos
 */
class ImagenController {
    private $usuario;

    public function __construct() {
        $this->usuario = new Usuario();
    }

    /**
     * Sirve una imagen de perfil desde la base de datos
     * @param int $usuarioId
     */
    public function servirFotoPerfil($usuarioId) {
        try {
            $sql = "SELECT foto_perfil, tipo_foto, nombre_foto FROM personas WHERE usuario_id = :usuario_id";
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
            
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($resultado && $resultado['foto_perfil']) {
                // Configurar headers para la imagen
                header('Content-Type: ' . $resultado['tipo_foto']);
                header('Content-Length: ' . strlen($resultado['foto_perfil']));
                header('Cache-Control: public, max-age=31536000'); // Cache por 1 año
                header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000));
                // Mostrar la imagen
                echo $resultado['foto_perfil'];
                exit;
            } else {
                // Si no hay imagen, mostrar imagen por defecto
                $this->mostrarImagenPorDefecto();
            }
        } catch (PDOException $e) {
            error_log("Error sirviendo foto de perfil: " . $e->getMessage());
            $this->mostrarImagenPorDefecto();
        }
    }

    /**
     * Muestra una imagen por defecto cuando no hay foto de perfil
     */
    private function mostrarImagenPorDefecto() {
        // Crear una imagen SVG simple como avatar por defecto
        $svg = '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">'
            .'<rect width="200" height="200" fill="#e0e0e0"/>'
            .'<circle cx="100" cy="80" r="30" fill="#9e9e9e"/>'
            .'<path d="M 50 140 Q 100 180 150 140" stroke="#9e9e9e" stroke-width="3" fill="none"/>'
            .'<text x="100" y="110" text-anchor="middle" font-family="Arial" font-size="24" fill="#666">?</text>'
            .'</svg>';
        header('Content-Type: image/svg+xml');
        header('Cache-Control: public, max-age=31536000');
        echo $svg;
        exit;
    }

    /**
     * Verifica si el usuario actual puede ver la imagen solicitada
     * @param int $usuarioId
     * @return bool
     */
    private function puedeVerImagen($usuarioId) {
        // Por ahora, permitir que cualquier usuario autenticado vea las fotos de perfil
        // En el futuro se pueden agregar más restricciones
        return isset($_SESSION['usuario_id']);
    }
}

// Procesar la solicitud de imagen
if (isset($_GET['usuario_id'])) {
    $imagenController = new ImagenController();
    $usuarioId = (int)$_GET['usuario_id'];
    if ($imagenController->puedeVerImagen($usuarioId)) {
        $imagenController->servirFotoPerfil($usuarioId);
    } else {
        http_response_code(403);
        echo "Acceso denegado";
    }
} else {
    http_response_code(400);
    echo "ID de usuario requerido";
}
?> 