/* Estilos específicos para la gestión de justificaciones de asistencia */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Estilos para el botón de toggle del sidebar */
.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--secondary-color);
}

.sidebar-toggle .material-icons {
    font-size: 1.5rem;
}

/* Header personalizado */
.content-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 5px;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb .separator {
    color: var(--text-light);
}

.breadcrumb .current {
    color: var(--text-light);
}

/* Estilos adicionales para notificaciones */
.notifications {
    display: flex;
    align-items: center;
}

.notification-btn {
    background: none;
    border: none;
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: var(--transition);
    color: var(--text-color);
}

.notification-btn:hover {
    background-color: var(--secondary-color);
}

.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Tarjeta de información del maestro */
.teacher-info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 25px;
}

.teacher-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
    box-shadow: 0 4px 8px rgba(42, 77, 183, 0.2);
}

.teacher-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.teacher-details {
    flex: 1;
}

.teacher-details h2 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 1.6rem;
    font-weight: 700;
}

.teacher-role {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.teacher-grade {
    margin: 0;
    color: var(--text-light);
    font-size: 1rem;
}

.teacher-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid #f0f0f0;
    min-width: 80px;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    display: block;
    color: var(--text-light);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Barra de herramientas */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.toolbar-left {
    display: flex;
    gap: 25px;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    background: white;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    min-width: 180px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
}

.btn-secondary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    color: var(--text-color);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(42, 77, 183, 0.2);
}

/* Contenedor de justificaciones */
.justifications-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Tarjetas de justificación */
.justification-card {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.3s ease;
}

.justification-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.date-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-color);
}

.date-info .material-icons {
    color: var(--primary-color);
    font-size: 1.4rem;
}

.badges {
    display: flex;
    gap: 12px;
}

.type-badge, .status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Badges de tipo */
.type-badge.tardanza {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.type-badge.falta {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.type-badge.salida-anticipada {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Badges de estado */
.status-badge.pendiente {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3); }
    100% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
}

.status-badge.aprobada {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.rechazada {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.card-content {
    padding: 30px;
}

.reason-section {
    margin-bottom: 25px;
}

.reason-section h4 {
    margin: 0 0 15px 0;
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.reason-section p {
    margin: 0;
    color: var(--text-color);
    line-height: 1.6;
    font-size: 1rem;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.actions-section {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.btn-success, .btn-danger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 24px;
    border: none;
    border-radius: 10px;
    font-size: 0.95rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
}

.processed-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    color: var(--success-color);
}

.processed-info.rejected {
    color: var(--danger-color);
}

.processed-info .material-icons {
    font-size: 1.4rem;
}

/* Filtros */
.justification-card[style*="display: none"] {
    display: none !important;
}

/* Ajustes adicionales para el layout */
.content-body {
    padding: 20px 30px;
}

.logo-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-left: 10px;
}

.sidebar.collapsed .logo-text {
    display: none;
}

/* Responsive */
@media (max-width: 992px) {
    .teacher-info-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .teacher-stats {
        justify-content: center;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .toolbar-left {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .badges {
        flex-wrap: wrap;
    }
    
    .actions-section {
        flex-direction: column;
    }
    
    .btn-success, .btn-danger {
        justify-content: center;
    }
    
    .teacher-stats {
        flex-direction: column;
        gap: 10px;
    }
}
