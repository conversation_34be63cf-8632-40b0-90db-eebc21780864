/* Estilos para la página Nosotros */

/* Hero Section */
.nosotros-hero {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .nosotros-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .nosotros-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
  }
  
  /* Misión y Visión */
  .mision-vision {
    padding: 5rem 0;
    background-color: #f9f9f9;
  }
  
  .info-card {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .info-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .info-header .material-icons {
    font-size: 2.5rem;
  }
  
  .info-header h2 {
    font-size: 1.8rem;
    margin: 0;
  }
  
  .info-content {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .info-box {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 2rem;
    border-left: 5px solid var(--primary-color);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  
  .info-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .info-box h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
  
  .info-box p {
    line-height: 1.7;
    color: #555;
    text-align: justify;
  }
  
  /* Directora */
  .directora {
    padding: 5rem 0;
    background-color: var(--white);
  }
  
  .directora-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: center;
  }
  
  .directora-imagen {
    position: relative;
  }
  
  .directora-imagen img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }
  
  .directora-info h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2rem;
  }
  
  .directora-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
  }
  
  .directora-cargo {
    display: inline-block;
    background-color: #f0f0f0;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1.5rem;
  }
  
  .directora-bio {
    margin-bottom: 2rem;
  }
  
  .directora-bio p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: #555;
  }
  
  .directora-mensaje {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 2rem;
    position: relative;
  }
  
  .directora-mensaje blockquote {
    font-style: italic;
    color: #555;
    font-size: 1.1rem;
    line-height: 1.7;
    position: relative;
    padding-left: 2rem;
  }
  
  .directora-mensaje blockquote::before {
    content: "\201C"; /* Comilla Unicode en lugar de comillas directas */
    font-size: 4rem;
    color: var(--primary-color);
    position: absolute;
    left: -1rem;
    top: -1rem;
    opacity: 0.3;
  }
  
  /* Ubicación */
  .ubicacion {
    padding: 5rem 0;
    background-color: #f9f9f9;
  }
  
  .ubicacion-header {
    text-align: center;
    margin-bottom: 3rem;
  }
  
  .ubicacion-header h2 {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
  }
  
  .ubicacion-header p {
    color: #666;
    max-width: 600px;
    margin: 0 auto;
  }
  
  .mapa-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
  }
  
  .direccion-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }
  
  .direccion-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s;
  }
  
  .direccion-item:hover {
    transform: translateY(-5px);
  }
  
  .direccion-item .material-icons {
    color: var(--primary-color);
    font-size: 2rem;
  }
  
  .direccion-item p {
    margin: 0;
    color: #555;
  }
  
  /* Responsive Styles */
  @media (max-width: 768px) {
    .nosotros-hero {
      padding: 100px 0 40px;
    }
  
    .nosotros-hero h1 {
      font-size: 2rem;
    }
  
    .info-content {
      grid-template-columns: 1fr;
    }
  
    .directora-container {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
  
    .directora-imagen {
      max-width: 300px;
      margin: 0 auto;
    }
  
    .direccion-info {
      grid-template-columns: 1fr;
    }
  }
  