document.addEventListener("DOMContentLoaded", () => {
    // Elementos DOM
    const editGradeBtns = document.querySelectorAll(".grade-action-btn.edit-btn")
    const viewGradeBtns = document.querySelectorAll(".grade-action-btn.view-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const editGradeModal = document.getElementById("edit-grade-modal")
    const viewGradeModal = document.getElementById("view-grade-modal")
    const searchInput = document.querySelector(".search-input")
    const exportBtn = document.querySelector(".export-btn")
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
    }
  
    function setupEventListeners() {
      // Event listeners para botones de editar calificaciones
      editGradeBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          openEditGradeModal()
        })
      })
  
      // Event listeners para botones de ver detalles
      viewGradeBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          openViewGradeModal()
        })
      })
  
      // Event listeners para cerrar modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })
  
      // Event listener para búsqueda
      if (searchInput) {
        searchInput.addEventListener("input", filterStudents)
      }
  
      // Event listener para exportar calificaciones
      if (exportBtn) {
        exportBtn.addEventListener("click", exportGrades)
      }
    }
  
    function openEditGradeModal() {
      if (editGradeModal) {
        editGradeModal.classList.add("active")
      }
    }
  
    function openViewGradeModal() {
      if (viewGradeModal) {
        viewGradeModal.classList.add("active")
      }
    }
  
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  
    function filterStudents() {
      const searchTerm = searchInput.value.toLowerCase()
      const rows = document.querySelectorAll(".grades-table tbody tr")

      rows.forEach((row) => {
        const studentName = row.querySelector(".student-name").textContent.toLowerCase()

        if (studentName.includes(searchTerm)) {
          row.style.display = ""
        } else {
          row.style.display = "none"
        }
      })
    }
  
    function exportGrades() {
      // Datos de ejemplo para exportar
      const gradesData = [
        {
          estudiante: "Ana García",
          ejerciciosFracciones: "8/10",
          multiplicacionFracciones: "9/10",
          representacionGrafica: "10/10",
          examenParcial: "18/20",
          examenFinal: "17/20",
          participacion: "9/10",
          promedio: "16.2"
        },
        {
          estudiante: "Carlos Rodríguez",
          ejerciciosFracciones: "7/10",
          multiplicacionFracciones: "8/10",
          representacionGrafica: "9/10",
          examenParcial: "16/20",
          examenFinal: "15/20",
          participacion: "8/10",
          promedio: "14.8"
        },
        {
          estudiante: "Sofía Martínez",
          ejerciciosFracciones: "--/10",
          multiplicacionFracciones: "10/10",
          representacionGrafica: "9/10",
          examenParcial: "19/20",
          examenFinal: "18/20",
          participacion: "10/10",
          promedio: "17.2"
        }
      ]

      // Crear CSV
      const headers = [
        "Estudiante",
        "Ejercicios de Fracciones",
        "Multiplicación de fracciones",
        "Representación gráfica",
        "Examen Parcial",
        "Examen Final",
        "Participación",
        "Promedio"
      ]

      let csvContent = headers.join(",") + "\n"

      gradesData.forEach(row => {
        const values = [
          row.estudiante,
          row.ejerciciosFracciones,
          row.multiplicacionFracciones,
          row.representacionGrafica,
          row.examenParcial,
          row.examenFinal,
          row.participacion,
          row.promedio
        ]
        csvContent += values.join(",") + "\n"
      })

      // Crear y descargar archivo
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute("href", url)
        link.setAttribute("download", "calificaciones_matematicas_5to.csv")
        link.style.visibility = "hidden"
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    }
  })
  
  