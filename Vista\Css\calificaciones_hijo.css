/* Estilos específicos para la sección de calificaciones del hijo */

/* Variables */
:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Selector de estudiante */
.student-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  background-color: white;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
}

.student-selector label {
  font-weight: 500;
  color: var(--text-color);
}

.student-selector select {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 0.95rem;
  color: var(--text-color);
  background-color: white;
  outline: none;
  cursor: pointer;
  transition: var(--transition);
}

.student-selector select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* Tabla de calificaciones */
.grades-table-container {
  overflow-x: auto;
  margin-bottom: 30px;
}

.grades-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.grades-table thead {
  background-color: var(--primary-light);
}

.grades-table th {
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: var(--primary-color);
  white-space: nowrap;
}

.grades-table td {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.grades-table tbody tr:last-child td {
  border-bottom: none;
}

.grades-table tbody tr:hover {
  background-color: var(--secondary-color);
}

.course-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.course-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.math-icon {
  background-color: #2196f3;
}

.language-icon {
  background-color: #4caf50;
}

.science-icon {
  background-color: #ff9800;
}

.english-icon {
  background-color: #9c27b0;
}

.history-icon {
  background-color: #e91e63;
}

.grade {
  font-weight: 600;
}

.good-grade {
  color: var(--success-color);
}

.average-grade {
  color: var(--warning-color);
}

.poor-grade {
  color: var(--danger-color);
}

/* Conducta del estudiante */
.conduct-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.conduct-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.conduct-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.conduct-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.conduct-icon .material-icons {
  font-size: 2rem;
  color: var(--primary-color);
}

.conduct-info {
  flex: 1;
}

.conduct-info h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-light);
}

.conduct-value {
  font-size: 1.2rem;
  font-weight: 700;
}

.conduct-value.excellent {
  color: #4caf50;
}

.conduct-value.good {
  color: #2196f3;
}

.conduct-value.average {
  color: #ff9800;
}

.conduct-value.poor {
  color: #f44336;
}

.conduct-details {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 30px;
}

.conduct-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.conduct-table-container {
  overflow-x: auto;
}

.conduct-table {
  width: 100%;
  border-collapse: collapse;
}

.conduct-table th {
  padding: 12px 15px;
  text-align: left;
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.conduct-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
}

.conduct-table tr:last-child td {
  border-bottom: none;
}

.conduct-table tr.positive-conduct {
  background-color: rgba(76, 175, 80, 0.05);
}

.conduct-table tr.negative-conduct {
  background-color: rgba(244, 67, 54, 0.05);
}

.conduct-table tr:hover {
  background-color: var(--secondary-color);
}

/* Asistencia */
.attendance-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.attendance-stat {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.attendance-stat:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.attendance-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.attendance-label {
  color: var(--text-light);
  font-size: 0.95rem;
}

/* Nuevo diseño de asistencia con pestañas */
.attendance-details {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: 30px;
}

.attendance-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--secondary-color);
}

.attendance-tab {
  padding: 15px 20px;
  background: none;
  border: none;
  font-family: inherit;
  font-size: 1rem;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  flex: 1;
  text-align: center;
}

.attendance-tab:hover {
  color: var(--primary-color);
  background-color: rgba(42, 77, 183, 0.05);
}

.attendance-tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.attendance-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.attendance-tab-content {
  display: none;
  padding: 20px;
}

.attendance-tab-content.active {
  display: block;
}

.attendance-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.attendance-list-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.attendance-count {
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.attendance-days {
  list-style: none;
  padding: 0;
  margin: 0;
}

.attendance-day {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.attendance-day:last-child {
  border-bottom: none;
}

.day-date {
  font-weight: 500;
  color: var(--text-color);
  min-width: 120px;
}

.day-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.day-status .material-icons {
  font-size: 1.2rem;
}

.day-status span:not(.material-icons) {
  color: var(--text-light);
}

.day-details {
  width: 100%;
  margin-top: 5px;
  padding-left: 125px;
  color: var(--text-light);
  font-size: 0.9rem;
}

.day-details p {
  margin: 0;
}

.no-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-light);
  text-align: center;
}

.no-records .material-icons {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 15px;
}

.no-records p {
  font-size: 1.1rem;
  margin: 0;
}

/* Botones de acción */
.period-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.period-selector label,
.month-selector label {
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.period-selector select,
.month-selector select {
  padding: 8px 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: white;
  color: var(--text-color);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 35px;
  min-width: 140px;
}

.period-selector select:hover,
.month-selector select:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(42, 77, 183, 0.1);
}

.period-selector select:focus,
.month-selector select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
}

.month-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.report-actions {
  display: flex;
  gap: 10px;
}

.download-btn,
.summary-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
}

.download-btn {
  background-color: var(--primary-color);
  color: white;
}

.summary-btn {
  background-color: #4caf50;
  color: white;
}

.download-btn:hover {
  background-color: #1e40af;
}

.summary-btn:hover {
  background-color: #388e3c;
}

.download-btn .material-icons,
.summary-btn .material-icons {
  font-size: 1.2rem;
}

/* Modal de resumen */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  transform: translateY(-20px);
  transition: transform 0.3s;
}

.modal-overlay.active .modal-content {
  transform: translateY(0);
}

.modal-large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.modal-close-btn {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.modal-close-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.modal-body {
  padding: 30px 40px;
}

/* Estilos para el resumen de calificaciones */
.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  padding: 0 10px;
}

.student-info-summary h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.student-info-summary p {
  font-size: 0.95rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.summary-actions {
  display: flex;
  gap: 10px;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary:hover {
  background-color: #1e40af;
}

.summary-section {
  margin-bottom: 30px;
  padding: 0 10px;
}

.summary-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
  padding-bottom: 5px;
  border-bottom: 1px solid var(--border-color);
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.summary-table th {
  padding: 12px 15px;
  text-align: left;
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.summary-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
}

.summary-table tr:last-child td {
  border-bottom: none;
}

.grade-cell {
  font-weight: 600;
}

.grade-cell.good {
  color: var(--success-color);
}

.grade-cell.average {
  color: var(--warning-color);
}

.grade-cell.poor {
  color: var(--danger-color);
}

.conduct-cell {
  font-weight: 600;
}

.conduct-cell.excellent {
  color: #4caf50;
}

.conduct-cell.good {
  color: #2196f3;
}

.conduct-cell.average {
  color: #ff9800;
}

.conduct-cell.poor {
  color: #f44336;
}

.attendance-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 0 10px;
}

.attendance-summary-item {
  display: flex;
  flex-direction: column;
  background-color: var(--secondary-color);
  padding: 15px;
  border-radius: 5px;
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.summary-comments {
  background-color: var(--secondary-color);
  padding: 15px;
  border-radius: 5px;
}

.summary-comments p {
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
  margin-bottom: 10px;
}

.summary-comments p:last-child {
  margin-bottom: 0;
}

.summary-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 35px;
  padding: 25px 15px 0;
  border-top: 1px solid var(--border-color);
}

.signature-area {
  display: flex;
  gap: 30px;
}

.signature-line {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.signature-line span {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.signature-line p {
  font-size: 0.9rem;
  color: var(--text-color);
  text-align: center;
  line-height: 1.3;
}

.school-stamp {
  width: 100px;
  height: 100px;
}

.school-stamp img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Comentarios de docentes */
.teacher-comments {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.comment-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.teacher-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.teacher-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--primary-light);
}

.teacher-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.teacher-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 4px 0;
}

.teacher-info p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 0;
  font-weight: 500;
}

.comment-date {
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.comment-content {
  line-height: 1.6;
  color: var(--text-color);
}

.comment-content p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 992px) {
  .conduct-summary,
  .attendance-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  .summary-header {
    flex-direction: column;
    gap: 15px;
  }

  .summary-actions {
    width: 100%;
    justify-content: space-between;
  }

  .attendance-tabs {
    flex-wrap: wrap;
  }

  .attendance-tab {
    flex: 1 0 50%;
  }
}

@media (max-width: 768px) {
  .conduct-summary,
  .attendance-summary {
    grid-template-columns: 1fr;
  }

  .student-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .student-selector select {
    width: 100%;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .period-selector {
    width: 100%;
    flex-wrap: wrap;
  }

  .report-actions {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }

  .attendance-tab {
    flex: 1 0 100%;
  }

  .day-details {
    padding-left: 0;
  }

  .signature-area {
    flex-direction: column;
    gap: 20px;
  }

  .summary-footer {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .school-stamp {
    margin-top: 20px;
  }

  /* Mejoras de padding para móviles */
  .modal-body {
    padding: 20px 25px;
  }

  .summary-header,
  .summary-section {
    padding: 0 5px;
  }

  .summary-footer {
    padding: 20px 10px 0;
  }

  .attendance-summary-grid {
    padding: 0 5px;
  }
}