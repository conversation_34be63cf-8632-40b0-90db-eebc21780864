document.addEventListener("DOMContentLoaded", () => {
    // Elementos DOM
    const conversationItems = document.querySelectorAll(".conversation-item")
    const messageInput = document.querySelector(".message-input")
    const sendBtn = document.querySelector(".send-btn")
    const chatMessages = document.querySelector(".chat-messages")
    const searchInput = document.querySelector(".search-input")
    const messageTypeButtons = document.querySelectorAll(".message-type-btn")
    const messageItems = document.querySelectorAll(".message-item")
    const newMessageBtn = document.querySelector(".new-message-btn")
    const newMessageModal = document.getElementById("new-message-modal")
    const newMessageForm = document.getElementById("new-message-form")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const replyTextarea = document.querySelector(".reply-editor textarea")
    const messageAttachment = document.getElementById("message-attachment")
    const attachmentList = document.getElementById("attachment-list")
  
    // Nuevos elementos DOM
    const emailItems = document.querySelectorAll(".email-item")
    const composeBtn = document.querySelector(".compose-btn")
    const composeModal = document.getElementById("compose-modal")
    const recipientsBtn = document.querySelector(".recipients-btn")
    const recipientsModal = document.getElementById("recipients-modal")
    const confirmRecipientsBtn = document.getElementById("confirm-recipients-btn")
    const composeForm = document.getElementById("compose-form")
    const refreshBtn = document.querySelector(".refresh-btn")
    const starBtns = document.querySelectorAll(".email-item-star")
    const composeAttachment = document.getElementById("compose-attachment")
    const selectAllStudents = document.getElementById("select-all-students")
    const studentCheckboxes = document.querySelectorAll(".recipient-item input[type='checkbox']")
    const scrollIndicator = document.querySelector(".scroll-indicator")
    const emailViewContent = document.querySelector(".email-view-content")
    const composeSubjectType = document.getElementById("compose-subject-type")
    const customSubjectField = document.getElementById("custom-subject-field")
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
    }
  
    function setupEventListeners() {
      // Event listeners para elementos de conversación
      if (conversationItems) {
        conversationItems.forEach((item) => {
          item.addEventListener("click", () => {
            // Quitar la clase active de todos los elementos
            conversationItems.forEach((el) => {
              el.classList.remove("active")
            })
  
            // Agregar la clase active al elemento seleccionado
            item.classList.add("active")
  
            // Actualizar la información del chat
            updateChatHeader(item.dataset.studentId)
  
            // Cargar los mensajes de la conversación
            loadChatMessages(item.dataset.studentId)
  
            // Quitar el badge de mensajes no leídos
            const unreadBadge = item.querySelector(".unread-badge")
            if (unreadBadge) {
              unreadBadge.remove()
            }
          })
        })
      }
  
      // Event listeners para botones de tipo de mensaje
      messageTypeButtons.forEach((btn) => {
        btn.addEventListener("click", () => {
          // Quitar la clase active de todos los botones
          messageTypeButtons.forEach((el) => {
            el.classList.remove("active")
          })
  
          // Agregar la clase active al botón seleccionado
          btn.classList.add("active")
  
          // Cambiar la vista según el tipo seleccionado
          changeMessageType(btn.dataset.type)
        })
      })
  
      // Event listeners para elementos de mensaje
      messageItems.forEach((item) => {
        item.addEventListener("click", () => {
          // Quitar la clase active de todos los elementos
          messageItems.forEach((el) => {
            el.classList.remove("active")
          })
  
          // Agregar la clase active al elemento seleccionado
          item.classList.add("active")
  
          // Quitar la clase unread
          item.classList.remove("unread")
          item.querySelector(".unread-indicator")?.remove()
  
          // Cargar el mensaje
          loadMessageDetail(item.dataset.id)
        })
      })
  
      // Event listener para nuevo mensaje
      if (newMessageBtn) {
        newMessageBtn.addEventListener("click", openNewMessageModal)
      }
  
      // Event listeners para cerrar modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })
  
      // Event listener para enviar mensaje
      if (sendBtn) {
        sendBtn.addEventListener("click", sendReply)
      }
  
      // Event listener para enviar mensaje
      if (messageInput) {
        sendBtn.addEventListener("click", sendMessage)
  
        // Event listener para enviar mensaje con Enter
        messageInput.addEventListener("keydown", (e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault()
            sendMessage()
          }
        })
  
        // Auto-resize para el textarea
        messageInput.addEventListener("input", () => {
          messageInput.style.height = "auto"
          messageInput.style.height = messageInput.scrollHeight + "px"
        })
      }
  
      // Event listener para búsqueda
      if (searchInput) {
        searchInput.addEventListener("input", filterConversations)
        searchInput.addEventListener("input", searchMessages)
        searchInput.addEventListener("input", searchEmails) // Agregado
      }
  
      // Event listener para adjuntos
      if (messageAttachment) {
        messageAttachment.addEventListener("change", handleAttachments)
      }
  
      // Event listener para enviar nuevo mensaje
      if (newMessageForm) {
        newMessageForm.addEventListener("submit", sendNewMessage)
      }
  
      // Event listeners para elementos de email
      emailItems.forEach((item) => {
        item.addEventListener("click", () => {
          // Quitar la clase selected de todos los elementos
          emailItems.forEach((el) => {
            el.classList.remove("selected")
          })
  
          // Agregar la clase selected al elemento seleccionado
          item.classList.add("selected")
  
          // Quitar la clase unread
          item.classList.remove("unread")
  
          // Cargar el mensaje
          loadEmailMessage(item.dataset.id)
        })
      })
  
      // Event listener para botón de redactar
      if (composeBtn) {
        composeBtn.addEventListener("click", openComposeModal)
      }
  
      // Event listener para botón de destinatarios
      if (recipientsBtn) {
        recipientsBtn.addEventListener("click", openRecipientsModal)
      }
  
      // Event listener para confirmar destinatarios
      if (confirmRecipientsBtn) {
        confirmRecipientsBtn.addEventListener("click", confirmRecipients)
      }
  
      // Event listener para seleccionar todos los estudiantes
      if (selectAllStudents) {
        selectAllStudents.addEventListener("change", toggleAllStudents)
      }
  
      // Event listener para enviar mensaje compuesto
      if (composeForm) {
        composeForm.addEventListener("submit", sendComposedEmail)
      }
  
      // Event listener para refrescar
      if (refreshBtn) {
        refreshBtn.addEventListener("click", refreshEmails)
      }
  
      // Event listeners para marcar con estrella
      starBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
          e.stopPropagation()
          toggleStar(btn)
        })
      })
  
      // Event listener para adjuntos
      if (composeAttachment) {
        composeAttachment.addEventListener("change", handleAttachments)
      }
  
      // Event listener para scroll indicator
      if (scrollIndicator) {
        scrollIndicator.addEventListener("click", () => {
          emailViewContent.scrollTop = 0
        })
      }
  
      // Mostrar/ocultar indicador de scroll según la posición
      if (emailViewContent) {
        emailViewContent.addEventListener("scroll", () => {
          if (scrollIndicator) {
            if (emailViewContent.scrollTop > 100) {
              scrollIndicator.style.display = "flex"
            } else {
              scrollIndicator.style.display = "none"
            }
          }
        })
      }

      // Event listener para el selector de tipo de asunto
      if (composeSubjectType) {
        composeSubjectType.addEventListener("change", handleSubjectTypeChange)
      }
    }
  
    function updateChatHeader(studentId) {
      // Aquí se actualizaría la información del encabezado del chat
      // según el estudiante seleccionado
      console.log("Actualizando encabezado para estudiante:", studentId)
    }
  
    function loadChatMessages(studentId) {
      // Aquí se cargarían los mensajes de la conversación
      // según el estudiante seleccionado
      console.log("Cargando mensajes para estudiante:", studentId)
    }
  
    function sendMessage() {
      const messageText = messageInput.value.trim()
  
      if (messageText) {
        // Crear el elemento de mensaje
        const messageElement = document.createElement("div")
        messageElement.className = "message sent"
  
        // Obtener la hora actual
        const now = new Date()
        const hours = now.getHours().toString().padStart(2, "0")
        const minutes = now.getMinutes().toString().padStart(2, "0")
        const timeString = `${hours}:${minutes}`
  
        // Agregar el contenido del mensaje
        messageElement.innerHTML = `
          <div class="message-content">
            <p>${messageText}</p>
          </div>
          <div class="message-time">${timeString}</div>
        `
  
        // Agregar el mensaje al chat
        chatMessages.appendChild(messageElement)
  
        // Limpiar el input
        messageInput.value = ""
        messageInput.style.height = "auto"
  
        // Hacer scroll al final del chat
        chatMessages.scrollTop = chatMessages.scrollHeight
  
        // Simular respuesta después de 2 segundos
        setTimeout(() => {
          simulateResponse()
        }, 2000)
      }
    }
  
    function simulateResponse() {
      // Crear el elemento de mensaje
      const messageElement = document.createElement("div")
      messageElement.className = "message received"
  
      // Obtener la hora actual
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, "0")
      const minutes = now.getMinutes().toString().padStart(2, "0")
      const timeString = `${hours}:${minutes}`
  
      // Agregar el contenido del mensaje
      messageElement.innerHTML = `
        <div class="message-content">
          <p>¡Exacto! El MCD de 15 y 25 es 5. Entonces 15/25 simplificado sería 3/5.</p>
        </div>
        <div class="message-time">${timeString}</div>
      `
  
      // Agregar el mensaje al chat
      chatMessages.appendChild(messageElement)
  
      // Hacer scroll al final del chat
      chatMessages.scrollTop = chatMessages.scrollHeight
    }
  
    function filterConversations() {
      const searchTerm = searchInput.value.toLowerCase()
  
      conversationItems.forEach((item) => {
        const studentName = item.querySelector(".student-name").textContent.toLowerCase()
        const messagePreview = item.querySelector(".conversation-preview p").textContent.toLowerCase()
  
        if (studentName.includes(searchTerm) || messagePreview.includes(searchTerm)) {
          item.style.display = "flex"
        } else {
          item.style.display = "none"
        }
      })
    }
  
    function changeMessageType(type) {
      console.log(`Cambiando a mensajes de ${type}`)
      // Aquí se implementaría la lógica para cambiar entre mensajes de estudiantes y padres
    }
  
    function loadMessageDetail(messageId) {
      console.log(`Cargando mensaje con ID: ${messageId}`)
      // Aquí se cargaría el contenido del mensaje seleccionado
    }
  
    function loadEmailMessage(emailId) {
      // Aquí se cargaría el contenido del mensaje seleccionado
      console.log(`Cargando mensaje con ID: ${emailId}`)
  
      // En una implementación real, se haría una petición al servidor
      // Por ahora, simplemente actualizamos la UI para mostrar que se ha leído el mensaje
      const emailItem = document.querySelector(`.email-item[data-id="${emailId}"]`)
      if (emailItem) {
        emailItem.classList.remove("unread")
      }
    }
  
    function openNewMessageModal() {
      if (newMessageModal) {
        newMessageModal.classList.add("active")
      }
    }
  
    function openComposeModal() {
      if (composeModal) {
        composeModal.classList.add("active")
        document.getElementById("compose-to").focus()

        // Resetear el formulario
        if (composeSubjectType) {
          composeSubjectType.value = ""
        }
        if (customSubjectField) {
          customSubjectField.style.display = "none"
        }
      }
    }

    function handleSubjectTypeChange() {
      if (!composeSubjectType || !customSubjectField) return

      const selectedValue = composeSubjectType.value

      if (selectedValue === "otro") {
        // Mostrar campo personalizado para "Otro"
        customSubjectField.style.display = "block"
        document.getElementById("compose-subject").required = true
      } else {
        // Ocultar campo personalizado
        customSubjectField.style.display = "none"
        document.getElementById("compose-subject").required = false
        document.getElementById("compose-subject").value = ""
      }
    }
  
    function openRecipientsModal(e) {
      if (e) e.preventDefault()
      if (recipientsModal) {
        recipientsModal.classList.add("active")
      }
    }
  
    function confirmRecipients() {
      // Obtener los estudiantes seleccionados
      const selectedStudents = []
      studentCheckboxes.forEach((checkbox) => {
        if (checkbox.checked) {
          const label = document.querySelector(`label[for="${checkbox.id}"]`)
          if (label) {
            selectedStudents.push(label.textContent)
          }
        }
      })
  
      // Actualizar el campo de destinatarios
      const composeToField = document.getElementById("compose-to")
      if (composeToField) {
        composeToField.value = selectedStudents.join(", ")
      }
  
      // Cerrar el modal
      closeModal(recipientsModal)
    }
  
    function toggleAllStudents() {
      if (selectAllStudents) {
        const checked = selectAllStudents.checked
        studentCheckboxes.forEach((checkbox) => {
          checkbox.checked = checked
        })
      }
    }
  
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  
    function closeModal(modal) {
      if (modal) {
        modal.classList.remove("active")
      }
    }
  
    function sendReply() {
      if (replyTextarea) {
        const replyText = replyTextarea.value.trim()
  
        if (replyText) {
          // Aquí se enviaría la respuesta
          console.log(`Enviando respuesta: ${replyText}`)
  
          // Limpiar el textarea
          replyTextarea.value = ""
  
          // Mostrar mensaje de éxito
          showNotification("Respuesta enviada correctamente", "success")
        } else {
          showNotification("Por favor, escribe un mensaje", "error")
        }
      }
    }
  
    function searchMessages() {
      const searchTerm = searchInput.value.toLowerCase()
  
      messageItems.forEach((item) => {
        const senderName = item.querySelector(".sender-name").textContent.toLowerCase()
        const messageSubject = item.querySelector(".message-subject").textContent.toLowerCase()
        const messageSnippet = item.querySelector(".message-snippet").textContent.toLowerCase()
  
        if (
          senderName.includes(searchTerm) ||
          messageSubject.includes(searchTerm) ||
          messageSnippet.includes(searchTerm)
        ) {
          item.style.display = "flex"
        } else {
          item.style.display = "none"
        }
      })
    }
  
    function searchEmails() {
      const searchTerm = searchInput.value.toLowerCase()
  
      emailItems.forEach((item) => {
        const sender = item.querySelector(".email-item-sender").textContent.toLowerCase()
        const subject = item.querySelector(".email-item-subject").textContent.toLowerCase()
        const snippet = item.querySelector(".email-item-snippet").textContent.toLowerCase()
  
        if (sender.includes(searchTerm) || subject.includes(searchTerm) || snippet.includes(searchTerm)) {
          item.style.display = "flex"
        } else {
          item.style.display = "none"
        }
      })
    }
  
    function refreshEmails() {
      // Aquí se recargarían los mensajes
      console.log("Refrescando mensajes...")
  
      // Mostrar un indicador de carga
      if (refreshBtn) {
        refreshBtn.innerHTML = '<span class="material-icons rotating">refresh</span>'
  
        // Simular una carga
        setTimeout(() => {
          refreshBtn.innerHTML = '<span class="material-icons">refresh</span>'
          showNotification("Mensajes actualizados", "success")
        }, 1000)
      }
    }
  
    function toggleStar(btn) {
      btn.classList.toggle("starred")
  
      if (btn.classList.contains("starred")) {
        btn.innerHTML = '<span class="material-icons">star</span>'
      } else {
        btn.innerHTML = '<span class="material-icons">star_border</span>'
      }
    }
  
    function handleAttachments() {
      const files = messageAttachment ? messageAttachment.files : composeAttachment.files
  
      if (files.length > 0) {
        attachmentList.innerHTML = ""
  
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          const fileSize = formatFileSize(file.size)
  
          const fileItem = document.createElement("div")
          fileItem.className = "attachment-item"
          fileItem.innerHTML = `
            <span class="material-icons">attach_file</span>
            <span class="attachment-name">${file.name}</span>
            <span class="attachment-size">${fileSize}</span>
            <button type="button" class="remove-attachment-btn" data-index="${i}">
              <span class="material-icons">close</span>
            </button>
          `
  
          attachmentList.appendChild(fileItem)
        }
  
        // Agregar event listeners para eliminar adjuntos
        document.querySelectorAll(".remove-attachment-btn").forEach((btn) => {
          btn.addEventListener("click", function () {
            const index = this.dataset.index
            // Aquí se eliminaría el adjunto
            this.closest(".attachment-item").remove()
          })
        })
      }
    }
  
    function formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes"
  
      const k = 1024
      const sizes = ["Bytes", "KB", "MB", "GB"]
      const i = Math.floor(Math.log(bytes) / Math.log(k))
  
      return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
    }
  
    function sendNewMessage(e) {
      e.preventDefault()
  
      const recipients = document.getElementById("message-recipients").value
      const subject = document.getElementById("message-subject").value
      const content = document.getElementById("message-content").value
  
      if (!recipients || !subject || !content) {
        showNotification("Por favor, completa todos los campos obligatorios", "error")
        return
      }
  
      // Aquí se enviaría el mensaje
      console.log("Enviando mensaje a:", recipients)
      console.log("Asunto:", subject)
      console.log("Contenido:", content)
  
      // Cerrar el modal
      closeAllModals()
  
      // Mostrar mensaje de éxito
      showNotification("Mensaje enviado correctamente", "success")
    }
  
    function sendComposedEmail(e) {
      if (e) e.preventDefault()

      const composeToField = document.getElementById("compose-to")
      const composeSubjectTypeField = document.getElementById("compose-subject-type")
      const composeSubjectField = document.getElementById("compose-subject")
      const composeMessageField = document.getElementById("compose-message")

      if (composeToField && composeSubjectTypeField && composeMessageField) {
        const to = composeToField.value
        const subjectType = composeSubjectTypeField.value
        const customSubject = composeSubjectField.value
        const message = composeMessageField.value

        // Determinar el asunto final
        let finalSubject = ""
        if (subjectType === "otro") {
          if (!customSubject) {
            showNotification("Por favor, escribe un asunto personalizado", "error")
            return
          }
          finalSubject = customSubject
        } else {
          // Usar el texto del option seleccionado
          const selectedOption = composeSubjectTypeField.options[composeSubjectTypeField.selectedIndex]
          finalSubject = selectedOption.text
        }

        if (!to || !subjectType || !message) {
          showNotification("Por favor, completa todos los campos obligatorios", "error")
          return
        }

        // Aquí se enviaría el mensaje
        console.log("Enviando mensaje a:", to)
        console.log("Tipo de asunto:", subjectType)
        console.log("Asunto final:", finalSubject)
        console.log("Mensaje:", message)

        // Cerrar el modal
        closeModal(composeModal)

        // Mostrar mensaje de éxito
        showNotification("Mensaje enviado correctamente", "success")
      }
    }
  
    function showNotification(message, type) {
      // Crear elemento de notificación
      const notification = document.createElement("div")
      notification.className = `notification ${type}`
      notification.innerHTML = `
        <span class="material-icons">${type === "success" ? "check_circle" : "error"}</span>
        <span>${message}</span>
      `
  
      // Agregar al DOM
      document.body.appendChild(notification)
  
      // Mostrar con animación
      setTimeout(() => {
        notification.classList.add("show")
      }, 10)
  
      // Eliminar después de 3 segundos
      setTimeout(() => {
        notification.classList.remove("show")
        setTimeout(() => {
          notification.remove()
        }, 300)
      }, 3000)
    }
  })
  
  