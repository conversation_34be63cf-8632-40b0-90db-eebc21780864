<?php
require_once __DIR__ . '/../config.php';

/**
 * Clase para manejar la conexión a la base de datos MySQL
 * Sistema Educativo Escuela NV
 */
class Conexion {
    private static $host = DB_HOST;
    private static $dbname = DB_NAME;
    private static $username = DB_USER;
    private static $password = DB_PASS;
    private static $charset = DB_CHARSET;
    private static $pdo = null;

    /**
     * Obtiene la conexión PDO a la base de datos
     * @return PDO
     */
    public static function getConexion() {
        if (self::$pdo === null) {
            try {
                $dsn = "mysql:host=" . self::$host . ";dbname=" . self::$dbname . ";charset=" . self::$charset;
                
                $opciones = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];

                self::$pdo = new PDO($dsn, self::$username, self::$password, $opciones);
                
            } catch (PDOException $e) {
                throw new PDOException("Error de conexión: " . $e->getMessage());
            }
        }
        
        return self::$pdo;
    }

    /**
     * Cierra la conexión a la base de datos
     */
    public static function cerrarConexion() {
        self::$pdo = null;
    }

    /**
     * Verifica si la conexión está activa
     * @return bool
     */
    public static function verificarConexion() {
        try {
            $pdo = self::getConexion();
            $pdo->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
}
?>
