/* Estilos para la página de Preinscripción */

/* Hero Section */
.preinscripcion-hero {
    background-color: var(--primary-color);
    background-image: url("../img/rosa.svg");
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
    color: var(--white);
    padding: 120px 0 60px;
    text-align: center;
    position: relative;
  }
  
  .preinscripcion-hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }
  
  .preinscripcion-hero .container {
    position: relative;
    z-index: 2;
  }
  
  .preinscripcion-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .preinscripcion-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  }
  
  /* Formulario Section */
  .formulario-section {
    padding: 5rem 0;
    background-color: #f9f9f9;
  }
  
  .formulario-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
  }
  
  /* Información lateral */
  .formulario-info {
    background-color: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    align-self: start;
    position: sticky;
    top: 120px;
  }
  
  .formulario-info h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
  
  .info-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: flex-start;
  }
  
  .info-item:last-child {
    margin-bottom: 0;
  }
  
  .info-item .material-icons {
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
  }
  
  .info-item p {
    margin: 0;
    color: #555;
    line-height: 1.5;
  }
  
  /* Formulario Card */
  .formulario-card {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .form-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 2rem;
    text-align: center;
  }
  
  .form-header h2 {
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
  }
  
  .form-header p {
    opacity: 0.8;
  }
  
  .preinscripcion-form {
    padding: 2rem;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
  }
  
  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: "Montserrat", sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s;
  }
  
  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.1);
  }
  
  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .checkbox-group input {
    width: auto;
  }
  
  .checkbox-group label {
    margin-bottom: 0;
  }
  
  .terminos-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
  }
  
  .terminos-link:hover {
    text-decoration: underline;
  }
  
  .form-actions {
    margin-top: 2rem;
    text-align: center;
  }
  
  .submit-btn {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .submit-btn:hover {
    background-color: #e63e00;
  }
  
  .submit-btn .material-icons {
    font-size: 1.3rem;
  }
  
  /* Modal de Términos */
  .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-contenido {
    background-color: white;
    border-radius: 12px;
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .modal-header h2 {
    margin: 0;
    color: var(--primary-color);
  }
  
  .cerrar-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }
  
  .cerrar-modal:hover {
    background-color: #f0f0f0;
    color: #333;
  }
  
  .modal-body {
    padding: 2rem;
  }
  
  .modal-body h3 {
    color: var(--secondary-color);
    margin-top: 1.5rem;
    margin-bottom: 0.8rem;
  }
  
  .modal-body p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #555;
  }
  
  .modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
  
  .aceptar-btn {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .aceptar-btn:hover {
    background-color: #e63e00;
  }
  
  /* Modal de Confirmación */
  .confirmacion-contenido {
    text-align: center;
    padding: 3rem;
    max-width: 500px;
  }
  
  .confirmacion-icon {
    margin-bottom: 1.5rem;
  }
  
  .confirmacion-icon .material-icons {
    font-size: 5rem;
    color: #4caf50;
  }
  
  .confirmacion-contenido h2 {
    color: #4caf50;
    margin-bottom: 1.5rem;
  }
  
  .confirmacion-contenido p {
    margin-bottom: 1rem;
    color: #555;
    line-height: 1.6;
  }
  
  .confirmacion-actions {
    margin-top: 2rem;
  }
  
  .cerrar-confirmacion-btn {
    background-color: #4caf50;
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 0.8rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .cerrar-confirmacion-btn:hover {
    background-color: #3e8e41;
  }
  
  /* Responsive Styles */
  @media (max-width: 768px) {
    .preinscripcion-hero {
      padding: 100px 0 40px;
    }
  
    .preinscripcion-hero h1 {
      font-size: 2rem;
    }
  
    .formulario-container {
      grid-template-columns: 1fr;
    }
  
    .formulario-info {
      position: static;
      margin-bottom: 2rem;
    }
  
    .modal-contenido {
      width: 95%;
    }
  
    .confirmacion-contenido {
      padding: 2rem;
    }
  }  