Cosas que hay en la web (Necesito que el sistema sepa cuando son los 4 bimestres)
Preinscripcion
Nombre del menor
Apellido paterno
Apellido Materno
DNi del estudiante
DNI del padre
Correo del apoderado
Telefono del apoderado
Grado Academico de Interes (Nivel Inicial: 3,4 o 5 años | Nivel primaria: 1, 2, 3 , 4, 5 y 6to de  primaria)

Esta información es para que cuando una personas que no a iniciado sesión dentro de la web, pero quiera mas información para poder ver si matricula a su menor, rellena el formulario que le pide dichos datos, y esos datos se envían al apartado del menú del administrador en  su respectiva sección de preinscripción. Una vez que el administrador termine de confirmar haber revisado esa solicitud, esa información se borra automáticamente, ya que no se utilizara.

Anuncios 
La tabla debe tener
Titulo del anuncio
Fecha de publicación (Ejem: 12 de febrero, 2023)
Descripción corta
Contenido
Imagen (opcional)
Administrador que lo publico

En este apartado los que no tengan cuenta o sean usuarios del rol de padres, podrán ver  los anuncios que publica la administración, así mismo tendrán un filtro para buscar los anuncios por títulos o por fecha de año
En Intranet (que es para que los usuarios inicien sesion)
Solo pide la información básica del  nombre de usuario y contraseña, si no se agrega le pedirá que es un requisito.

AHORA TE DIRE LOS DATOS QUE SALEN DEPENDIENDO DEL DASHBOARD ASIGNADOS

Si es estudiante
Tendrá en inicio donde le dan bienvenida con su primer nombre, y debajo le saldrán el resumen de sus actividad (el nro cursos que tiene actualmente, nro de asistencias y nro de faltas) y debajo sale los cursos que tiene ese día.

En la sección de perfil, se pueden visualizar sus datos como
Sale su nombre de perfil completo, su foto, que es estudiante (puede cambiar la foto)
INFORMACIÓN PERSONAL:
Nombre Completo
Fecha de Nacimiento
Edad
Sexo
Dirección
teléfono
INFORMACION DE  CUENTA
Nombre de usuario
Contraseña
Correo
INFORMACION ACADEMICA:
Grado actual
Tutor
Promedio General
Año escolar
asistencias
Contacto de emergencia (padre/apoderado)
Nombre del contacto
Telefono principal
Teléfono alternativo
Correo

En la sección de cursos, se puede visualizar el nombre del curso, el maestro encargado, el día y horario (ejem: 9:00-10:30), y la respectiva imagen.
Asi mismo en donde su visualizan los cursos se puede hacer búsqueda del curso por su nombre

Si selecciona un curso, salen los mismos datos anteriores del curso, pero se visualiza tmb un apartado en donde el maestra puede dejar  un link de zoom /meet para clases virtuales. De igual manera, debajo las clases están organizadas por carpetas llamadas sesiones, si se selecciona una sección, esta se despliega en tipo acordeón para ver el contenido que tiene (que puede ser tarea, anuncio, pdfs o materiales de descarga y links de videos o videos, y participación (se especifica mas en el apartado del maestro)). Si en el caso el estudiante escoge una tarea, le saldrá la info y a su vez un apartado donde podrá enviar un archivo de su desarrollo (Word, pdf, etc…) y colocar un comentario adicional antes de hacer la entegra. Una vez enviado su tarea, deberá esperar a que el maestro califique dicha tarea para que luego se visualize la nota en el cuadro donde representa donde estará la nota. En otra pestaña de cursos tendrá un apartado para ver las tareas pendientes y las que estarán próximas a aparecer, si selecciona la tarea en la que esta (digamos que en tareas pendiente sale que debe resolver un ejercicio de multiplicación de la pag 50, selecciona dicha tarea y lo reenvía a la ventana para ver dicha tarea), en otra pestaña del curso podrá ver la cantidad y los estudiantes que están en el curso, así mismo puede buscar en la barra de búsqueda el nombre de algún estudiante. En la misma pestaña habrá un apartado de calificaciones de dicho curso, en donde el estudiante podrá ver una tabla con las tareas ya calificadas (solo las que han sido calificadas, el resto no). Hay otro apartado en donde el estudiante puede ver sus asistencias, si falto, estuve presente, llego tarde o fue justificado (no incluir el porque). En otro apartado del curso, también tendrá para poder enviarle mensajes a la maestroa seleccionando el asunto del mensaje (consulta sobre examen, consulta sobre tarea, consulta sobre clase, u otro) y el mensaje que le quiere escribir, una vez que haya rellenado esos datos puede darle a enviar mensaje. Al lado de ese formulario para enviar las tareas, sale el historial de mensajes que fueron respondidos por el maestro, mostrando el asunto, la fecha en la que lo escribió, el mensaje y debajo la respuesta del maestro (su nombre de usuario), la fecha en la que respondió y su mensaje al respecto. Saliendo de cursos, en el menú esta la opción de notas, donde tendrá un apartado para ver el resumen de su promedio actual, su puesto en su grado y sus asistencias. Debajo sale la opción para cambiar los bimestres o ver el final que estar relacionados con la tabla del resumen de notas generales por cada curso que tiene el estudiante (las de primer bimestre, segundo brimeste, tercer bimestre, cuarto bimestre y el promedio final de todas). Y si selecciona, pongamos de ejemplo, el primer bimestre sale en una tabla, en una columna estará dedicada a tener el promedio por las notas, en otra columna será por exámenes, y otro por participación del estudiante, y finalmente la otra columna para el promedio del estudiante. De esta tabla debajo hay un apartado donde se generan gráficos tomados de la información del promedio de cada curso por bimestre (ósea que si cambio el bimestre, el grafico tomara los datos del respectivo bimestre para cambiar el tamaño del grafico de barras).  Siguiendo en el menú estará la opción de tareas en donde el estudiante podrá ver sus tareas de manera general. Estara el cuadro de tareas que a pendientes, tareas completadas y tareas atrasadas. Debajo habrá un filtro para escoger que categoría de las tareas, y así mismo cuando le salgan las tareas podrá ver el titulo de la tarea, su descripción y de que curso es, junto con su fecha de vencimiento y nota (si no esta calificado aun se mostrara “- -“). De igual modo, el estudiante puede seleccionar dicha tarea que lo rediriga para verla y poder realizarla y enviarla. Finalmente en el menú estará el apartado para cerrar sesión.

Para el apartado del maestro tenemos en el menú el inicio donde el maestro podrá ver el nombre del prof. ---- y vera cuantos mensajes tiene sin leer y cuantas tareas le faltan calificar. Debajo estarán unos cuadros con el resumen de los estudiantes que tiene en total por los cursos, las horas que ha estado trabajando. Y la cantidad de tareas que ha calificado. Debajo similar al estudiante, tendrá un apartado para ver el horario que tiene ese día de los cursos que le toca enseñar. Debajo otro apartado donde le mostrara que tareas aun debe calificar(mostrando el titulo de la tarea, el grado y la entrega max. Antes de que se venza). Y que le pueda redirigirr a la tarea para ir calificándolas.
En el menú de peril saldrá su nombre completo 
Su foto (puede cambiarlo)
Maestro de que curso dicta (y de que salón es encargado)
En información personal sale
Nombre Completo
Fecha de nacimiento
Edad
Sexo
Dirección
Telefono
En información de cuenta
Nombre de usuario
Correo electrónico
Contraseña
En información profesional
Su especialidad (curso que dicta)
Nivel educativo
(Inicial o primaria)
Fecha de contratación
Contacto de emergencia
Nombre del contacto
Telefono principal
Telefono alternativo
Correo
En el menú hay otra opción de Mis Cursos
En esa pag hay una opción para buscar los cursos en la barra de búsqueda, además habrá un combobox para ver por los grados. Desde inicial hasta 6to de primaria) Y un botón para crear cursos. Si selecciona el botón puede Colocar nombre del curso, el grado designado, el horario (Ejem: Lunes  1 pm -1:30 pm, – Martes 10:00 am – 11:00 am). El icono del curso con un combobox. Y colocar la imagen que el maestro desee de manera opcional. Puede editar estas opciones luego o borrar el curso cuando lo cree. Tambien, una vez creado el curso, puede ingresar y me presentan con el nombre del curso en la parte superior junto con el grado y al lado el horario que toca con la hora, debajo del submenú que por defecto inicia con contenido hay para ver el contenido del curso y decidir si colocar un enlace de zoom para videollamdas (opcional). Si lo coloca le pedirá colocar el Titulo, la fecha y la url. 
Debajo estarán las carpetas (acordeones) donde se mostraran las sesiones del curso, pero además habran un boton para crear las carpetas de las sesiones (titulo, fecha de inicio, fecha de fin, descripcion). Dentro de ellas estará la opcion para crear contenido en las carpetas, que te pedirá seleccionar tipo: Anuncio (Titulo, descripción e imagen opcional), Tarea (Titulo, Descripcion e imagen opcional, Fecha limite, Hora límite, Puntos), Examen (Titulo, Descripcion e imagen opcional, Fecha limite, hora  limite, puntos), Documento (Titulo, Descripcion, Archivo), Presentación(ppt; (Titulo, Descripcion, Archivo)), enlace (Titulo, Descripcion, url), video (Titulo, Descripcion, Archivo o URL) o participación (nombre, lista de todos  los  estudiantes y su N° de nota de participación). Esto archivos seran para que lo vean los estudiantes y si los selecciona el maestro los puede ver también o editar el maestro, los archivos de tareas al lado de las opciones de editar o borrar saldrá como pendientes debido a los que aun no califique el maestro. 
Hay otra subpestaña en cursos llamada tareas en donde el maestro podrá ver las tareas y examenes, una barra de búsqueda para buscar la semana o la tarea. Debajo estarán las tareas y examenes, si quiere ver una tarea o examen, puede darle click en el botón de ver tarea/ver examen (dependiendo del archivo que seleccione) o de ver entregas. Si selecciona en ver entregas puede ver el resumen de la tarea/examen viendo que estudiantes ya salen como envíos entregadas, las tardías, las pendientes, y las calificadas. Igual debajo hay un combobox con esta categorías para filtrarlas o puede buscar al estudiante por la barra de búsqueda. Los resultados se verán debajo en donde saldrá por estudiante El nombre del estudiante y su grado, la foto de perfil si la tiene, la categoría, la fecha y hora de entrega, si sigue pendiente saldrá la fecha limite en la que se dejo la tarea/examen, la calificación si en caso se califico, en caso contrario saldrá como sin calificar y un botón para ver mas a detalle la entrega. En la parte superior saldrán esos datos previos del estudiante y podremos ver el archivo o texto que vio, descargarlo y visualizarlo. Su comentario, y podremos calificar la tarea/examen con los puntos y nuestro comentario de retroalimentación, le damos a guardar. Ahora, el otro apartado de tareas/examenes es para poder crear tareas y examanes desde ese espacio. Podremos buscar tareas o exámenes, buscar el titulo de la tarea/examen o crear una tarea (o examen). Colocando los datos de titulo, descripción e imagen opcional, la fecha y hora limite, los puntos y la sesión donde se suba. Asi mismo debajo tendremos una tabla crud donde veremos por titulo, tipo (examen, tarea), semana, fecha, hora, puntos, entregas y las acciones (para editar, ver o borrar). En el apartado de estudiantes, se podrá ver una barra para buscar a los estudiantes de dicho curso, para ver debajo la lista de estudiantes, pero también para agregar a los estudiantes a x curso. En calificaciones podremos ver una barra de búsqueda para buscar a los estudiantes para ver sus notas (las primeras 3 notas sean publicas ya que se verá mal si hay mas tareas), al lado estará el promedio de sus notas, y las acciones (de editar o ver), si seleccióno alguna de ellas podre ver a mas detalle cada tarea, y si es que le di a editar puedo modificar la tarea y debajo de ese resumen de notas colocar un comentario para que el padre lo vea. Igual puedo exportar todas las calificaciones en Excel luego. En otro apartado llamado asistencias puedo colocar las asistencias de los estudiantes por el día, Me saldrá una tabla con cada nombre, grado y foto del estudiante, y el estado (Presente, Faltó, Tardanzo, Justificado) y guardar la asistencia, lo que se guarde debajo saldrá un resumen de la cantidad de estudiantes por cada categoría seleccionado, grabo la asistencia o se puede reiniciar, y debajo se muestra el historial de las anteriores asistencias por si un estudiante tiene justificación otro día o si hubo confusiones con la asistencia. En el otro opcion del submenú esta el apartado de mensajes donde estara para ver la bandeja de entrega de dudas de los estudiantes, se puede buscar mensajes, se puede ver en bandeja de entrega los nombres de los estudiantes, junto con su titulo de la duda y descripción. Y si selecciono x duda puede ver su duda, El nombre del estudiante, el correo, la foto, la fecha y hora del mensaje, la descripción y la opcion de responder a su mensaje, adjuntar un archivo si es necesario y enviar. Saliendo de cursos En el menú principal también tenemos las asistencias del maestro donde salga la hora actual y al lado hay 2 botones para grabar la hora de entrada y el registro de salida (solo se activa si  el primero fue activo y vicesversa), debajo sale un resumen de días asistidos, faltas, tardanzas, salidas anticipadas,  y el registro de las asistencias. 
Fecha, día hora de ingreso y salida, el estado y al lado las  observaciones (si es por tardanza, falta o salidas). Si quiere enviar justificación de salidas, falta o tardanza, envia una solicitud seleccionando la asistencia a la cual justificara y una descripción, y luego tiene que esperar  para ver si esta pendiente, rechazada o aprobada. En apartado de menú tmb hay mensajes para los podres donde podrá buscar el nombre del padre, al lado saldrá los mensajes de las fotos de los padres, nombre + padre de que estudiante y descripción del mensaje, si se  selecciona uno, saldrá  estos datos, los mensajes y la hora tanto de lo que escriba el maestro como el padre o madre, y debajo un apartado para enviar un archivo o un mensaje y enviarlo. Finalmente el apartado para cerrar sesión.

En padres saldrá el inicio con el nombre del padre, y debajo pueda escoger con un combobox a los estudiantes a los que este ligado. Si selecciona uno de los estudiantes vera  su nombre, foto y grado con un Promedio general, tareas completadas, asistencias, y si esta al día en pagos. Dejabo puede ver los últimos nuevos 3 anuncios publicados (titulo, descripción, fecha y quien lo subio), y debajo las tareas pendientes que tiene su hijo (titulo, descripción, curso y fecha limite). En el menú también hay para ver  su perfil, foto, nombre completo, padre de familia, Apoderado de x estudiante. En información de cuenta pide
Nombre Completo
Fecha de nacimiento
Edad
Sexo
Dirección
Teléfono
Informacion de cuenta: Nombre de usuario, correo y contraseña
Informacion del hijo (por cada hijo): Nombre del hijo, Grado actual, año escolar, Maestro tutor y la fecha de nacimiento
Contacto de emergencia: Nombre del contacto, teléfono principal, teléfono alternativo, correo.
Continuando con el menú hay un apartado de anuncios en donde el padre puede ver los anuncios que publica la escuela desde la barra de búsqueda, hasta un filtro con los de hoy, esta semana, mes y año. De manera previa puede ver la imagen, titulo, una breve descripción y año, pero si selecciona el anuncio ve el autor, la foto, el año, el titulo, la imagen, la descripción completa. 
En el menú puede ver las calificaciones puede seleccionar a su estudiante para ver por cada uno sus calificaciones, debajo vera un resumen de ese hijo (promedio, tareas completadas, asistencia y posición en la clase)
Debajo vera un resumen de sus notas con un filtro por bimestre con cada curso, exámenes, tareas, participación, asistencias y calificación final (las asistencias no cuentan en este apartado) al lado hay 2 botones para descargar la boleta o ver un resumen de las tareas, donde saldrá el nombre del estudiante, grado, el periodo (bimestre) y la tabla con las notas de los cursos por calificación general y observaciones, debajo de este cuadro sale sus asistencias de todos los días que ha habido asistencias, los días que asistió, las faltas, las tardanzas y el porcentaje de asistencias. Debajo saldrá su registro de asistencia, mostrando los días que asistió, los que falto, las tardanzas y el porcentaje, al lado habrá un filtrado por mes para ver debajo los resultados que una vez filtrados por mes se organizaran  para ver por esas categorías junto con la fecha. Finalmente un apartado donde se vean los comentarios de manera general de cada docente por sus notas
En chats similar ha de los maestros con los padres, con la excepción de que ahora todo esta filtrado por cada maestro desde la perspectiva del padre, al igual que puede filtrarlo desde el estudiante el cual seleccione. La opcion de control de pagos (No se tocara hasta que el cliente acepte el resto). Finalemente el botón de cerrar sesión. 
Para finalizar el apartado del administrador en donde en el inicio saldrá el resumen de la cantidad de personas dentro del sistema registradas, nro de estudiantes, nro de maestros, y las solicitudes pendientes (de preinscripcion) . Debajo habrá un apartado para ver los nuevos movimientos de manera general de todo el sistema de todos los usuarios, junto con los Anuncios pendientes a publicar, y los usuarios creados recientemente. En perfil sale su nombre, foto, administrador del sistema, debajo la información general: Nombre completo, fecha de nacimiento, edad, sexo, dirección, teléfono. Informacion de cuenta: nombre de usuario, correo, contraseña. Informacion profesional: Cargo, departamento, fecha de contratación. Contacto de emergencia; nombre completo, teléfono principal, teléfono alternativo, correo. 
En el apartado de usuarios que esta en el menú, puede gestionar la creación de usuarios, puede con la barra de búsqueda buscar a los usuarios, filtrar por el tipo de usuario (Administrador, Padre, Estudiante o Maestro). Tiene un botón para crear a los usuarios, pidiendo en info el nombre, apellido, correo, teléfono, dirección. Junto con fecha de nacimiento y sexo. En cuenta le pide el nombre de usuario, contraseña, contraseña para confirmar, finalmente el rol del usuario (si es estudiante puede guardar, si es maestro, le dira que especialidad (Matematicas, Ciencia y Tecnologia, Comunicación, Ingles, Arte, Personal Social y Educacion Física) le asignara un grado de tutor). Si es padre primero deberá crear un estudiantes, ya que se mostrara una lista de estudiantes a los que se seleccionara para vincularlos, y el tipo (si es padre, madre o tutor legal), el tipo administrador también puede guardar normal. El resto si no realiza los demás no pueden continuar con la creación del usuario. Una vez creador debajo sale la tabla de resumen de todos los usuarios (Nombre del usuario, Rol, correo, fecha de registro y acciones). En las acciones si es estudiante podrá su nombre, grado, perfil de foto, rol y ver el resumen de sus notas mostrando el curso, maestro, los promedios por bimestre y final y el resumen de asistencias mostrando el porcentaje de asistencias, faltas y tardanzas. Si es maestro se muestra su nombre, foto de perfil, rol y grado seleccionado y vera los estados de sus cursos, vera todos los cursos por cada grado, las asistencias que tiene en esos cursos, y si ya se completo las notas (si en caso en el sistema aun hay pendientes saldrá como pendiente, en caso el sistema vea que ya se culminaron las notas saldrá como completado), igualmente debajo estará el resumen de asistencia mostrando el porcentaje de asistencia y días de falto o tuvo tardanza. Igualmente en la vista debería haber un boton para modificar asistencias, en donde puedan ver las solicitudes de justificación y aceptar o negarlas por dichos motivos y así cambiar el estado de asistencia del maestro. Si es padre de familia saldrá su nombre, foto, tipo de apoderado, rol y luego se verá su apartado para ver si pagado la matricula y  pensiones por mes, el monto (se suma si tiene  mas de un menor), la fecha limite y si salen el estado de si ya se ha pagado, es pendiente o vencido (también un apartado para editar si ya pago de manera externa), debajo sale de que estudiantes esta relacionado con nombre, foto y grado. Si es administrador, se vera sus nombres, foto, rol y las  actividades de modificaciones recientes en la web. Tambien como otra acción puede eliminar usuarios.  En el menú también esta el apartado de anuncios en donde puede buscar los títulos de los anuncios, los estados, si es publico o borrador. Y debajo ver la tabla del titulo con foto del anuncio y breve descripción, la fecha y el estado (y las acciones de ver, editar o modificar). Similar al menú principal de web, si le da ver  puede chequear la fecha, titulo, imagen del anuncio, la descripción. Si le da al boton de crear anuncio o editar le saldrá el apartado para colocar el titulo del anuncio, la descripción corta, y la descripción general donde puede colocar texto e imagen y debajo el apartado para subir la imagen principal del anuncio y finalmente el estado para guardar como borrador o publicar. Debajo tiene 3 botones para cancelar el anuncio, ver una vista previa del anuncio o guardar anuncio (dependiendo del estado que le coloco). En el menú de solicitudes puede ver las preinscripciones de personas que quieren matricularse. Puede buscar en la barra de búsqueda. O usar los filtros para buscar por el estado de inicial o primaria. 
Saldrá el nro de solicitud, el nombre, apellido paterno, apellido materno, dni del estudiante, dni del padre, correo electrónico, teléfono y grado de interés (inicial 3,4, 5 años o 1,2,3,4,5,6 de primaria). Puede ver las solicitudes, Y si ya las reviso las marca con un Check para que se eliminen automáticamente del sistema. Finalmente tiene un boton para cerrar sesión.
