document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const studentSelect = document.getElementById("student-select")
    const busquedaTitulo = document.getElementById("busqueda-titulo")
    const filtroFecha = document.getElementById("filtro-fecha")
    const anunciosGrid = document.getElementById("anuncios-grid")
    const noResultados = document.getElementById("no-resultados")
    const anuncioModal = document.getElementById("anuncio-modal")
    const modalCloseBtn = document.querySelector(".modal-close-btn")
    const modalTitulo = document.getElementById("modal-titulo")
    const modalFecha = document.getElementById("modal-fecha")
    const modalImagen = document.getElementById("modal-imagen")
    const modalContenido = document.getElementById("modal-contenido-texto")
    const paginacionBtns = document.querySelectorAll(".paginacion-btn")
  
    // Datos de ejemplo para los anuncios (en una implementación real, estos datos vendrían del servidor)
    const anuncios = [
      {
        id: 1,
        titulo: "Reunión de Padres de Familia - 2do Bimestre",
        extracto:
          "Estimados padres, les informamos que la reunión para la entrega de libretas del 2do bimestre se realizará el día viernes 28 de marzo.",
        contenido: `<p>Estimados padres de familia:</p>
                  <p>Nos complace invitarlos a la reunión de entrega de libretas correspondiente al 2do bimestre del año escolar 2025, que se llevará a cabo el día <strong>viernes 28 de marzo</strong> en nuestras instalaciones.</p>
                  <p>Durante esta reunión, tendrán la oportunidad de conversar con los docentes sobre el desempeño académico y conductual de sus hijos, así como recibir recomendaciones para continuar apoyando su proceso de aprendizaje.</p>
                  <p><strong>Horarios por grado:</strong></p>
                  <ul>
                      <li>1° y 2° grado: 8:00 am - 9:30 am</li>
                      <li>3° y 4° grado: 10:00 am - 11:30 am</li>
                      <li>5° y 6° grado: 12:00 pm - 1:30 pm</li>
                  </ul>
                  <p>Les recordamos que es importante asistir puntualmente en el horario asignado para su grado. En caso de no poder asistir, deberán programar una cita con el tutor en los días posteriores.</p>
                  <p>Agradecemos su compromiso con la educación de sus hijos y esperamos contar con su valiosa presencia.</p>
                  <p>Atentamente,<br>La Dirección</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-20",
        fechaTexto: "20/03/2025",
        autor: "Administración Escolar",
        cargo: "Comunicaciones",
      },
      {
        id: 2,
        titulo: "Feria de Ciencias 2025",
        extracto:
          "Invitamos a todos los estudiantes a participar en nuestra feria anual de ciencias con proyectos innovadores que se realizará el 15 de abril.",
        contenido: `<p>¡Llega nuestra esperada Feria de Ciencias 2025!</p>
                  <p>Invitamos a todos nuestros estudiantes a participar en este importante evento académico que se realizará el 15 de abril en nuestras instalaciones. La feria es una excelente oportunidad para que los alumnos demuestren su creatividad, pensamiento crítico y habilidades de investigación.</p>
                  <p><strong>Categorías de participación:</strong></p>
                  <ul>
                      <li>Ciencias Naturales</li>
                      <li>Tecnología e Innovación</li>
                      <li>Medio Ambiente y Sostenibilidad</li>
                      <li>Ciencias Sociales</li>
                  </ul>
                  <p><strong>Fechas importantes:</strong></p>
                  <ul>
                      <li>Inscripción de proyectos: Del 25 al 31 de marzo</li>
                      <li>Entrega de resúmenes: 5 de abril</li>
                      <li>Montaje de stands: 14 de abril</li>
                      <li>Presentación y evaluación: 15 de abril</li>
                  </ul>
                  <p>Los proyectos ganadores representarán a nuestra institución en la Feria Regional de Ciencias. Habrá premios especiales para los primeros lugares de cada categoría.</p>
                  <p>Para más información y asesoría, los estudiantes pueden acercarse al Departamento de Ciencias.</p>
                  <p>¡Esperamos contar con la participación entusiasta de todos nuestros estudiantes!</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-18",
        fechaTexto: "18/03/2025",
        autor: "Departamento de Ciencias",
        cargo: "Coordinación Académica",
      },
      {
        id: 3,
        titulo: "Taller de Lectura para Padres e Hijos",
        extracto:
          "Les invitamos a participar en nuestro taller de lectura para padres e hijos que se realizará todos los sábados de abril de 9:00 a 11:00 am.",
        contenido: `<p>Estimadas familias:</p>
                  <p>Nos complace invitarlos a participar en nuestro Taller de Lectura para Padres e Hijos, una iniciativa que busca fortalecer los vínculos familiares a través del amor por la lectura.</p>
                  <p><strong>Detalles del taller:</strong></p>
                  <ul>
                      <li>Fechas: Todos los sábados de abril</li>
                      <li>Horario: 9:00 a 11:00 am</li>
                      <li>Lugar: Biblioteca escolar</li>
                      <li>Dirigido a: Padres e hijos de todos los grados</li>
                  </ul>
                  <p>Durante estas sesiones, realizaremos actividades de lectura compartida, cuentacuentos, y técnicas para fomentar el hábito de la lectura en casa.</p>
                  <p>¡Los esperamos para vivir juntos esta hermosa experiencia literaria!</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-15",
        fechaTexto: "15/03/2025",
        autor: "Departamento de Lengua",
        cargo: "Coordinación Académica",
      },
      {
        id: 4,
        titulo: "Actualización de Datos Familiares",
        extracto:
          "Solicitamos a todos los padres de familia actualizar sus datos de contacto y dirección a través de la plataforma antes del 31 de marzo.",
        contenido: `<p>Estimados padres de familia:</p>
                  <p>Como parte de nuestro compromiso con mantener una comunicación efectiva, solicitamos actualizar sus datos de contacto en nuestro sistema.</p>
                  <p><strong>Datos a verificar:</strong></p>
                  <ul>
                      <li>Números de teléfono (principal y alternativo)</li>
                      <li>Dirección de residencia actual</li>
                      <li>Correo electrónico</li>
                      <li>Contacto de emergencia</li>
                  </ul>
                  <p><strong>Fecha límite:</strong> 31 de marzo de 2025</p>
                  <p>Pueden realizar la actualización a través de la plataforma educativa o acercándose a secretaría en horario de atención.</p>
                  <p>Agradecemos su colaboración.</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-10",
        fechaTexto: "10/03/2025",
        autor: "Secretaría Académica",
        cargo: "Administración",
      },
      {
        id: 5,
        titulo: "Olimpiadas Matemáticas 2025",
        extracto:
          "Nos complace anunciar que nuestra institución participará en las Olimpiadas Matemáticas Nacionales que se realizarán en mayo.",
        contenido: `<p>¡Excelente noticia para nuestros estudiantes!</p>
                  <p>Nuestra institución ha sido seleccionada para participar en las Olimpiadas Matemáticas Nacionales 2025, que se realizarán en el mes de mayo.</p>
                  <p><strong>Proceso de selección:</strong></p>
                  <ul>
                      <li>Evaluación interna: 15 de abril</li>
                      <li>Selección de representantes: 20 de abril</li>
                      <li>Entrenamientos especiales: Del 25 de abril al 10 de mayo</li>
                      <li>Competencia nacional: 15-17 de mayo</li>
                  </ul>
                  <p>Invitamos a todos los estudiantes con aptitudes matemáticas a participar en la evaluación interna.</p>
                  <p>¡Representemos con orgullo a nuestra institución!</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-05",
        fechaTexto: "05/03/2025",
        autor: "Departamento de Matemáticas",
        cargo: "Coordinación Académica",
      },
      {
        id: 6,
        titulo: "Campaña de Vacunación Escolar",
        extracto:
          "Informamos que el día 10 de abril se realizará una campaña de vacunación escolar en coordinación con el Ministerio de Salud para todos los estudiantes.",
        contenido: `<p>Estimados padres de familia:</p>
                  <p>En coordinación con el Ministerio de Salud, informamos que se realizará una campaña de vacunación escolar el día 10 de abril en nuestras instalaciones.</p>
                  <p><strong>Detalles importantes:</strong></p>
                  <ul>
                      <li>Fecha: 10 de abril de 2025</li>
                      <li>Horario: 8:00 am a 3:00 pm</li>
                      <li>Lugar: Patio principal de la institución</li>
                      <li>Dirigido a: Todos los estudiantes</li>
                  </ul>
                  <p><strong>Vacunas a aplicar:</strong></p>
                  <ul>
                      <li>Refuerzo contra la influenza</li>
                      <li>Vacuna contra el VPH (estudiantes de 5° y 6° grado)</li>
                  </ul>
                  <p>Es importante que envíen el consentimiento informado firmado antes del 8 de abril.</p>
                  <p>Cualquier consulta, pueden comunicarse con la enfermería escolar.</p>`,
        imagen: "/placeholder.svg?height=400&width=800",
        fecha: "2025-03-01",
        fechaTexto: "01/03/2025",
        autor: "Departamento de Salud",
        cargo: "Bienestar Estudiantil",
      }
    ]

    // Variable para almacenar todos los anuncios originales
    let anunciosOriginales = [...anuncios]
    let anunciosFiltrados = [...anuncios]
  
    // Cambiar estudiante seleccionado
    if (studentSelect) {
      studentSelect.addEventListener("change", () => {
        // Aquí iría la lógica para cargar los anuncios del estudiante seleccionado
        const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
        console.log(`Estudiante seleccionado: ${selectedStudent}`)
      })
    }
  
    // Función para obtener el rango de fechas según la selección
    const obtenerRangoFechas = (filtro) => {
      const hoy = new Date()
      const inicioSemana = new Date(hoy)
      inicioSemana.setDate(hoy.getDate() - hoy.getDay())

      const inicioMes = new Date(hoy.getFullYear(), hoy.getMonth(), 1)
      const inicioBimestre = new Date(hoy.getFullYear(), Math.floor(hoy.getMonth() / 2) * 2, 1)

      switch (filtro) {
        case 'hoy':
          return {
            inicio: new Date(hoy.getFullYear(), hoy.getMonth(), hoy.getDate()),
            fin: new Date(hoy.getFullYear(), hoy.getMonth(), hoy.getDate() + 1)
          }
        case 'semana':
          return {
            inicio: inicioSemana,
            fin: new Date(inicioSemana.getTime() + 7 * 24 * 60 * 60 * 1000)
          }
        case 'mes':
          return {
            inicio: inicioMes,
            fin: new Date(hoy.getFullYear(), hoy.getMonth() + 1, 1)
          }
        case 'bimestre':
          return {
            inicio: inicioBimestre,
            fin: new Date(hoy.getFullYear(), Math.floor(hoy.getMonth() / 2) * 2 + 2, 1)
          }
        default:
          return null
      }
    }

    // Función para renderizar los anuncios filtrados
    const renderizarAnuncios = (anunciosParaMostrar) => {
      if (anunciosParaMostrar.length === 0) {
        anunciosGrid.style.display = "none"
        noResultados.style.display = "block"
        return
      }

      anunciosGrid.style.display = "grid"
      noResultados.style.display = "none"

      // Limpiar el grid actual
      anunciosGrid.innerHTML = ""

      // Crear las tarjetas de anuncios
      anunciosParaMostrar.forEach(anuncio => {
        const anuncioCard = document.createElement('div')
        anuncioCard.className = 'anuncio-card'
        anuncioCard.innerHTML = `
          <div class="anuncio-imagen">
            <img src="${anuncio.imagen}" alt="${anuncio.titulo}">
          </div>
          <div class="anuncio-contenido">
            <div class="anuncio-meta">
              <span class="anuncio-fecha">${anuncio.fechaTexto}</span>
            </div>
            <h2 class="anuncio-titulo">${anuncio.titulo}</h2>
            <p class="anuncio-extracto">${anuncio.extracto}</p>
            <a class="anuncio-link" data-id="${anuncio.id}">
              Leer más <span class="material-icons">arrow_forward</span>
            </a>
          </div>
        `
        anunciosGrid.appendChild(anuncioCard)
      })

      // Reactivar los event listeners para los nuevos enlaces
      const nuevosLinks = anunciosGrid.querySelectorAll('.anuncio-link')
      nuevosLinks.forEach(link => {
        link.addEventListener('click', () => {
          const anuncioId = Number.parseInt(link.getAttribute('data-id'))
          abrirModal(anuncioId)
        })
      })
    }

    // Filtrar anuncios mejorado
    const filtrarAnuncios = () => {
      const textoBusqueda = busquedaTitulo.value.toLowerCase().trim()
      const fechaSeleccionada = filtroFecha.value

      console.log(`Filtros aplicados: Texto="${textoBusqueda}", Fecha=${fechaSeleccionada}`)

      // Mostrar indicador de carga
      anunciosGrid.style.opacity = '0.5'

      let anunciosFiltrados = [...anunciosOriginales]

      // Filtrar por texto de búsqueda
      if (textoBusqueda) {
        anunciosFiltrados = anunciosFiltrados.filter(anuncio =>
          anuncio.titulo.toLowerCase().includes(textoBusqueda) ||
          anuncio.extracto.toLowerCase().includes(textoBusqueda) ||
          anuncio.autor.toLowerCase().includes(textoBusqueda)
        )
      }

      // Filtrar por fecha
      if (fechaSeleccionada !== 'todos') {
        const rangoFechas = obtenerRangoFechas(fechaSeleccionada)
        if (rangoFechas) {
          anunciosFiltrados = anunciosFiltrados.filter(anuncio => {
            const fechaAnuncio = new Date(anuncio.fecha)
            return fechaAnuncio >= rangoFechas.inicio && fechaAnuncio < rangoFechas.fin
          })
        }
      }

      // Simular un pequeño delay para mostrar el efecto de carga
      setTimeout(() => {
        // Renderizar los anuncios filtrados
        renderizarAnuncios(anunciosFiltrados)
        // Restaurar opacidad
        anunciosGrid.style.opacity = '1'
      }, 100)
    }
  
    // Variable para el timeout de búsqueda
    let timeoutBusqueda = null

    // Eventos para los filtros
    if (busquedaTitulo) {
      const inputGroup = busquedaTitulo.closest('.input-group')

      busquedaTitulo.addEventListener("input", () => {
        // Agregar clase visual de búsqueda
        if (inputGroup) {
          inputGroup.classList.add('searching')
        }

        // Limpiar el timeout anterior
        if (timeoutBusqueda) {
          clearTimeout(timeoutBusqueda)
        }

        // Establecer un nuevo timeout para evitar búsquedas excesivas
        timeoutBusqueda = setTimeout(() => {
          filtrarAnuncios()
          // Remover clase visual después del filtrado
          if (inputGroup) {
            setTimeout(() => {
              inputGroup.classList.remove('searching')
            }, 200)
          }
        }, 300) // Esperar 300ms después de que el usuario deje de escribir
      })
    }

    if (filtroFecha) {
      filtroFecha.addEventListener("change", filtrarAnuncios)
    }
  
    // Abrir modal de anuncio
    const abrirModal = (anuncioId) => {
      // Buscar el anuncio en los datos originales
      const anuncio = anunciosOriginales.find((a) => a.id === anuncioId)

      if (anuncio) {
        modalTitulo.textContent = anuncio.titulo
        modalFecha.textContent = anuncio.fechaTexto
        modalImagen.src = anuncio.imagen
        modalImagen.alt = anuncio.titulo
        modalContenido.innerHTML = anuncio.contenido

        // Actualizar información del autor
        const autorNombre = document.querySelector('.autor-nombre')
        const autorCargo = document.querySelector('.autor-cargo')
        if (autorNombre) autorNombre.textContent = anuncio.autor
        if (autorCargo) autorCargo.textContent = anuncio.cargo

        // Mostrar el modal
        anuncioModal.classList.add("active")
        document.body.style.overflow = "hidden" // Evitar scroll en el body
      }
    }
  
    // Inicializar la página con todos los anuncios
    const inicializarPagina = () => {
      renderizarAnuncios(anunciosOriginales)
    }

    // Eventos para abrir modal al hacer clic en "Leer más" (se manejan dinámicamente en renderizarAnuncios)
  
    // Cerrar modal
    if (modalCloseBtn) {
      modalCloseBtn.addEventListener("click", () => {
        anuncioModal.classList.remove("active")
        document.body.style.overflow = "" // Restaurar scroll en el body
      })
    }
  
    // Cerrar modal al hacer clic fuera del contenido
    if (anuncioModal) {
      anuncioModal.addEventListener("click", (e) => {
        if (e.target === anuncioModal) {
          anuncioModal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
      })
    }
  
    // Paginación
    if (paginacionBtns.length > 0) {
      paginacionBtns.forEach((btn) => {
        if (btn.classList.contains("paginacion-numero")) {
          btn.addEventListener("click", () => {
            // Remover clase active de todos los botones
            paginacionBtns.forEach((b) => b.classList.remove("active"))
  
            // Agregar clase active al botón seleccionado
            btn.classList.add("active")
  
            // En una implementación real, aquí se cargarían los anuncios de la página seleccionada
            console.log(`Página seleccionada: ${btn.textContent}`)
          })
        }
      })
    }
  
    // Actualizar fecha actual
    const dateElements = document.querySelectorAll(".current-date")
    if (dateElements.length > 0) {
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      const today = new Date()
      dateElements.forEach((element) => {
        element.textContent = today.toLocaleDateString("es-ES", options)
      })
    }
  
    // Compartir anuncio (simulado)
    const compartirBtn = document.querySelector(".compartir-btn")
    if (compartirBtn) {
      compartirBtn.addEventListener("click", () => {
        alert("Funcionalidad de compartir: Esta acción compartiría el anuncio por correo electrónico o redes sociales.")
      })
    }
  
    // Descargar anuncio (simulado)
    const descargarBtn = document.querySelector(".descargar-btn")
    if (descargarBtn) {
      descargarBtn.addEventListener("click", () => {
        alert("Funcionalidad de descarga: Esta acción descargaría el anuncio como PDF.")
      })
    }

    // Inicializar la página
    inicializarPagina()
  })
  
  