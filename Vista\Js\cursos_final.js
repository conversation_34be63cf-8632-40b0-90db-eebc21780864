// VERSIÓN FINAL - SISTEMA DE CURSOS SIN IMÁGENES
document.addEventListener("DOMContentLoaded", () => {
    console.log('=== SISTEMA DE CURSOS FINAL INICIADO ===')
    
    // Elementos del DOM
    const courseModal = document.getElementById("course-modal")
    const courseForm = document.getElementById("course-form")
    const courseIdInput = document.getElementById("course-id")
    const courseNameInput = document.getElementById("course-name")
    const courseGradeInput = document.getElementById("course-grade")
    const courseIconInput = document.getElementById("course-icon")
    const modalTitle = document.getElementById("modal-title")
    const createCourseBtn = document.getElementById("create-course-btn")
    const createFirstCourseBtn = document.getElementById("create-first-course-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const scheduleDayCheckboxes = document.querySelectorAll('input[name="schedule-day"]')
    
    // Verificar elementos críticos
    const elementosCriticos = {
        modal: courseModal,
        form: courseForm,
        idInput: courseIdInput,
        nameInput: courseNameInput,
        gradeInput: courseGradeInput,
        iconInput: courseIconInput,
        title: modalTitle
    }
    
    console.log('Estado de elementos críticos:')
    Object.entries(elementosCriticos).forEach(([nombre, elemento]) => {
        console.log(`- ${nombre}: ${elemento ? '✓' : '✗'}`)
    })

    // Función de notificación
    function notificar(mensaje, tipo = 'info') {
        console.log(`${tipo.toUpperCase()}: ${mensaje}`)
        // Usar alert temporalmente para debugging
        if (tipo === 'error') {
            alert(`ERROR: ${mensaje}`)
        } else if (tipo === 'success') {
            alert(`ÉXITO: ${mensaje}`)
        }
    }

    // Cargar datos del curso
    async function cargarDatosCurso(cursoId) {
        try {
            console.log(`Cargando datos del curso ID: ${cursoId}`)
            
            const response = await fetch(`../api_cursos.php?action=obtener_curso&id=${cursoId}`)
            
            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`)
            }
            
            const data = await response.json()
            console.log('Respuesta de la API:', data)
            
            if (data.success && data.curso) {
                const curso = data.curso
                
                // Llenar formulario
                if (courseIdInput) courseIdInput.value = curso.id
                if (courseNameInput) courseNameInput.value = curso.nombre
                if (courseGradeInput) courseGradeInput.value = curso.grado
                if (courseIconInput) courseIconInput.value = curso.icono || 'school'
                if (modalTitle) modalTitle.textContent = "Editar Curso"
                
                // Limpiar horarios
                scheduleDayCheckboxes.forEach(checkbox => {
                    checkbox.checked = false
                    const day = checkbox.value
                    const startInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endInput = document.querySelector(`input[name="end-time-${day}"]`)
                    if (startInput) {
                        startInput.disabled = true
                        startInput.value = ''
                    }
                    if (endInput) {
                        endInput.disabled = true
                        endInput.value = ''
                    }
                })
                
                // Cargar horarios si existen
                if (curso.horarios_procesados) {
                    Object.keys(curso.horarios_procesados).forEach(dia => {
                        const checkbox = document.querySelector(`input[name="schedule-day"][value="${dia}"]`)
                        if (checkbox) {
                            checkbox.checked = true
                            const startInput = document.querySelector(`input[name="start-time-${dia}"]`)
                            const endInput = document.querySelector(`input[name="end-time-${dia}"]`)
                            
                            if (startInput && endInput) {
                                startInput.disabled = false
                                endInput.disabled = false
                                
                                const horario = curso.horarios_procesados[dia]
                                const [inicio, fin] = horario.split('-')
                                startInput.value = inicio
                                endInput.value = fin
                            }
                        }
                    })
                }
                
                console.log('Datos del curso cargados exitosamente')
                return true
            } else {
                const errorMsg = data.error || 'Error desconocido al cargar el curso'
                console.error('Error en la respuesta:', errorMsg)
                notificar(errorMsg, 'error')
                return false
            }
        } catch (error) {
            console.error('Error al cargar curso:', error)
            notificar(`Error al cargar curso: ${error.message}`, 'error')
            return false
        }
    }

    // Abrir modal para crear curso
    function abrirModalCrear() {
        console.log('Abriendo modal para crear curso')
        
        // Resetear formulario
        if (courseForm) courseForm.reset()
        if (courseIdInput) courseIdInput.value = ""
        if (modalTitle) modalTitle.textContent = "Crear Nuevo Curso"
        
        // Limpiar horarios
        scheduleDayCheckboxes.forEach(checkbox => {
            checkbox.checked = false
            const day = checkbox.value
            const startInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endInput = document.querySelector(`input[name="end-time-${day}"]`)
            if (startInput) {
                startInput.disabled = true
                startInput.value = ''
            }
            if (endInput) {
                endInput.disabled = true
                endInput.value = ''
            }
        })
        
        // Mostrar modal
        if (courseModal) {
            courseModal.classList.add("active")
            document.body.style.overflow = "hidden"
            console.log('Modal de creación abierto')
        } else {
            console.error('Modal no encontrado')
        }
    }

    // Event listeners para botones de crear
    if (createCourseBtn) {
        createCourseBtn.addEventListener("click", abrirModalCrear)
        console.log('Event listener agregado para botón crear curso')
    }
    
    if (createFirstCourseBtn) {
        createFirstCourseBtn.addEventListener("click", abrirModalCrear)
        console.log('Event listener agregado para botón crear primer curso')
    }

    // Cerrar modales
    modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", () => {
            console.log('Cerrando modal')
            document.querySelectorAll(".modal-overlay").forEach(modal => {
                modal.classList.remove("active")
            })
            document.body.style.overflow = ""
        })
    })

    // Cerrar al hacer clic fuera del modal
    document.querySelectorAll(".modal-overlay").forEach(modal => {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                console.log('Cerrando modal (clic fuera)')
                modal.classList.remove("active")
                document.body.style.overflow = ""
            }
        })
    })

    // Manejar checkboxes de horario
    scheduleDayCheckboxes.forEach(checkbox => {
        checkbox.addEventListener("change", () => {
            const day = checkbox.value
            const startInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endInput = document.querySelector(`input[name="end-time-${day}"]`)
            
            if (startInput && endInput) {
                startInput.disabled = !checkbox.checked
                endInput.disabled = !checkbox.checked
                
                if (checkbox.checked) {
                    startInput.required = true
                    endInput.required = true
                } else {
                    startInput.required = false
                    endInput.required = false
                    startInput.value = ''
                    endInput.value = ''
                }
            }
        })
    })

    // BOTÓN DE EDITAR - FUNCIÓN PRINCIPAL
    document.addEventListener("click", async (e) => {
        if (e.target.closest('.edit-btn')) {
            e.preventDefault()
            e.stopPropagation()
            
            const btn = e.target.closest('.edit-btn')
            const courseId = btn.getAttribute("data-id")
            
            console.log(`Botón de editar clickeado para curso ID: ${courseId}`)
            
            if (courseId) {
                const success = await cargarDatosCurso(courseId)
                if (success && courseModal) {
                    courseModal.classList.add("active")
                    document.body.style.overflow = "hidden"
                    console.log('Modal de edición abierto exitosamente')
                    notificar('Modal de edición abierto correctamente', 'success')
                } else {
                    console.error('No se pudo abrir el modal de edición')
                    notificar('No se pudo abrir el modal de edición', 'error')
                }
            } else {
                console.error('ID de curso no encontrado')
                notificar('ID de curso no encontrado', 'error')
            }
        }
    })

    // Manejar envío del formulario
    if (courseForm) {
        courseForm.addEventListener("submit", async (e) => {
            e.preventDefault()
            console.log('Formulario enviado')
            
            const courseId = courseIdInput ? courseIdInput.value : ""
            const isNewCourse = courseId === ""
            
            // Recopilar datos del formulario
            const formData = {
                nombre: courseNameInput ? courseNameInput.value : "",
                grado: courseGradeInput ? courseGradeInput.value : "",
                icono: courseIconInput ? courseIconInput.value : "school"
            }
            
            // Recopilar horarios
            const horarios = {}
            scheduleDayCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const day = checkbox.value
                    const startInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endInput = document.querySelector(`input[name="end-time-${day}"]`)
                    
                    if (startInput && endInput && startInput.value && endInput.value) {
                        horarios[day] = {
                            activo: true,
                            inicio: startInput.value,
                            fin: endInput.value
                        }
                    }
                }
            })
            formData.horarios = horarios
            
            console.log('Datos a enviar:', formData)
            
            try {
                const url = isNewCourse ? 
                    '../api_cursos.php?action=crear_curso' : 
                    '../api_cursos.php?action=actualizar_curso'
                
                if (!isNewCourse) {
                    formData.id = courseId
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                })
                
                const data = await response.json()
                console.log('Respuesta del servidor:', data)
                
                if (data.success) {
                    notificar(data.mensaje || `Curso ${isNewCourse ? 'creado' : 'actualizado'} correctamente`, 'success')
                    
                    // Cerrar modal
                    if (courseModal) {
                        courseModal.classList.remove("active")
                        document.body.style.overflow = ""
                    }
                    
                    // Recargar página
                    setTimeout(() => {
                        window.location.reload()
                    }, 1000)
                } else {
                    notificar(data.error || `Error al ${isNewCourse ? 'crear' : 'actualizar'} el curso`, 'error')
                }
            } catch (error) {
                console.error('Error al guardar curso:', error)
                notificar(`Error al ${isNewCourse ? 'crear' : 'actualizar'} el curso: ${error.message}`, 'error')
            }
        })
    }

    // Búsqueda de cursos
    const searchInput = document.querySelector('.search-box input')
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase()
            const courseCards = document.querySelectorAll('.course-card')
            
            courseCards.forEach(card => {
                const courseName = card.querySelector('h3').textContent.toLowerCase()
                const courseGrade = card.querySelector('.course-grade').textContent.toLowerCase()
                
                if (courseName.includes(searchTerm) || courseGrade.includes(searchTerm)) {
                    card.style.display = 'block'
                } else {
                    card.style.display = 'none'
                }
            })
        })
    }

    // Filtros
    const periodFilter = document.getElementById('period-filter')
    const gradeFilter = document.getElementById('grade-filter')
    
    function aplicarFiltros() {
        const periodValue = periodFilter ? periodFilter.value : 'all'
        const gradeValue = gradeFilter ? gradeFilter.value : 'all'
        const courseCards = document.querySelectorAll('.course-card')
        
        courseCards.forEach(card => {
            const courseGrade = card.querySelector('.course-grade').textContent
            let mostrar = true
            
            // Aplicar filtro de grado
            if (gradeValue !== 'all') {
                const gradeText = window.grados[gradeValue] || gradeValue
                if (!courseGrade.includes(gradeText)) {
                    mostrar = false
                }
            }
            
            card.style.display = mostrar ? 'block' : 'none'
        })
    }
    
    if (periodFilter) {
        periodFilter.addEventListener('change', aplicarFiltros)
    }
    
    if (gradeFilter) {
        gradeFilter.addEventListener('change', aplicarFiltros)
    }

    console.log('=== SISTEMA DE CURSOS FINAL CARGADO EXITOSAMENTE ===')
}) 