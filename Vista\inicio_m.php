<?php
require_once '../Controlador/AuthController.php';

// Proteger la página - solo maestros
AuthController::proteger<PERSON><PERSON><PERSON>(['maestro']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información específica del maestro
$infoMaestro = $usuarioActual['informacion_rol'];
$especialidad = $infoMaestro['especialidad'] ?? 'No especificada';
$gradoTutor = $infoMaestro['grado_tutor'] ?? 'Sin grado asignado';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Inicio Maestros</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/inicio_m.css">
    <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar maestro'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                        <?php if ($fotoPerfilUrl): ?>
                            <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                        <p>Maestro(a) - <?php echo htmlspecialchars($especialidad); ?></p>
                        <?php if ($gradoTutor !== 'Sin grado asignado'): ?>
                            <small>Tutor: <?php echo htmlspecialchars($gradoTutor); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li class="active">
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_m.php">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../Controlador/AuthController.php?action=logout">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Panel de Control</h1>
                    <p class="current-date">Lunes, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">5</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Bienvenida y resumen -->
                <section class="welcome-section">
                    <div class="welcome-card">
                        <div class="welcome-content">
                            <h2>Bienvenido, Prof. Carlos García</h2>
                            <p>Tiene <strong>5</strong> mensajes sin leer y <strong>3</strong> tareas pendientes por calificar.</p>
                            <div class="welcome-actions">
                                <a href="mensajes_m.html" class="action-btn">
                                    <span class="material-icons">mail</span>
                                    Ver mensajes
                                </a>
                                <a href="cursos_m.html" class="action-btn">
                                    <span class="material-icons">assignment</span>
                                    Ver cursos
                                </a>
                            </div>
                        </div>
                        <div class="welcome-image">
                            <img src="/Vista/img/buho.png?height=200&width=200" alt="Ilustración de bienvenida">
                        </div>
                    </div>
                </section>
                
                <!-- Estadísticas rápidas -->
                <section class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <span class="material-icons">book</span>
                            </div>
                            <div class="stat-info">
                                <h3>Total Cursos</h3>
                                <div class="stat-value">5</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <span class="material-icons">schedule</span>
                            </div>
                            <div class="stat-info">
                                <h3>Horas Trabajadas</h3>
                                <div class="stat-value">24</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <span class="material-icons">assignment_turned_in</span>
                            </div>
                            <div class="stat-info">
                                <h3>Tareas Calificadas</h3>
                                <div class="stat-value">18</div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Horario del día -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Horario de hoy</h2>
                    </div>

                    <div class="schedule-list">
                        <div class="schedule-item">
                            <div class="schedule-time">7:30 - 8:15</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Matemáticas</div>
                                <div class="schedule-grade">5° Primaria</div>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">8:15 - 9:00</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Comunicación</div>
                                <div class="schedule-grade">5° Primaria</div>
                            </div>
                        </div>

                        <div class="schedule-item current-class">
                            <div class="schedule-time">9:00 - 9:45</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Ciencias Naturales</div>
                                <div class="schedule-grade">5° Primaria</div>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">9:45 - 10:15</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Recreo</div>
                                <div class="schedule-grade">Patio principal</div>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">10:15 - 11:00</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Inglés</div>
                                <div class="schedule-grade">5° Primaria</div>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">11:00 - 11:45</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Historia</div>
                                <div class="schedule-grade">6° Primaria</div>
                            </div>
                        </div>

                        <div class="schedule-item">
                            <div class="schedule-time">11:45 - 12:30</div>
                            <div class="schedule-details">
                                <div class="schedule-subject">Matemáticas</div>
                                <div class="schedule-grade">6° Primaria</div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Tareas pendientes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas por Calificar</h2>
                    </div>
                    
                    <div class="tasks-list">
                        <div class="task-item">
                            <div class="task-info">
                                <div class="task-title">Ejercicios de Fracciones</div>
                                <div class="task-meta">
                                    <span class="task-course">Matemáticas - 5° Primaria</span>
                                    <span class="task-date">Entrega: 18/03/2025</span>
                                </div>
                            </div>
                            <div class="task-pending">
                                <div class="pending-number">3</div>
                                <div class="pending-label">pendiente</div>
                            </div>
                        </div>

                        <div class="task-item">
                            <div class="task-info">
                                <div class="task-title">Proyecto de Ecosistemas</div>
                                <div class="task-meta">
                                    <span class="task-course">Ciencias - 6° Primaria</span>
                                    <span class="task-date">Entrega: 20/03/2025</span>
                                </div>
                            </div>
                            <div class="task-pending">
                                <div class="pending-number">5</div>
                                <div class="pending-label">pendiente</div>
                            </div>
                        </div>

                        <div class="task-item">
                            <div class="task-info">
                                <div class="task-title">Examen Bimestral</div>
                                <div class="task-meta">
                                    <span class="task-course">Matemáticas - 4° Primaria</span>
                                    <span class="task-date">Realizado: 21/03/2025</span>
                                </div>
                            </div>
                            <div class="task-pending urgent">
                                <div class="pending-number">8</div>
                                <div class="pending-label">pendiente</div>
                            </div>
                        </div>
                    </div>
                </section>

            </div>
        </main>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/inicio_m.js"></script>
</body>
</html>

