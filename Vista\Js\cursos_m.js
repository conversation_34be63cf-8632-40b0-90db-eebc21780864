document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const createCourseBtn = document.getElementById("create-course-btn")
    const createFirstCourseBtn = document.getElementById("create-first-course-btn")
    const courseModal = document.getElementById("course-modal")
    const deleteModal = document.getElementById("delete-modal")
    const loadingModal = document.getElementById("loading-modal")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const courseForm = document.getElementById("course-form")
    const modalTitle = document.getElementById("modal-title")
    const courseIdInput = document.getElementById("course-id")
    const courseNameInput = document.getElementById("course-name")
    const courseGradeInput = document.getElementById("course-grade")
    const courseIconInput = document.getElementById("course-icon")
    const courseImageInput = document.getElementById("course-image")
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn")
    const editBtns = document.querySelectorAll(".edit-btn")
    const deleteBtns = document.querySelectorAll(".delete-btn")
    const scheduleDayCheckboxes = document.querySelectorAll('input[name="schedule-day"]')
    const coursesGrid = document.getElementById("courses-grid")
    const searchInput = document.getElementById("search-input")
    const periodFilter = document.getElementById("period-filter")
    const gradeFilter = document.getElementById("grade-filter")
    const viewBtns = document.querySelectorAll(".view-btn")
  
    // Variables para manejo de imágenes
    let selectedImageFile = null
    let imagePreviewUrl = null
    let currentView = 'grid'
  
    // Actualizar fecha actual
    const dateElements = document.querySelectorAll(".current-date")
    if (dateElements.length > 0) {
        const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
        const today = new Date()
        dateElements.forEach((element) => {
            element.textContent = today.toLocaleDateString("es-ES", options)
        })
    }
  
    // Función para mostrar notificaciones
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div')
        notification.className = `notification notification-${type}`
        notification.innerHTML = `
            <span class="material-icons">${type === 'success' ? 'check_circle' : 'error'}</span>
            <span>${message}</span>
        `
        document.body.appendChild(notification)
        
        setTimeout(() => {
            notification.classList.add('show')
        }, 100)
        
        setTimeout(() => {
            notification.classList.remove('show')
            setTimeout(() => {
                document.body.removeChild(notification)
            }, 300)
        }, 4000)
    }

    // Función para mostrar/ocultar modal de carga
    function showLoading(show = true) {
        if (loadingModal) {
            if (show) {
                loadingModal.classList.add("active")
                document.body.style.overflow = "hidden"
            } else {
                loadingModal.classList.remove("active")
                document.body.style.overflow = ""
            }
        }
    }

    // Función para manejar la selección de imagen
    function handleImageSelection(file) {
        // Validar tipo de archivo
        if (!file.type.startsWith('image/')) {
            showNotification('Por favor selecciona un archivo de imagen válido', 'error')
            return false
        }

        // Validar tamaño (máximo 5MB)
        const maxSize = 5 * 1024 * 1024 // 5MB
        if (file.size > maxSize) {
            showNotification('La imagen debe ser menor a 5MB', 'error')
            return false
        }

        selectedImageFile = file

        // Crear vista previa
        const reader = new FileReader()
        reader.onload = function(e) {
            imagePreviewUrl = e.target.result
            showImagePreview()
        }
        reader.readAsDataURL(file)

        return true
    }

    // Función para mostrar vista previa de imagen
    function showImagePreview() {
        // Buscar o crear contenedor de vista previa
        let previewContainer = document.querySelector('.image-preview-container')
        if (!previewContainer) {
            previewContainer = document.createElement('div')
            previewContainer.className = 'image-preview-container'
            previewContainer.innerHTML = `
                <div class="image-preview">
                    <img src="" alt="Vista previa" class="preview-image">
                    <button type="button" class="remove-image-btn">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            `
            
            // Insertar después del campo de archivo
            const fileUploadContainer = document.querySelector('.file-upload')
            if (fileUploadContainer) {
                fileUploadContainer.parentNode.insertBefore(previewContainer, fileUploadContainer.nextSibling)
            }
        }

        // Actualizar imagen
        const previewImage = previewContainer.querySelector('.preview-image')
        if (previewImage) {
            previewImage.src = imagePreviewUrl
        }

        // Agregar evento para remover imagen
        const removeBtn = previewContainer.querySelector('.remove-image-btn')
        if (removeBtn) {
            removeBtn.onclick = removeImagePreview
        }

        // Ocultar el botón de selección
        const fileUploadBtn = document.querySelector('.file-upload-btn')
        if (fileUploadBtn) {
            fileUploadBtn.style.display = 'none'
        }
    }

    // Función para remover vista previa de imagen
    function removeImagePreview() {
        selectedImageFile = null
        imagePreviewUrl = null

        // Remover contenedor de vista previa
        const previewContainer = document.querySelector('.image-preview-container')
        if (previewContainer) {
            previewContainer.remove()
        }

        // Limpiar input
        if (courseImageInput) {
            courseImageInput.value = ''
        }

        // Mostrar botón de selección
        const fileUploadBtn = document.querySelector('.file-upload-btn')
        if (fileUploadBtn) {
            fileUploadBtn.style.display = 'flex'
        }
    }

    // Función para validar formulario
    function validateForm() {
        const errors = []

        // Validar nombre
        if (!courseNameInput.value.trim()) {
            errors.push('El nombre del curso es requerido')
        } else if (courseNameInput.value.trim().length < 3) {
            errors.push('El nombre del curso debe tener al menos 3 caracteres')
        }

        // Validar grado
        if (!courseGradeInput.value) {
            errors.push('Debe seleccionar un grado')
        }

        // Validar ícono
        if (!courseIconInput.value) {
            errors.push('Debe seleccionar un ícono')
        }

        // Validar horarios
        let tieneHorarios = false
        scheduleDayCheckboxes.forEach((checkbox) => {
            if (checkbox.checked) {
                const day = checkbox.value
                const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
                const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
                
                if (startTimeInput && endTimeInput && startTimeInput.value && endTimeInput.value) {
                    tieneHorarios = true
                } else {
                    errors.push(`Debe completar los horarios para ${day}`)
                }
            }
        })

        if (!tieneHorarios) {
            errors.push('Debe seleccionar al menos un día con horario')
        }

        return errors
    }
  
    // Función para cargar datos de un curso para editar
    async function cargarDatosCurso(cursoId) {
        try {
            showLoading(true)
            const response = await fetch(`../api_cursos.php?action=obtener_curso&id=${cursoId}`)
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            
            const data = await response.json()
            
            if (data.success && data.curso) {
                const curso = data.curso
                
                // Llenar el formulario con los datos del curso
                courseIdInput.value = curso.id
                courseNameInput.value = curso.nombre
                courseGradeInput.value = curso.grado
                courseIconInput.value = curso.icono || 'school'

                // Cargar imagen si existe
                if (curso.imagen) {
                    imagePreviewUrl = curso.imagen
                    showImagePreview()
                    // Cambiar el texto del botón para indicar que hay imagen
                    const fileUploadBtn = document.querySelector('.file-upload-btn')
                    if (fileUploadBtn) {
                        fileUploadBtn.innerHTML = '<span class="material-icons">image</span> Cambiar imagen'
                    }
                } else {
                    removeImagePreview()
                }

                // Limpiar todos los checkboxes y campos de tiempo
                scheduleDayCheckboxes.forEach((checkbox) => {
                    checkbox.checked = false
                    const day = checkbox.value
                    const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
                    
                    if (startTimeInput && endTimeInput) {
                        startTimeInput.disabled = true
                        endTimeInput.disabled = true
                        startTimeInput.value = ''
                        endTimeInput.value = ''
                    }
                })

                // Marcar los días de la semana y establecer horarios
                if (curso.horarios_procesados && Object.keys(curso.horarios_procesados).length > 0) {
                    Object.keys(curso.horarios_procesados).forEach((dia) => {
                        const checkbox = document.querySelector(`input[name="schedule-day"][value="${dia}"]`)
                        if (checkbox) {
                            checkbox.checked = true
                            
                            const startTimeInput = document.querySelector(`input[name="start-time-${dia}"]`)
                            const endTimeInput = document.querySelector(`input[name="end-time-${dia}"]`)
                            
                            if (startTimeInput && endTimeInput) {
                                startTimeInput.disabled = false
                                endTimeInput.disabled = false
                                
                                const horario = curso.horarios_procesados[dia]
                                if (horario && typeof horario === 'string') {
                                    const [inicio, fin] = horario.split('-')
                                    if (inicio && fin) {
                                        startTimeInput.value = inicio.trim()
                                        endTimeInput.value = fin.trim()
                                    }
                                }
                            }
                        }
                    })
                }

                // Actualizar título del modal
                modalTitle.textContent = "Editar Curso"
                
                return true
            } else {
                showNotification(data.error || 'Error al cargar los datos del curso', 'error')
                return false
            }
        } catch (error) {
            console.error('Error al cargar curso:', error)
            showNotification('Error al cargar los datos del curso: ' + error.message, 'error')
            return false
        } finally {
            showLoading(false)
        }
    }
  
    // Función para recargar la lista de cursos
    async function recargarCursos() {
        try {
            showLoading(true)
            const response = await fetch('../api_cursos.php?action=obtener_cursos')
            const data = await response.json()
            
            if (data.success) {
                // Actualizar los datos en window
                window.cursosData = data.cursos
                
                // Recargar la página para mostrar los cambios
                window.location.reload()
            } else {
                showNotification('Error al recargar los cursos', 'error')
            }
        } catch (error) {
            console.error('Error al recargar cursos:', error)
            showNotification('Error al recargar los cursos', 'error')
        } finally {
            showLoading(false)
        }
    }
  
    // Abrir modal para crear curso
    function abrirModalCrear() {
        // Resetear el formulario
        courseForm.reset()
        courseIdInput.value = ""
        modalTitle.textContent = "Crear Nuevo Curso"

        // Limpiar imagen
        removeImagePreview()

        // Deshabilitar todos los campos de tiempo
        scheduleDayCheckboxes.forEach((checkbox) => {
            const day = checkbox.value
            const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
            
            if (startTimeInput && endTimeInput) {
                startTimeInput.disabled = true
                endTimeInput.disabled = true
                startTimeInput.value = ''
                endTimeInput.value = ''
            }
        })
  
        // Mostrar el modal
        courseModal.classList.add("active")
        document.body.style.overflow = "hidden"
    }

    // Función para cambiar vista (grid/list)
    function cambiarVista(vista) {
        currentView = vista
        
        // Actualizar botones
        viewBtns.forEach(btn => {
            btn.classList.remove('active')
            if (btn.getAttribute('data-view') === vista) {
                btn.classList.add('active')
            }
        })
        
        // Actualizar grid
        if (coursesGrid) {
            coursesGrid.classList.remove('grid-view', 'list-view')
            coursesGrid.classList.add(`${vista}-view`)
        }
    }

    // Función para aplicar filtros y búsqueda
    function aplicarFiltros() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : ''
        const periodValue = periodFilter ? periodFilter.value : 'all'
        const gradeValue = gradeFilter ? gradeFilter.value : 'all'
        const courseCards = document.querySelectorAll('.course-card')
        
        courseCards.forEach(card => {
            const courseName = card.querySelector('h3').textContent.toLowerCase()
            const courseGrade = card.getAttribute('data-grade')
            let mostrar = true
            
            // Aplicar búsqueda
            if (searchTerm) {
                const gradeText = window.grados[courseGrade] || courseGrade
                if (!courseName.includes(searchTerm) && !gradeText.toLowerCase().includes(searchTerm)) {
                    mostrar = false
                }
            }
            
            // Aplicar filtro de grado
            if (gradeValue !== 'all' && courseGrade !== gradeValue) {
                mostrar = false
            }
            
            // Por ahora el filtro de periodo no se aplica (todos los cursos son del año actual)
            
            card.style.display = mostrar ? 'block' : 'none'
        })
    }
  
    // Configurar eventos
    if (createCourseBtn) {
        createCourseBtn.addEventListener("click", abrirModalCrear)
    }

    if (createFirstCourseBtn) {
        createFirstCourseBtn.addEventListener("click", abrirModalCrear)
    }
  
    // Cerrar modales
    if (modalCloseBtns.length > 0) {
        modalCloseBtns.forEach((btn) => {
            btn.addEventListener("click", () => {
                // Cerrar todos los modales
                const modals = document.querySelectorAll(".modal-overlay")
                modals.forEach((modal) => {
                    modal.classList.remove("active")
                })
                document.body.style.overflow = "" // Restaurar scroll en el body
            })
        })
    }
  
    // Cerrar modales al hacer clic fuera del contenido
    const modals = document.querySelectorAll(".modal-overlay")
    modals.forEach((modal) => {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.classList.remove("active")
                document.body.style.overflow = "" // Restaurar scroll en el body
            }
        })
    })
  
    // Habilitar/deshabilitar campos de horario según checkbox
    if (scheduleDayCheckboxes.length > 0) {
        scheduleDayCheckboxes.forEach((checkbox) => {
            checkbox.addEventListener("change", () => {
                const day = checkbox.value
                const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
                const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
  
                if (startTimeInput && endTimeInput) {
                    startTimeInput.disabled = !checkbox.checked
                    endTimeInput.disabled = !checkbox.checked
  
                    if (checkbox.checked) {
                        startTimeInput.required = true
                        endTimeInput.required = true
                    } else {
                        startTimeInput.required = false
                        endTimeInput.required = false
                        startTimeInput.value = ''
                        endTimeInput.value = ''
                    }
                }
            })
        })
    }
  
    // Abrir modal para editar curso
    document.addEventListener("click", async (e) => {
        if (e.target.closest('.edit-btn')) {
            e.stopPropagation()
            const btn = e.target.closest('.edit-btn')
            const courseId = btn.getAttribute("data-id")
            
            if (await cargarDatosCurso(courseId)) {
                // Mostrar el modal
                courseModal.classList.add("active")
                document.body.style.overflow = "hidden"
            }
        }
    })
  
    // Abrir modal para eliminar curso
    document.addEventListener("click", (e) => {
        if (e.target.closest('.delete-btn')) {
            e.stopPropagation()
            const btn = e.target.closest('.delete-btn')
            const courseId = btn.getAttribute("data-id")
  
            // Guardar el ID del curso a eliminar
            if (confirmDeleteBtn) {
                confirmDeleteBtn.setAttribute("data-id", courseId)
            }
  
            // Mostrar el modal de confirmación
            if (deleteModal) {
                deleteModal.classList.add("active")
                document.body.style.overflow = "hidden"
            }
        }
    })
  
    // Confirmar eliminación de curso
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", async () => {
            const courseId = confirmDeleteBtn.getAttribute("data-id")
  
            try {
                showLoading(true)
                const response = await fetch(`../api_cursos.php?action=eliminar_curso&id=${courseId}`, {
                    method: 'DELETE'
                })
                const data = await response.json()
                
                if (data.success) {
                    showNotification('Curso eliminado correctamente')
  
                    // Cerrar el modal
                    if (deleteModal) {
                        deleteModal.classList.remove("active")
                        document.body.style.overflow = ""
                    }
                    
                    // Recargar la lista de cursos
                    await recargarCursos()
                } else {
                    showNotification(data.error || 'Error al eliminar el curso', 'error')
                }
            } catch (error) {
                console.error('Error al eliminar curso:', error)
                showNotification('Error al eliminar el curso', 'error')
            } finally {
                showLoading(false)
            }
        })
    }
  
    // Enviar formulario de curso
    if (courseForm) {
        courseForm.addEventListener("submit", async (e) => {
            e.preventDefault()

            // Validar formulario
            const errors = validateForm()
            if (errors.length > 0) {
                errors.forEach(error => showNotification(error, 'error'))
                return
            }

            const courseId = courseIdInput.value
            const isNewCourse = courseId === ""

            // Recopilar datos del formulario
            const formData = {
                nombre: courseNameInput.value.trim(),
                grado: courseGradeInput.value,
                icono: courseIconInput.value,
                imagen: imagePreviewUrl // Usar la URL de la imagen seleccionada
            }
            
            // Recopilar horarios
            const horarios = {}
            scheduleDayCheckboxes.forEach((checkbox) => {
                const day = checkbox.value
                if (checkbox.checked) {
                    const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
                    
                    if (startTimeInput && endTimeInput && startTimeInput.value && endTimeInput.value) {
                        horarios[day] = {
                            activo: true,
                            inicio: startTimeInput.value,
                            fin: endTimeInput.value
                        }
                    }
                }
            })
            
            formData.horarios = horarios
            
            try {
                showLoading(true)
                const url = isNewCourse ? '../api_cursos.php?action=crear_curso' : '../api_cursos.php?action=actualizar_curso'
                const method = 'POST'
                
                if (!isNewCourse) {
                    formData.id = courseId
                }
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`)
                }
                
                const data = await response.json()
                
                if (data.success) {
                    showNotification(data.mensaje || `Curso ${isNewCourse ? 'creado' : 'actualizado'} correctamente`)

                    // Cerrar el modal
                    if (courseModal) {
                        courseModal.classList.remove("active")
                        document.body.style.overflow = ""
                    }
                    
                    // Recargar la lista de cursos
                    await recargarCursos()
                } else {
                    showNotification(data.error || `Error al ${isNewCourse ? 'crear' : 'actualizar'} el curso`, 'error')
                }
            } catch (error) {
                console.error('Error al guardar curso:', error)
                showNotification(`Error al ${isNewCourse ? 'crear' : 'actualizar'} el curso: ` + error.message, 'error')
            } finally {
                showLoading(false)
            }
        })
    }

    // Configurar evento para selección de imagen
    if (courseImageInput) {
        courseImageInput.addEventListener('change', (e) => {
            const file = e.target.files[0]
            if (file) {
                handleImageSelection(file)
            }
        })
    }

    // Configurar evento para el botón de selección de archivo
    const fileUploadBtn = document.querySelector('.file-upload-btn')
    if (fileUploadBtn) {
        fileUploadBtn.addEventListener('click', () => {
            courseImageInput.click()
        })
    }

    // Búsqueda de cursos
    if (searchInput) {
        searchInput.addEventListener('input', aplicarFiltros)
    }

    // Filtros
    if (periodFilter) {
        periodFilter.addEventListener('change', aplicarFiltros)
    }
    
    if (gradeFilter) {
        gradeFilter.addEventListener('change', aplicarFiltros)
    }

    // Cambio de vista
    if (viewBtns.length > 0) {
        viewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const vista = btn.getAttribute('data-view')
                cambiarVista(vista)
            })
        })
    }

    // Eventos para botones de acción adicionales
    document.addEventListener("click", (e) => {
        if (e.target.closest('.manage-btn')) {
            const btn = e.target.closest('.manage-btn')
            const courseCard = btn.closest('.course-card')
            const courseId = courseCard.getAttribute('data-id')
            
            // Redirigir a gestión de contenido
            window.location.href = `contenido_m.php?curso_id=${courseId}`
        }
        
        if (e.target.closest('.students-btn')) {
            const btn = e.target.closest('.students-btn')
            const courseCard = btn.closest('.course-card')
            const courseId = courseCard.getAttribute('data-id')
            
            // Aquí podrías redirigir a una página de estudiantes del curso
            showNotification('Funcionalidad de estudiantes próximamente', 'success')
        }
    })

    // Inicializar vista por defecto
    cambiarVista('grid')
})
  