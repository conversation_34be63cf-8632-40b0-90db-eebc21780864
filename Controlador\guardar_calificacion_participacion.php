<?php
session_start();
require_once '../Modelo/conexion.php';

// Verificar que el usuario esté logueado y sea maestro
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'maestro') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

// Obtener datos JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Datos inválidos']);
    exit;
}

$maestroId = $_SESSION['user_id'];
$estudianteId = $input['estudiante_id'] ?? null;
$contenidoId = $input['contenido_id'] ?? null;
$calificacion = $input['calificacion'] ?? null;

// Validar datos requeridos
if (!$estudianteId || !$contenidoId || $calificacion === null) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Faltan datos requeridos']);
    exit;
}

// Validar que la calificación esté en el rango correcto
if ($calificacion < 0 || $calificacion > 20) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'La calificación debe estar entre 0 y 20']);
    exit;
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Verificar que el contenido pertenece a un curso del maestro
    $stmtVerificar = $pdo->prepare("
        SELECT c.id, c.tipo, cur.maestro_id 
        FROM contenido c 
        INNER JOIN cursos cur ON c.curso_id = cur.id 
        WHERE c.id = ? AND cur.maestro_id = ?
    ");
    $stmtVerificar->execute([$contenidoId, $maestroId]);
    $contenido = $stmtVerificar->fetch();

    if (!$contenido) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Contenido no encontrado o no autorizado']);
        exit;
    }

    // Verificar que el contenido sea de tipo participación
    if ($contenido['tipo'] !== 'participacion') {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Este contenido no es de tipo participación']);
        exit;
    }

    // Verificar que el estudiante esté inscrito en el curso
    $stmtInscrito = $pdo->prepare("
        SELECT 1 FROM inscripciones i 
        INNER JOIN contenido c ON c.curso_id = i.curso_id 
        WHERE i.estudiante_id = ? AND c.id = ?
    ");
    $stmtInscrito->execute([$estudianteId, $contenidoId]);
    
    if (!$stmtInscrito->fetch()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'El estudiante no está inscrito en este curso']);
        exit;
    }

    // Verificar si ya existe una calificación para este estudiante y contenido
    $stmtExiste = $pdo->prepare("
        SELECT id FROM calificaciones_participacion 
        WHERE estudiante_id = ? AND contenido_id = ?
    ");
    $stmtExiste->execute([$estudianteId, $contenidoId]);
    $calificacionExistente = $stmtExiste->fetch();

    if ($calificacionExistente) {
        // Actualizar calificación existente
        $stmtUpdate = $pdo->prepare("
            UPDATE calificaciones_participacion 
            SET calificacion = ?, fecha_actualizacion = NOW() 
            WHERE id = ?
        ");
        $stmtUpdate->execute([$calificacion, $calificacionExistente['id']]);
        $mensaje = 'Calificación actualizada exitosamente';
    } else {
        // Insertar nueva calificación
        $stmtInsert = $pdo->prepare("
            INSERT INTO calificaciones_participacion (estudiante_id, contenido_id, calificacion, fecha_creacion) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmtInsert->execute([$estudianteId, $contenidoId, $calificacion]);
        $mensaje = 'Calificación guardada exitosamente';
    }

    echo json_encode([
        'success' => true, 
        'message' => $mensaje,
        'data' => [
            'estudiante_id' => $estudianteId,
            'contenido_id' => $contenidoId,
            'calificacion' => $calificacion
        ]
    ]);

} catch (PDOException $e) {
    error_log("Error en guardar_calificacion_participacion.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Error interno del servidor']);
}
?>
