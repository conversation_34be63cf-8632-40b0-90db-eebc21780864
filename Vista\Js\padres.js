document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const studentSelect = document.getElementById("student-select")
  const prevMonthBtn = document.getElementById("prev-month")
  const nextMonthBtn = document.getElementById("next-month")
  const currentMonthEl = document.getElementById("current-month")
  const calendarDays = document.querySelectorAll(".calendar-day:not(.empty)")
  
  // Datos del calendario
  const months = ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"]
  let currentDate = new Date()
  let currentMonth = currentDate.getMonth()
  let currentYear = currentDate.getFullYear()
  
  // Cambiar estudiante seleccionado
  if (studentSelect) {
    studentSelect.addEventListener("change", () => {
      // Aquí iría la lógica para cargar los datos del estudiante seleccionado
      const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
      console.log(`Estudiante seleccionado: ${selectedStudent}`)
    })
  }
  
  // Navegación del calendario
  if (prevMonthBtn && nextMonthBtn && currentMonthEl) {
    // Actualizar el texto del mes actual
    const updateMonthDisplay = () => {
      currentMonthEl.textContent = `${months[currentMonth]} ${currentYear}`
    }
    
    // Ir al mes anterior
    prevMonthBtn.addEventListener("click", () => {
      currentMonth--
      if (currentMonth < 0) {
        currentMonth = 11
        currentYear--
      }
      updateMonthDisplay()
      // Aquí iría la lógica para actualizar los días del calendario
    })
    
    // Ir al mes siguiente
    nextMonthBtn.addEventListener("click", () => {
      currentMonth++
      if (currentMonth > 11) {
        currentMonth = 0
        currentYear++
      }
      updateMonthDisplay()
      // Aquí iría la lógica para actualizar los días del calendario
    })
  }
  
  // Eventos al hacer clic en los días del calendario
  if (calendarDays.length > 0) {
    calendarDays.forEach((day) => {
      day.addEventListener("click", () => {
        const dayNumber = day.querySelector(".day-number").textContent
        console.log(`Día seleccionado: ${dayNumber}/${currentMonth + 1}/${currentYear}`)
        // Aquí iría la lógica para mostrar eventos del día seleccionado
      })
    })
  }
  
  // Animación para las tarjetas de estadísticas
  const statCards = document.querySelectorAll(".stat-card")
  if (statCards.length > 0) {
    statCards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`
      card.classList.add("fade-in")
    })
  }
})