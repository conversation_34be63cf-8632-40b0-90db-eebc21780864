<?php
// Configurar manejo de errores para devolver JSON
error_reporting(0);
ini_set('display_errors', 0);

session_start();
header('Content-Type: application/json');

// Función para devolver respuesta JSON de error
function returnError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit;
}

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    returnError('No autorizado', 401);
}

try {
    require_once 'Controlador/CursoController.php';
} catch (Exception $e) {
    returnError('Error al cargar el controlador: ' . $e->getMessage(), 500);
}

$controller = new CursoController();
$maestroId = $_SESSION['usuario_id'];

// Obtener el método HTTP
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if ($action === 'obtener_cursos') {
                $cursos = $controller->obtenerCursosPorMaestro($maestroId);
                echo json_encode(['success' => true, 'cursos' => $cursos]);
            } elseif ($action === 'obtener_curso') {
                $cursoId = $_GET['id'] ?? null;
                if ($cursoId) {
                    $curso = $controller->obtenerCursoPorId($cursoId, $maestroId);
                    if ($curso) {
                        echo json_encode(['success' => true, 'curso' => $curso]);
                    } else {
                        returnError('Curso no encontrado');
                    }
                } else {
                    returnError('ID de curso requerido');
                }
            } elseif ($action === 'obtener_opciones') {
                $grados = $controller->obtenerGrados();
                $iconos = $controller->obtenerIconos();
                echo json_encode([
                    'success' => true, 
                    'grados' => $grados,
                    'iconos' => $iconos
                ]);
            } else {
                returnError('Acción no válida');
            }
            break;

        case 'POST':
            if ($action === 'crear_curso') {
                $input = file_get_contents('php://input');
                $datos = json_decode($input, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    returnError('Datos JSON inválidos');
                }
                
                // Validar datos requeridos (sin especialidad)
                if (empty($datos['nombre']) || empty($datos['grado'])) {
                    returnError('Datos incompletos: nombre y grado son requeridos');
                }

                $datos['maestro_id'] = $maestroId;
                $datos['anio_escolar'] = date('Y');
                
                $cursoId = $controller->crearCurso($datos);
                if ($cursoId) {
                    echo json_encode(['success' => true, 'curso_id' => $cursoId, 'mensaje' => 'Curso creado exitosamente']);
                } else {
                    returnError('Error al crear el curso');
                }
            } elseif ($action === 'actualizar_curso') {
                $input = file_get_contents('php://input');
                $datos = json_decode($input, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    returnError('Datos JSON inválidos');
                }
                
                $cursoId = $datos['id'] ?? null;
                
                if (!$cursoId) {
                    returnError('ID de curso requerido');
                }

                $datos['maestro_id'] = $maestroId;
                
                if ($controller->actualizarCurso($cursoId, $datos)) {
                    echo json_encode(['success' => true, 'mensaje' => 'Curso actualizado exitosamente']);
                } else {
                    returnError('Error al actualizar el curso');
                }
            } else {
                returnError('Acción no válida');
            }
            break;

        case 'DELETE':
            if ($action === 'eliminar_curso') {
                $cursoId = $_GET['id'] ?? null;
                
                if (!$cursoId) {
                    returnError('ID de curso requerido');
                }

                if ($controller->eliminarCurso($cursoId, $maestroId)) {
                    echo json_encode(['success' => true, 'mensaje' => 'Curso eliminado exitosamente']);
                } else {
                    returnError('Error al eliminar el curso');
                }
            } else {
                returnError('Acción no válida');
            }
            break;

        default:
            returnError('Método no permitido', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Error en API de cursos: " . $e->getMessage());
    returnError('Error interno del servidor: ' . $e->getMessage(), 500);
}
?> 