<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

// Verificar que se proporcione el ID del curso
if (!isset($_GET['curso_id']) || empty($_GET['curso_id'])) {
    header('Location: cursos_m.php');
    exit;
}

$cursoId = (int)$_GET['curso_id'];
$maestroId = $_SESSION['usuario_id'];

require_once '../Controlador/CursoController.php';

$controller = new CursoController();

// Obtener información del curso
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados para mostrar el nombre del grado
$grados = $controller->obtenerGrados();
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Tareas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_m.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <?php if ($maestro && $maestro['foto_perfil']): ?>
                          <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                      <?php else: ?>
                          <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.php">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.php">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.php">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_m.php" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1><?php echo htmlspecialchars($curso['nombre']); ?></h1>
                          <p><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <?php if (!empty($curso['horarios_procesados'])): ?>
                              <?php 
                              $dias = [
                                  'lunes' => 'Lunes',
                                  'martes' => 'Martes',
                                  'miercoles' => 'Miércoles',
                                  'jueves' => 'Jueves',
                                  'viernes' => 'Viernes'
                              ];
                              $contador = 0;
                              foreach ($curso['horarios_procesados'] as $dia => $horario): 
                                  if ($contador < 2): // Mostrar solo los primeros 2 horarios
                              ?>
                                  <div class="schedule-day">
                                      <span class="day-label"><?php echo $dias[$dia]; ?></span>
                                      <span class="day-time"><?php echo htmlspecialchars($horario); ?></span>
                                  </div>
                              <?php 
                                  endif;
                                  $contador++;
                              endforeach; 
                              ?>
                          <?php endif; ?>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Contenido</a>
                  <a href="tareas_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Tareas del curso</h2>
                      <div class="section-actions">
                          <div class="search-container">
                              <input type="text" class="search-input" placeholder="Buscar tarea...">
                              <span class="material-icons search-icon">search</span>
                          </div>
                          <a href="tareas_crud.php?curso_id=<?php echo $cursoId; ?>" class="action-btn manage-tasks-btn">
                              <span class="material-icons">settings</span>
                              <span>Administrar Tareas</span>
                          </a>
                      </div>
                  </div>
                  
                  <!-- Loading indicator -->
                  <div id="loading-indicator" class="loading-container">
                      <div class="loading-spinner"></div>
                      <p>Cargando tareas...</p>
                  </div>
                  
                  <!-- Error message -->
                  <div id="error-message" class="error-container" style="display: none;">
                      <span class="material-icons">error</span>
                      <p>Error al cargar las tareas. Inténtalo de nuevo.</p>
                      <button class="retry-btn" onclick="location.reload()">Reintentar</button>
                  </div>
                  
                  <!-- Empty state -->
                  <div id="empty-state" class="empty-container" style="display: none;">
                      <span class="material-icons">assignment</span>
                      <h3>No hay tareas disponibles</h3>
                      <p>Aún no se han creado tareas para este curso.</p>
                      <a href="tareas_crud.php?curso_id=<?php echo $cursoId; ?>" class="action-btn create-first-task-btn">
                          <span class="material-icons">add</span>
                          <span>Crear primera tarea</span>
                      </a>
                  </div>
                  
                  <!-- Tasks container -->
                  <div id="tasks-container" class="tasks-container" style="display: none;">
                      <!-- Las tareas se cargarán dinámicamente aquí -->
                  </div>
              </section>
          </div>
      </main>
  </div>
  
  <!-- Modal para ver tarea -->
  <div id="task-view-modal" class="modal-overlay" style="display: none;">
      <div class="modal-content assignment-container">
          <div class="assignment-header">
              <h1 class="assignment-title">
                  <span class="material-icons">assignment</span>
                  <span id="modal-task-title">Tarea</span>
              </h1>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="assignment-content">
              <div class="assignment-section">
                  <h2 class="assignment-section-title">Descripción de la tarea</h2>
                  <p id="modal-task-description" class="assignment-description"></p>
                  <div id="modal-task-attachment" class="task-attachment" style="display: none;">
                      <h4>Archivo adjunto:</h4>
                      <a id="modal-task-file-link" href="#" target="_blank" class="attachment-link">
                          <span class="material-icons">attachment</span>
                          Ver archivo
                      </a>
                  </div>
              </div>
              
              <div class="assignment-section">
                  <div class="assignment-meta">
                      <div class="meta-item">
                          <span class="material-icons">event</span>
                          <div class="meta-content">
                              <span class="meta-label">Fecha límite</span>
                              <span id="modal-task-due-date" class="meta-value"></span>
                          </div>
                      </div>
                      <div class="meta-item">
                          <span class="material-icons">grade</span>
                          <div class="meta-content">
                              <span class="meta-label">Puntos</span>
                              <span id="modal-task-points" class="meta-value"></span>
                          </div>
                      </div>
                      <div class="meta-item">
                          <span class="material-icons">schedule</span>
                          <div class="meta-content">
                              <span class="meta-label">Semana</span>
                              <span id="modal-task-week" class="meta-value"></span>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  
  <!-- Modal para ver entregas -->
  <div id="submissions-modal" class="modal-overlay" style="display: none;">
      <div class="modal-content large-modal">
          <div class="modal-header">
              <h3 id="submissions-modal-title">Entregas</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <!-- Loading indicator for submissions -->
              <div id="submissions-loading" class="loading-container">
                  <div class="loading-spinner"></div>
                  <p>Cargando entregas...</p>
              </div>
              
              <!-- Submissions content -->
              <div id="submissions-content" style="display: none;">
                  <div class="submissions-stats">
                      <div class="stat-item">
                          <span id="submitted-count" class="stat-number">0</span>
                          <span class="stat-label">Entregadas</span>
                      </div>
                      <div class="stat-item">
                          <span id="pending-count" class="stat-number">0</span>
                          <span class="stat-label">Pendientes</span>
                      </div>
                      <div class="stat-item">
                          <span id="graded-count" class="stat-number">0</span>
                          <span class="stat-label">Calificadas</span>
                      </div>
                  </div>
                  
                  <div id="submissions-list" class="submissions-list">
                      <!-- Las entregas se cargarán dinámicamente aquí -->
                  </div>
              </div>
          </div>
      </div>
  </div>

  <!-- Datos para JavaScript -->
  <script>
      window.cursoData = <?php echo json_encode($curso); ?>;
      window.cursoId = <?php echo $cursoId; ?>;
      window.maestroId = <?php echo $maestroId; ?>;
  </script>

  <!-- Scripts -->
  <script src="./Js/tareas_m.js"></script>
</body>
</html>
