/* Estilos para solicitudes de preinscripción */

/* Variables CSS */
:root {
  --primary-color: #2563eb;
  --success-color: #10b981;
  --text-color: #374151;
  --text-color-light: #6b7280;
  --border-color: #e5e7eb;
  --bg-color-light: #f9fafb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --transition: all 0.2s ease;
}

/* Card de búsqueda */
.search-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
  margin-bottom: 1.5rem;
}

.search-card .card-header {
  background: var(--primary-color);
  color: white;
  padding: 1.25rem 1.5rem;
  border-radius: 0.5rem 0.5rem 0 0;
}

.search-card .card-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.search-card .card-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.875rem;
}

.search-container {
  display: flex;
  gap: 1.5rem;
  align-items: flex-end;
  flex-wrap: wrap;
  padding: 1.5rem;
}

.search-group {
  flex: 1;
  min-width: 250px;
}

.search-group label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper .material-icons {
  position: absolute;
  left: 0.75rem;
  color: var(--text-color-light);
  font-size: 1.125rem;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: white;
  color: var(--text-color);
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.search-input::placeholder {
  color: var(--text-color-light);
}

.filter-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: white;
  color: var(--text-color);
  transition: var(--transition);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.nivel-filter {
  min-width: 180px;
}

/* Responsive para búsqueda */
@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
    gap: 1rem;
  }

  .search-group {
    min-width: 100%;
  }

  .results-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .results-actions {
    align-self: flex-start;
  }
}

/* Sección de resultados */
.results-section {
  margin-bottom: 1.5rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  padding: 1.5rem 0;
}

.results-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.solicitudes-count {
  font-size: 0.875rem;
  color: var(--text-color-light);
  font-weight: 400;
}

.solicitudes-count span {
  font-weight: 600;
  color: var(--primary-color);
}

.results-actions {
  flex-shrink: 0;
}

.btn-export {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-export:hover {
  background-color: #059669;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.btn-export .material-icons {
  font-size: 1.125rem;
}

/* Separación de la tabla */
.table-section {
  margin-top: 0;
}

.table-container {
  margin-bottom: 1.5rem;
  overflow-x: auto;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 500;
  font-size: 0.875rem;
  color: white;
  background: var(--primary-color);
  border: none;
}

.data-table th:first-child {
  border-radius: 0.5rem 0 0 0;
}

.data-table th:last-child {
  border-radius: 0 0.5rem 0 0;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
  color: var(--text-color);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:last-child td:first-child {
  border-radius: 0 0 0 0.5rem;
}

.data-table tr:last-child td:last-child {
  border-radius: 0 0 0.5rem 0;
}

.data-table tbody tr:hover {
  background-color: var(--bg-color-light);
}
  
/* Información de solicitudes */
.solicitud-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.solicitud-nombre {
  font-weight: 500;
  font-size: 0.95rem;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.solicitud-email,
.solicitud-telefono {
  font-size: 0.875rem;
  color: var(--text-color-light);
}

.solicitud-email {
  color: var(--primary-color);
}

/* Botones de acción */
.table-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: var(--transition);
}

.action-btn:hover {
  background-color: var(--bg-color-light);
}

.action-btn .material-icons {
  font-size: 1.125rem;
}

.view-btn .material-icons {
  color: var(--primary-color);
}

.view-btn:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.view-btn:hover .material-icons {
  color: white;
}

.mark-btn .material-icons {
  color: var(--success-color);
}

.mark-btn:hover {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.mark-btn:hover .material-icons {
  color: white;
}
  
.empty-table {
  padding: 2rem !important;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 1.5rem 0;
}

.empty-state .material-icons {
  font-size: 2.5rem;
  color: var(--text-color-light);
  margin-bottom: 1rem;
}

.empty-state p {
  margin-bottom: 1rem;
  color: var(--text-color);
  font-size: 0.875rem;
}

/* Paginación */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.items-per-page label {
  font-size: 0.875rem;
  color: var(--text-color);
}

.items-per-page select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0 0.5rem;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--bg-color-light);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  color: var(--text-color-light);
}

@media (max-width: 576px) {
  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }

  .items-per-page {
    width: 100%;
    justify-content: center;
  }

  .pagination {
    width: 100%;
    justify-content: center;
  }
}
  
/* Modal de Detalles */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow-y: auto;
  padding: 2rem 1rem;
  justify-content: center;
  align-items: flex-start;
}

.modal-overlay.active {
  display: flex;
}

.modal-content {
  width: 100%;
  max-width: 800px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: var(--transition);
}

.modal-close-btn:hover {
  background-color: var(--bg-color-light);
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.detail-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.detail-section h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}
  
.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-light);
  margin-bottom: 0.5rem;
}

.detail-value {
  font-weight: 500;
  color: var(--text-color);
}



/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-color-light);
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: white;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn-secondary:hover {
  background-color: var(--bg-color-light);
  border-color: var(--primary-color);
}