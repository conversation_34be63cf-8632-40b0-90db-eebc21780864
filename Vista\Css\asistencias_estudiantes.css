/* Estilos específicos para la página de asistencias de estudiantes */

:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Controles de fecha */
  .attendance-controls {
    margin-bottom: 20px;
  }
  
  .date-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .date-nav-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .date-nav-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .current-date {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    border-radius: 20px;
    background-color: white;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
  }
  
  .date-display {
    font-weight: 500;
    font-size: 1rem;
  }
  
  .calendar-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .calendar-btn:hover {
    color: var(--primary-color);
  }
  
  /* Tabla de asistencias */
  .attendance-table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 20px;
  }
  
  .attendance-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .attendance-table th,
  .attendance-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }
  
  .attendance-table th {
    background-color: var(--secondary-color);
    font-weight: 600;
    color: var(--text-color);
  }
  
  .attendance-table tr:last-child td {
    border-bottom: none;
  }
  
  .attendance-table tr:hover td {
    background-color: var(--secondary-color);
  }
  
  .student-col {
    width: 50%;
  }
  
  .status-col {
    width: 30%;
  }
  
  .actions-col {
    width: 20%;
  }
  
  .student-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .student-avatar.large {
    width: 80px;
    height: 80px;
  }
  
  .student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .student-details {
    display: flex;
    flex-direction: column;
  }
  
  .student-name {
    font-weight: 500;
    margin-bottom: 3px;
  }
  
  .student-id {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  /* Selector de estado de asistencia */
  .attendance-status-selector {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .status-option {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
  }
  
  .status-option.present {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border-color: transparent;
  }
  
  .status-option.present.active,
  .status-option.present:hover {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: var(--success-color);
  }
  
  .status-option.absent {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
    border-color: transparent;
  }
  
  .status-option.absent.active,
  .status-option.absent:hover {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: var(--danger-color);
  }
  
  .status-option.late {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border-color: transparent;
  }
  
  .status-option.late.active,
  .status-option.late:hover {
    background-color: rgba(255, 152, 0, 0.2);
    border-color: var(--warning-color);
  }
  
  .status-option.excused {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
    border-color: transparent;
  }
  
  .status-option.excused.active,
  .status-option.excused:hover {
    background-color: rgba(33, 150, 243, 0.2);
    border-color: var(--info-color);
  }
  
  /* Acciones de asistencia */
  .attendance-actions {
    display: flex;
    gap: 8px;
  }
  
  .attendance-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .attendance-action-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .attendance-action-btn.save-btn:hover {
    color: var(--success-color);
    border-color: var(--success-color);
  }
  
  .attendance-action-btn.reset-btn:hover {
    color: var(--warning-color);
    border-color: var(--warning-color);
  }
  
  /* Resumen de asistencias */
  .attendance-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
  }
  
  .summary-item {
    flex: 1;
    min-width: 150px;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
  }
  
  .summary-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .summary-icon.present {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .summary-icon.absent {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .summary-icon.late {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .summary-icon.excused {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
  }
  
  .summary-details {
    display: flex;
    flex-direction: column;
  }
  
  .summary-count {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 5px;
  }
  
  .summary-label {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  /* Historial de asistencias */
  .attendance-history {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .history-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .filter-group label {
    font-weight: 500;
  }
  
  .filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: white;
    color: var(--text-color);
    font-size: 0.95rem;
    outline: none;
    transition: var(--transition);
  }
  
  .filter-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  /* Calendario de asistencias */
  .history-calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
  }
  
  .calendar-day {
    aspect-ratio: 1 / 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .calendar-day:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  .calendar-day.has-class {
    background-color: var(--secondary-color);
  }
  
  .calendar-day.today {
    border-color: var(--primary-color);
    border-width: 2px;
  }
  
  .calendar-day.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .day-number {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .day-status {
    display: flex;
    gap: 3px;
  }
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .status-dot.present {
    background-color: var(--success-color);
  }
  
  .status-dot.absent {
    background-color: var(--danger-color);
  }
  
  .status-dot.late {
    background-color: var(--warning-color);
  }
  
  .status-dot.excused {
    background-color: var(--info-color);
  }
  
  /* Modal de calendario */
  .calendar-container {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
  }
  
  .calendar-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .calendar-title {
    font-weight: 600;
    font-size: 1.1rem;
  }
  
  .calendar-nav {
    display: flex;
    gap: 10px;
  }
  
  .calendar-nav-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .calendar-nav-btn:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .calendar-weekday {
    text-align: center;
    font-weight: 500;
    color: var(--text-light);
    padding: 5px 0;
    font-size: 0.9rem;
  }
  
  .calendar-date {
    aspect-ratio: 1 / 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.95rem;
  }
  
  .calendar-date:hover {
    background-color: var(--secondary-color);
  }
  
  .calendar-date.today {
    border: 2px solid var(--primary-color);
  }
  
  .calendar-date.selected {
    background-color: var(--primary-color);
    color: white;
  }
  
  .calendar-date.has-class {
    font-weight: 600;
  }
  
  .calendar-date.disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  .modal-small {
    max-width: 450px;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  /* Formulario */
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  .form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
  }
  
  .form-group input[type="text"],
  .form-group input[type="number"],
  .form-group input[type="date"],
  .form-group input[type="time"],
  .form-group input[type="url"],
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .checkbox-group {
    margin-top: 5px;
  }
  
  .checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    cursor: pointer;
  }
  
  .file-upload {
    position: relative;
  }
  
  .file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  
  .file-upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 15px;
    border: 1px dashed var(--border-color);
    border-radius: 5px;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .file-upload:hover .file-upload-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  /* Add styles for the general attendance actions */
  .attendance-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
  }

  .btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(42, 77, 183, 0.3);
  }

  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
  }

  .btn-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Estilos para form-actions */
  .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid var(--border-color);
  }

  @media (max-width: 768px) {
    .form-actions {
      flex-direction: column;
      gap: 15px;
    }

    .btn-primary,
    .btn-secondary {
      width: 100%;
      min-width: auto;
    }
  }
  
  .btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #d32f2f;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .attendance-summary {
      flex-wrap: wrap;
    }
  
    .history-calendar {
      grid-template-columns: repeat(7, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .date-selector {
      flex-direction: column;
      align-items: stretch;
    }
  
    .current-date {
      justify-content: space-between;
    }
  
    .attendance-status-selector {
      flex-wrap: wrap;
    }
  
    .history-filters {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .filter-group {
      width: 100%;
    }
  
    .filter-group select {
      flex: 1;
    }
  
    .history-calendar {
      grid-template-columns: repeat(7, 1fr);
      gap: 5px;
    }
  
    .day-number {
      font-size: 0.9rem;
    }
  }
  
  /* Add styles for success and reset messages */
  .success-message,
  .reset-message {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    border-radius: 5px;
    margin-top: 15px;
    animation: fadeIn 0.3s ease-out;
  }
  
  .success-message {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
  }

  .success-message .success-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .success-message .success-details strong {
    font-weight: 600;
    margin-bottom: 2px;
  }

  .success-message .success-details p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .success-message .success-details em {
    color: rgba(76, 175, 80, 0.8);
    font-style: italic;
  }
  
  .reset-message {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Add more spacing to attendance summary */
  .attendance-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
  }
  
  /* Estilos para el modal de detalle de historial */
  .history-detail-container {
    max-width: 800px;
    width: 95%;
  }
  
  .history-detail-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .history-detail-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }
  
  .history-detail-content {
    padding: 0;
  }
  
  .attendance-detail-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .attendance-detail-table th,
  .attendance-detail-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }
  
  .attendance-detail-table th {
    background-color: var(--secondary-color);
    font-weight: 600;
    color: var(--text-color);
  }
  
  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
  }
  
  .status-badge.present {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  .status-badge.absent {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
  }
  
  .status-badge.late {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
  }
  
  .status-badge.excused {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
  }
  
  .status-badge.none {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-light);
  }
  
  .action-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9rem;
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .change-status-btn .material-icons {
    font-size: 1rem;
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
  }
  
  /* Estilos para el modal de cambio de estado */
  .student-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .student-profile h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 5px 0;
  }
  
  .student-profile p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .status-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }
  
  