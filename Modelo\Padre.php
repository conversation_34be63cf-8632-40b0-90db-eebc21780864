<?php
require_once __DIR__ . '/Conexion.php';
require_once __DIR__ . '/Usuario.php';

/**
 * Modelo para manejar padres/apoderados
 */
class Padre {
    private $pdo;
    private $usuario;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->usuario = new Usuario();
    }

    /**
     * Crea un nuevo padre/apoderado
     * @param array $datosUsuario
     * @param array $datosPersona
     * @param array $datosPadre
     * @return int|false
     */
    public function crearPadre($datosUsuario, $datosPersona, $datosPadre) {
        try {
            $this->pdo->beginTransaction();

            // Crear usuario y persona
            $resultado = $this->usuario->crearUsuario($datosUsuario, $datosPersona);
            if (!$resultado) {
                throw new Exception("Error creando usuario base");
            }

            // Insertar datos específicos del padre
            $sql = "INSERT INTO padres (persona_id, tipo_apoderado) 
                    VALUES (:persona_id, :tipo_apoderado)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':persona_id', $resultado['persona_id']);
            $stmt->bindParam(':tipo_apoderado', $datosPadre['tipo_apoderado']);
            $stmt->execute();

            $padreId = $this->pdo->lastInsertId();

            $this->pdo->commit();
            return $padreId;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creando padre: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene todos los padres
     * @return array
     */
    public function obtenerTodos() {
        try {
            $sql = "SELECT pa.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, u.email, u.nombre_usuario
                    FROM padres pa
                    INNER JOIN personas p ON pa.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.activo = 1
                    ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo padres: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtiene padre por ID
     * @param int $id
     * @return array|false
     */
    public function obtenerPorId($id) {
        try {
            $sql = "SELECT pa.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, u.email, u.nombre_usuario
                    FROM padres pa
                    INNER JOIN personas p ON pa.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE pa.id = :id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo padre: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Asocia un padre con un estudiante
     * @param int $padreId
     * @param int $estudianteId
     * @return bool
     */
    public function asociarConEstudiante($padreId, $estudianteId) {
        try {
            $sql = "INSERT INTO padre_estudiante (padre_id, estudiante_id) VALUES (:padre_id, :estudiante_id)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':padre_id', $padreId);
            $stmt->bindParam(':estudiante_id', $estudianteId);
            $stmt->execute();

            return true;
        } catch (PDOException $e) {
            error_log("Error asociando padre con estudiante: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene los hijos de un padre
     * @param int $padreId
     * @return array
     */
    public function obtenerHijos($padreId) {
        try {
            $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno
                    FROM padre_estudiante pe
                    INNER JOIN estudiantes e ON pe.estudiante_id = e.id
                    INNER JOIN personas p ON e.persona_id = p.id
                    WHERE pe.padre_id = :padre_id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':padre_id', $padreId);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo hijos del padre: " . $e->getMessage());
            return [];
        }
    }
}
?>
