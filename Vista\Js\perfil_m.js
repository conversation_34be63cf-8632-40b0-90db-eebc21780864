document.addEventListener("DOMContentLoaded", () => {
    console.log('Script de perfil maestro cargado');

    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn");
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn");
    const modals = document.querySelectorAll(".modal-overlay");

    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
        button.addEventListener("click", () => {
            const section = button.getAttribute("data-section");
            const modal = document.getElementById(`edit-${section}-modal`);
            if (modal) {
                modal.classList.add("active");
                console.log('Modal mostrado:', section);
            }
        });
    });

    // Función para cerrar modales
    modalCloseButtons.forEach((button) => {
        button.addEventListener("click", () => {
            const modal = button.closest(".modal-overlay");
            if (modal) {
                modal.classList.remove("active");
            }
        });
    });

    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.classList.remove("active");
            }
        });
    });

    // Función global para abrir modal de foto
    window.abrirModalFoto = function() {
        const modal = document.getElementById('upload-photo-modal');
        if (modal) {
            modal.classList.add("active");
        }
    };

    // Función global para abrir modal de contraseña
    window.abrirModalContraseña = function() {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.classList.add("active");
        }
    };

    // Manejar subida de foto de perfil
    const uploadPhotoForm = document.getElementById('upload-photo-form');
    if (uploadPhotoForm) {
        uploadPhotoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('foto', formData.get('foto_perfil')); // Renombrar para el controlador

            fetch('../Controlador/PerfilController.php?action=subir_foto', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarNotificacion('Foto de perfil actualizada correctamente', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    mostrarNotificacion(data.error || 'Error desconocido', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarNotificacion('Error al subir la foto: ' + error.message, 'error');
            });
        });
    }

    // Vista previa de imagen
    const photoFile = document.getElementById('photo-file');
    const photoPreview = document.getElementById('photo-preview');
    const previewImage = document.getElementById('preview-image');

    if (photoFile) {
        photoFile.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    photoPreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Manejar cambio de contraseña
    const changePasswordForm = document.querySelector('#change-password-modal .edit-form');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (newPassword !== confirmPassword) {
                mostrarNotificacion('Las contraseñas nuevas no coinciden', 'error');
                return;
            }

            if (newPassword.length < 6) {
                mostrarNotificacion('La contraseña debe tener al menos 6 caracteres', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('password_actual', currentPassword);
            formData.append('password_nuevo', newPassword);
            formData.append('password_confirmar', confirmPassword);

            fetch('../Controlador/PerfilController.php?action=cambiar_password', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarNotificacion('Contraseña cambiada correctamente', 'success');
                    document.getElementById('change-password-modal').classList.remove("active");
                    changePasswordForm.reset();
                } else {
                    mostrarNotificacion(data.error || 'Error desconocido', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarNotificacion('Error al cambiar la contraseña: ' + error.message, 'error');
            });
        });
    }

    // Manejar formularios de edición
    const editForms = document.querySelectorAll('.edit-form:not(#upload-photo-form)');
    editForms.forEach((form) => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Validar que no se estén enviando datos de información profesional
            const camposProfesionales = ['especialidad', 'nivel_educativo', 'grado_tutor', 'cargo', 'departamento', 'tipo_apoderado'];
            let tieneDatosProfesionales = false;
            
            camposProfesionales.forEach(campo => {
                if (formData.has(campo) && formData.get(campo)) {
                    tieneDatosProfesionales = true;
                }
            });
            
            if (tieneDatosProfesionales) {
                mostrarNotificacion('La información profesional no puede ser editada desde el perfil', 'error');
                return;
            }

            fetch('../Controlador/PerfilController.php?action=actualizar', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarNotificacion('Perfil actualizado correctamente', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    mostrarNotificacion(data.error || 'Error desconocido', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarNotificacion('Error al actualizar el perfil: ' + error.message, 'error');
            });
        });
    });

    // Refuerzo: asegurar que el modal de edición de información personal se abra correctamente
    const editPersonalBtn = document.querySelector('.edit-section-btn[data-section="personal"]');
    const personalModal = document.getElementById('edit-personal-modal');
    if (editPersonalBtn && personalModal) {
        editPersonalBtn.addEventListener('click', () => {
            personalModal.classList.add('active');
        });
    }
    // Refuerzo: cerrar modal con botón de cerrar
    const closePersonalModalBtn = personalModal ? personalModal.querySelector('.modal-close-btn') : null;
    if (closePersonalModalBtn && personalModal) {
        closePersonalModalBtn.addEventListener('click', () => {
            personalModal.classList.remove('active');
        });
    }
    // Refuerzo: cerrar modal al hacer clic fuera del contenido
    if (personalModal) {
        personalModal.addEventListener('click', (e) => {
            if (e.target === personalModal) {
                personalModal.classList.remove('active');
            }
        });
    }
    // Refuerzo: asegurar que el formulario solo envíe los campos permitidos
    const personalForm = personalModal ? personalModal.querySelector('form.edit-form') : null;
    if (personalForm) {
        personalForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            // Solo permitir campos personales
            const camposPermitidos = ['nombres','apellido_paterno','apellido_materno','fecha_nacimiento','sexo','direccion','telefono'];
            for (let key of formData.keys()) {
                if (!camposPermitidos.includes(key)) {
                    formData.delete(key);
                }
            }
            fetch('../Controlador/PerfilController.php?action=actualizar', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarNotificacion('Información personal actualizada correctamente', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    mostrarNotificacion(data.error || 'Error desconocido', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarNotificacion('Error al actualizar la información: ' + error.message, 'error');
            });
        });
    }

    // Función para mostrar notificaciones
    function mostrarNotificacion(mensaje, tipo) {
        // Crear elemento de notificación
        const notificacion = document.createElement('div');
        notificacion.className = `notificacion ${tipo}`;
        notificacion.textContent = mensaje;
        
        // Estilos básicos
        notificacion.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        // Color según tipo
        if (tipo === 'success') {
            notificacion.style.backgroundColor = '#4caf50';
        } else if (tipo === 'error') {
            notificacion.style.backgroundColor = '#f44336';
        } else {
            notificacion.style.backgroundColor = '#2196f3';
        }
        
        // Agregar al DOM
        document.body.appendChild(notificacion);
        
        // Mostrar con animación
        setTimeout(() => {
            notificacion.style.transform = 'translateX(0)';
        }, 100);
        
        // Ocultar después de 3 segundos
        setTimeout(() => {
            notificacion.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notificacion.parentNode) {
                    notificacion.parentNode.removeChild(notificacion);
                }
            }, 300);
        }, 3000);
    }
});
  
  