/* Estilos específicos para la página de menú de tareas */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  .section-description {
    color: var(--text-light);
    margin-top: 5px;
    font-size: 1rem;
  }
  
  /* Contenedor de opciones de vista */
  .view-options-container {
    display: flex;
    gap: 30px;
    margin-top: 30px;
  }
  
  /* Tarjeta de opción de vista */
  .view-option-card {
    flex: 1;
    display: flex;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    cursor: pointer;
    min-height: 350px;
  }
  
  .view-option-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }
  
  /* Icono de la opción */
  .view-option-icon {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
  }
  
  .view-option-icon.crud {
    background-color: var(--success-color);
  }
  
  .view-option-icon .material-icons {
    font-size: 3rem;
  }
  
  /* Contenido de la opción */
  .view-option-content {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
  }
  
  .view-option-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--text-color);
  }
  
  .view-option-content p {
    font-size: 1rem;
    color: var(--text-light);
    margin: 0 0 20px 0;
    line-height: 1.5;
  }
  
  /* Lista de características */
  .view-features {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
  }
  
  .view-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-size: 0.95rem;
    color: var(--text-color);
  }
  
  .view-features li .material-icons {
    color: var(--primary-color);
    font-size: 1.2rem;
  }
  
  .view-option-card:nth-child(2) .view-features li .material-icons {
    color: var(--success-color);
  }
  
  /* Botón de selección */
  .view-option-btn {
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .view-option-btn:hover {
    background-color: #1e40af;
  }
  
  .view-option-btn.crud {
    background-color: var(--success-color);
  }
  
  .view-option-btn.crud:hover {
    background-color: #3d8b40;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .view-options-container {
      flex-direction: column;
    }
  
    .view-option-card {
      width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .view-option-card {
      flex-direction: column;
    }
  
    .view-option-icon {
      width: 100%;
      height: 100px;
    }
  }
  