document.addEventListener("DOMContentLoaded", function() {
    // Elementos del DOM
    const registerEntryBtn = document.getElementById("register-entry-btn");
    const registerExitBtn = document.getElementById("register-exit-btn");
    const statusValue = document.querySelector(".status-value");
    const currentTimeDisplay = document.querySelector(".current-time");
    const monthNavBtns = document.querySelectorAll(".month-nav-btn");
    const currentMonthDisplay = document.querySelector(".current-month");
    const attendanceFilter = document.getElementById("attendance-filter");
    const attendanceRows = document.querySelectorAll(".attendance-row");
    const newRequestBtn = document.querySelector(".new-request-btn");
    const justificationModal = document.getElementById("justification-modal");
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn");
    const justificationForm = document.querySelector(".justification-form");

    // Variables de estado
    let currentMonth = new Date();
    let hasRegisteredEntry = false;
    let entryTime = null;

    // Datos de ejemplo para diferentes meses
    const monthlyData = {
        "2025-03": {
            diasAsistidos: 22,
            faltas: 0,
            tardanzas: 1,
            salidasAnticipadas: 0,
            porcentaje: 98
        },
        "2025-02": {
            diasAsistidos: 20,
            faltas: 1,
            tardanzas: 2,
            salidasAnticipadas: 1,
            porcentaje: 91
        },
        "2025-01": {
            diasAsistidos: 18,
            faltas: 2,
            tardanzas: 0,
            salidasAnticipadas: 0,
            porcentaje: 90
        }
    };

    // Inicializar la aplicación
    init();

    function init() {
        updateClock();
        setInterval(updateClock, 1000);
        setupEventListeners();
        updateMonthDisplay();
        updateSummaryStats();
    }

    function setupEventListeners() {
        // Botones de registro de asistencia
        registerEntryBtn.addEventListener("click", registerEntry);
        registerExitBtn.addEventListener("click", registerExit);

        // Navegación de meses
        monthNavBtns.forEach((btn, index) => {
            btn.addEventListener("click", () => {
                if (index === 0) {
                    // Botón anterior
                    currentMonth.setMonth(currentMonth.getMonth() - 1);
                } else {
                    // Botón siguiente
                    currentMonth.setMonth(currentMonth.getMonth() + 1);
                }
                updateMonthDisplay();
                updateSummaryStats();
            });
        });

        // Filtro de asistencia
        attendanceFilter.addEventListener("change", filterAttendanceRecords);

        // Modal de justificación
        newRequestBtn.addEventListener("click", openJustificationModal);
        modalCloseBtns.forEach(btn => {
            btn.addEventListener("click", closeJustificationModal);
        });

        // Formulario de justificación
        justificationForm.addEventListener("submit", submitJustification);

        // Cerrar modal al hacer clic fuera
        justificationModal.addEventListener("click", (e) => {
            if (e.target === justificationModal) {
                closeJustificationModal();
            }
        });
    }

    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString("es-ES", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit"
        });
        currentTimeDisplay.textContent = timeString;
    }

    function registerEntry() {
        const now = new Date();
        entryTime = now.toLocaleTimeString("es-ES", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit"
        });

        // Actualizar estado
        hasRegisteredEntry = true;
        statusValue.textContent = `Entrada registrada a las ${entryTime}`;
        statusValue.style.color = "#4CAF50";

        // Cambiar estado de botones
        registerEntryBtn.disabled = true;
        registerEntryBtn.style.opacity = "0.5";
        registerExitBtn.disabled = false;
        registerExitBtn.style.opacity = "1";

        // Mostrar mensaje de confirmación
        showSuccessMessage("Entrada registrada correctamente");
    }

    function registerExit() {
        const now = new Date();
        const exitTime = now.toLocaleTimeString("es-ES", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit"
        });

        // Actualizar estado
        hasRegisteredEntry = false;
        statusValue.textContent = `Jornada completada (${entryTime} - ${exitTime})`;
        statusValue.style.color = "#2196F3";

        // Cambiar estado de botones
        registerExitBtn.disabled = true;
        registerExitBtn.style.opacity = "0.5";
        registerEntryBtn.disabled = false;
        registerEntryBtn.style.opacity = "1";

        // Mostrar mensaje de confirmación
        showSuccessMessage("Salida registrada correctamente");

        // Resetear para el siguiente día (opcional)
        setTimeout(() => {
            statusValue.textContent = "Pendiente de registro";
            statusValue.style.color = "#FF9800";
        }, 5000);
    }

    function showSuccessMessage(message) {
        // Crear elemento de mensaje
        const messageElement = document.createElement("div");
        messageElement.className = "success-message";
        messageElement.innerHTML = `
            <span class="material-icons">check_circle</span>
            <span>${message}</span>
        `;

        // Agregar estilos
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;

        // Agregar al DOM
        document.body.appendChild(messageElement);

        // Remover después de 3 segundos
        setTimeout(() => {
            messageElement.style.animation = "slideOut 0.3s ease-in";
            setTimeout(() => {
                document.body.removeChild(messageElement);
            }, 300);
        }, 3000);
    }

    function updateMonthDisplay() {
        const monthNames = [
            "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
            "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
        ];
        
        const monthText = `${monthNames[currentMonth.getMonth()]} ${currentMonth.getFullYear()}`;
        currentMonthDisplay.textContent = monthText;
    }

    function updateSummaryStats() {
        const monthKey = `${currentMonth.getFullYear()}-${String(currentMonth.getMonth() + 1).padStart(2, '0')}`;
        const data = monthlyData[monthKey] || {
            diasAsistidos: 0,
            faltas: 0,
            tardanzas: 0,
            salidasAnticipadas: 0,
            porcentaje: 0
        };

        // Actualizar estadísticas
        const statItems = document.querySelectorAll(".stat-item");
        statItems[0].querySelector(".stat-value").textContent = data.diasAsistidos;
        statItems[1].querySelector(".stat-value").textContent = data.faltas;
        statItems[2].querySelector(".stat-value").textContent = data.tardanzas;
        statItems[3].querySelector(".stat-value").textContent = data.salidasAnticipadas;
        statItems[4].querySelector(".stat-value").textContent = `${data.porcentaje}%`;
    }

    function filterAttendanceRecords() {
        const filterValue = attendanceFilter.value;
        
        attendanceRows.forEach(row => {
            const statusBadge = row.querySelector(".status-badge");
            if (!statusBadge) return;

            const statusClass = statusBadge.className;
            let shouldShow = true;

            switch (filterValue) {
                case "present":
                    shouldShow = statusClass.includes("present");
                    break;
                case "late":
                    shouldShow = statusClass.includes("late");
                    break;
                case "absent":
                    shouldShow = statusClass.includes("absent");
                    break;
                case "early-exit":
                    shouldShow = statusClass.includes("early-exit");
                    break;
                case "all":
                default:
                    shouldShow = true;
                    break;
            }

            row.style.display = shouldShow ? "" : "none";
        });
    }

    function openJustificationModal() {
        justificationModal.classList.add("active");
        // Limpiar formulario
        justificationForm.reset();
    }

    function closeJustificationModal() {
        justificationModal.classList.remove("active");
    }

    function submitJustification(e) {
        e.preventDefault();
        
        const formData = new FormData(justificationForm);
        const date = document.getElementById("justification-date").value;
        const type = document.getElementById("justification-type").value;
        const reason = document.getElementById("justification-reason").value;

        // Simular envío
        showSuccessMessage("Solicitud de justificación enviada correctamente");
        closeJustificationModal();
        
        // Aquí se podría agregar la lógica para enviar al servidor
        console.log("Justificación enviada:", { date, type, reason });
    }

    // Agregar estilos CSS para las animaciones
    const style = document.createElement("style");
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .modal-overlay.active {
            display: flex !important;
        }
    `;
    document.head.appendChild(style);
});
