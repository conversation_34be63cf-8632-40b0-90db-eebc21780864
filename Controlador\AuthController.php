<?php
session_start();
require_once '../Modelo/Usuario.php';

/**
 * Controlador para manejar la autenticación de usuarios
 */
class AuthController {
    private $usuario;

    public function __construct() {
        $this->usuario = new Usuario();
    }

    /**
     * Procesa el login del usuario
     */
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nombreUsuario = trim($_POST['usuario'] ?? '');
            $password = trim($_POST['password'] ?? '');

            // Validar campos requeridos
            if (empty($nombreUsuario) || empty($password)) {
                $this->redirectWithError('Por favor, complete todos los campos.');
                return;
            }

            // Verificar si el usuario existe
            $usuarioExiste = $this->verificarUsuarioExiste($nombreUsuario);

            if (!$usuarioExiste) {
                $this->redirectWithError('El usuario "' . $nombreUsuario . '" no existe en el sistema.');
                return;
            }

            // Intentar autenticar
            $datosUsuario = $this->usuario->autenticar($nombreUsuario, $password);

            if ($datosUsuario) {
                // Obtener información específica del rol
                $informacionRol = $this->usuario->obtenerInformacionPorRol($datosUsuario['id'], $datosUsuario['rol']);
                
                // Guardar datos en sesión
                $_SESSION['usuario_id'] = $datosUsuario['id'];
                $_SESSION['nombre_usuario'] = $datosUsuario['nombre_usuario'];
                $_SESSION['email'] = $datosUsuario['email'];
                $_SESSION['rol'] = $datosUsuario['rol'];
                $_SESSION['nombres'] = $datosUsuario['nombres'];
                $_SESSION['apellido_paterno'] = $datosUsuario['apellido_paterno'];
                $_SESSION['apellido_materno'] = $datosUsuario['apellido_materno'];
                $_SESSION['foto_perfil'] = $datosUsuario['nombre_foto'];
                $_SESSION['informacion_rol'] = $informacionRol;
                $_SESSION['ultimo_acceso'] = time();

                // Redirigir según el rol
                $this->redirectByRole($datosUsuario['rol']);
            } else {
                $this->redirectWithError('La contraseña ingresada es incorrecta.');
            }
        } else {
            // Si no es POST, redirigir al login
            header('Location: ../Vista/intranet.php');
            exit();
        }
    }

    /**
     * Redirige al usuario según su rol
     * @param string $rol
     */
    private function redirectByRole($rol) {
        switch ($rol) {
            case 'administrador':
                header('Location: ../Vista/inicio_a.php');
                break;
            case 'maestro':
                header('Location: ../Vista/inicio_m.php');
                break;
            case 'padre':
                header('Location: ../Vista/inicio_p.php');
                break;
            case 'estudiante':
                header('Location: ../Vista/inicio_e.php');
                break;
            default:
                $this->redirectWithError('Rol de usuario no válido.');
                break;
        }
        exit();
    }

    /**
     * Verifica si un usuario existe en el sistema
     * @param string $nombreUsuario
     * @return bool
     */
    private function verificarUsuarioExiste($nombreUsuario) {
        try {
            $sql = "SELECT id FROM usuarios WHERE nombre_usuario = :nombre_usuario";
            $stmt = $this->usuario->pdo->prepare($sql);
            $stmt->bindParam(':nombre_usuario', $nombreUsuario);
            $stmt->execute();

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error verificando usuario: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Redirige con mensaje de error
     * @param string $mensaje
     */
    private function redirectWithError($mensaje) {
        $_SESSION['error_login'] = $mensaje;
        header('Location: ../Vista/intranet.php');
        exit();
    }

    /**
     * Cierra la sesión del usuario
     */
    public function logout() {
        session_start();

        // Limpiar todas las variables de sesión
        session_unset();

        // Destruir la sesión
        session_destroy();

        // Eliminar la cookie de sesión si existe
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time()-3600, '/');
        }

        // Iniciar una nueva sesión limpia
        session_start();

        // Agregar mensaje de confirmación
        $_SESSION['logout_success'] = 'Sesión cerrada exitosamente.';

        header('Location: ../Vista/intranet.php');
        exit();
    }

    /**
     * Verifica si el usuario está autenticado
     * @return bool
     */
    public static function estaAutenticado() {
        return isset($_SESSION['usuario_id']) && !empty($_SESSION['usuario_id']);
    }

    /**
     * Verifica si el usuario tiene un rol específico
     * @param string $rol
     * @return bool
     */
    public static function tieneRol($rol) {
        return self::estaAutenticado() && $_SESSION['rol'] === $rol;
    }

    /**
     * Verifica si el usuario tiene uno de los roles especificados
     * @param array $roles
     * @return bool
     */
    public static function tieneAlgunRol($roles) {
        return self::estaAutenticado() && in_array($_SESSION['rol'], $roles);
    }

    /**
     * Protege una página requiriendo autenticación
     * @param array $rolesPermitidos (opcional)
     */
    public static function protegerPagina($rolesPermitidos = []) {
        if (!self::estaAutenticado()) {
            header('Location: intranet.php');
            exit();
        }

        if (!empty($rolesPermitidos) && !self::tieneAlgunRol($rolesPermitidos)) {
            header('Location: intranet.php');
            exit();
        }
    }

    /**
     * Obtiene los datos del usuario actual
     * @return array|null
     */
    public static function obtenerUsuarioActual() {
        if (!self::estaAutenticado()) {
            return null;
        }

        return [
            'id' => $_SESSION['usuario_id'],
            'nombre_usuario' => $_SESSION['nombre_usuario'],
            'email' => $_SESSION['email'],
            'rol' => $_SESSION['rol'],
            'nombres' => $_SESSION['nombres'],
            'apellido_paterno' => $_SESSION['apellido_paterno'],
            'apellido_materno' => $_SESSION['apellido_materno'],
            'foto_perfil' => $_SESSION['foto_perfil'],
            'informacion_rol' => $_SESSION['informacion_rol'] ?? null
        ];
    }
}

// Procesar la acción solicitada
if (isset($_GET['action'])) {
    $auth = new AuthController();
    
    switch ($_GET['action']) {
        case 'login':
            $auth->login();
            break;
        case 'logout':
            $auth->logout();
            break;
        default:
            header('Location: ../Vista/intranet.php');
            exit();
    }
} else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Si es POST sin action, asumir que es login
    $auth = new AuthController();
    $auth->login();
}
?>
