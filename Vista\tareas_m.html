<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Tareas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_m.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                  </div>
                  <div class="user-details">
                      <h3>Carlos García</h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.html">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.html">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.html">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.html">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_m.html" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1>Matemáticas</h1>
                          <p>5° Primaria</p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <div class="schedule-day">
                              <span class="day-label">Lunes</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                          <div class="schedule-day">
                              <span class="day-label">Miércoles</span>
                              <span class="day-time">8:00 - 9:30</span>
                          </div>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.html" class="course-tab">Contenido</a>
                  <a href="tareas_m.html" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.html" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Tareas del curso</h2>
                      <div class="section-actions">
                          <div class="search-container">
                              <input type="text" class="search-input" placeholder="Buscar tarea...">
                              <span class="material-icons search-icon">search</span>
                          </div>
                      </div>
                  </div>
                  
                  <div class="tasks-container">
                      <!-- Semana 1 -->
                      <div class="week-tasks">
                          <div class="week-header">
                              <h3>Semana 1: Introducción a las fracciones</h3>
                              <span class="week-dates">15/03/2025 - 21/03/2025</span>
                          </div>
                          
                          <div class="task-card">
                              <div class="task-header">
                                  <h3 class="task-title">Ejercicios de Fracciones</h3>
                                  <span class="task-badge pending">5 pendientes</span>
                              </div>
                              <div class="task-details">
                                  <div class="task-meta">
                                      <div class="meta-item">
                                          <span class="material-icons">event</span>
                                          <span>Fecha límite: 18/03/2025</span>
                                      </div>
                                      <div class="meta-item">
                                          <span class="material-icons">grade</span>
                                          <span>10 puntos</span>
                                      </div>
                                  </div>
                                  <p class="task-description">Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                              </div>
                              <div class="task-actions">
                                  <button class="task-action-btn view-btn" data-task-id="1">
                                      <span class="material-icons">visibility</span>
                                      Ver tarea
                                  </button>
                                  <button class="task-action-btn submissions-btn" data-task-id="1">
                                      <span class="material-icons">assignment_turned_in</span>
                                      Ver entregas
                                  </button>
                              </div>
                          </div>
                          
                          <div class="task-card">
                              <div class="task-header">
                                  <h3 class="task-title">Representación gráfica de fracciones</h3>
                                  <span class="task-badge graded">Calificada</span>
                              </div>
                              <div class="task-details">
                                  <div class="task-meta">
                                      <div class="meta-item">
                                          <span class="material-icons">event</span>
                                          <span>Fecha límite: 10/03/2025</span>
                                      </div>
                                      <div class="meta-item">
                                          <span class="material-icons">grade</span>
                                          <span>10 puntos</span>
                                      </div>
                                  </div>
                                  <p class="task-description">Representar gráficamente las fracciones indicadas.</p>
                              </div>
                              <div class="task-actions">
                                  <button class="task-action-btn view-btn" data-task-id="2">
                                      <span class="material-icons">visibility</span>
                                      Ver tarea
                                  </button>
                                  <button class="task-action-btn submissions-btn" data-task-id="2">
                                      <span class="material-icons">assignment_turned_in</span>
                                      Ver entregas
                                  </button>
                              </div>
                          </div>
                      </div>
                      
                      <!-- Semana 2 -->
                      <div class="week-tasks">
                          <div class="week-header">
                              <h3>Semana 2: Operaciones con fracciones</h3>
                              <span class="week-dates">22/03/2025 - 28/03/2025</span>
                          </div>
                          
                          <div class="task-card">
                              <div class="task-header">
                                  <h3 class="task-title">Multiplicación de fracciones</h3>
                                  <span class="task-badge pending">8 pendientes</span>
                              </div>
                              <div class="task-details">
                                  <div class="task-meta">
                                      <div class="meta-item">
                                          <span class="material-icons">event</span>
                                          <span>Fecha límite: 25/03/2025</span>
                                      </div>
                                      <div class="meta-item">
                                          <span class="material-icons">grade</span>
                                          <span>15 puntos</span>
                                      </div>
                                  </div>
                                  <p class="task-description">Ejercicios de multiplicación de fracciones.</p>
                              </div>
                              <div class="task-actions">
                                  <button class="task-action-btn view-btn" data-task-id="3">
                                      <span class="material-icons">visibility</span>
                                      Ver tarea
                                  </button>
                                  <button class="task-action-btn submissions-btn" data-task-id="3">
                                      <span class="material-icons">assignment_turned_in</span>
                                      Ver entregas
                                  </button>
                              </div>
                          </div>

                          <div class="task-card exam-card">
                              <div class="task-header">
                                  <div class="task-title-with-icon">
                                      <span class="material-icons exam-icon">quiz</span>
                                      <h3 class="task-title">Examen: Evaluación de fracciones</h3>
                                  </div>
                                  <span class="task-badge active">5 pendientes</span>
                              </div>
                              <div class="task-details">
                                  <div class="task-meta">
                                      <div class="meta-item">
                                          <span class="material-icons">event</span>
                                          <span>Fecha límite: 28/03/2025</span>
                                      </div>
                                      <div class="meta-item">
                                          <span class="material-icons">grade</span>
                                          <span>20 puntos</span>
                                      </div>
                                  </div>
                                  <p class="task-description">Examen sobre conceptos básicos y operaciones con fracciones.</p>
                              </div>
                              <div class="task-actions">
                                  <button class="task-action-btn view-btn" data-task-id="4">
                                      <span class="material-icons">visibility</span>
                                      Ver examen
                                  </button>
                                  <button class="task-action-btn submissions-btn" data-task-id="4">
                                      <span class="material-icons">assignment_turned_in</span>
                                      Ver entregas
                                  </button>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
          </div>
      </main>
  </div>
  
  <!-- Modal para ver tarea -->
  <div id="task-view-modal" class="modal-overlay">
      <div class="modal-content assignment-container">
          <div class="assignment-header">
              <h1 class="assignment-title">
                  <span class="material-icons">assignment</span>
                  Tarea: Ejercicios de Fracciones
              </h1>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="assignment-content">
              <div class="assignment-section">
                  <h2 class="assignment-section-title">Descripción de la tarea</h2>
                  <p class="assignment-description">Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                  <img src="/placeholder.svg?height=300&width=500" alt="Ejercicios de sumas y restas" class="assignment-image">
              </div>
              
              <div class="assignment-section">
                  <div class="assignment-meta">
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">event</span>
                              Fecha de entrega
                          </div>
                          <div class="meta-value">18/03/2025, 11:59 PM</div>
                      </div>
                      
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">grade</span>
                              Puntos
                          </div>
                          <div class="meta-value">10 puntos máximos</div>
                      </div>
                      
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">person</span>
                              Asignado por
                          </div>
                          <div class="meta-value">Prof. Carlos García</div>
                      </div>
                  </div>
              </div>

              <div class="assignment-section">
                  <div class="submission-section">
                      <h2 class="submission-title">Estado de la tarea</h2>
                      <div class="task-status-info">
                          <div class="status-item">
                              <span class="material-icons status-icon">schedule</span>
                              <div class="status-content">
                                  <span class="status-label">Estado actual</span>
                                  <span class="status-value">Activa - Los estudiantes pueden entregar hasta el 18/03/2025</span>
                              </div>
                          </div>
                      </div>
                      <div class="submission-options">
                          <div class="form-actions">
                              <button type="button" class="btn-secondary modal-close-btn">Cerrar</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  
  <!-- Modal para ver entregas de tarea -->
  <div id="submissions-modal" class="modal-overlay">
      <div class="modal-content submissions-container">
          <div class="modal-header">
              <h3>Entregas: Ejercicios de Fracciones</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <div class="submissions-stats">
                  <div class="stat-item">
                      <div class="stat-value">5</div>
                      <div class="stat-label">Entregadas</div>
                  </div>
                  <div class="stat-item">
                      <div class="stat-value">2</div>
                      <div class="stat-label">Tardías</div>
                  </div>
                  <div class="stat-item">
                      <div class="stat-value">5</div>
                      <div class="stat-label">Pendientes</div>
                  </div>
                  <div class="stat-item">
                      <div class="stat-value">3</div>
                      <div class="stat-label">Calificadas</div>
                  </div>
              </div>
              
              <div class="submissions-filters">
                  <div class="filter-group">
                      <label for="submission-filter">Mostrar:</label>
                      <select id="submission-filter">
                          <option value="all">Todas las entregas</option>
                          <option value="submitted">Entregadas</option>
                          <option value="late">Tardías</option>
                          <option value="pending">Pendientes</option>
                          <option value="graded">Calificadas</option>
                      </select>
                  </div>
                  <div class="search-container">
                      <input type="text" class="search-input" placeholder="Buscar estudiante...">
                      <span class="material-icons search-icon">search</span>
                  </div>
              </div>
              
              <div class="submissions-list">
                  <!-- Los elementos se cargarán dinámicamente con JavaScript -->
              </div>
          </div>
      </div>
  </div>
  
  <!-- Modal para ver entrega individual -->
  <div id="student-submission-modal" class="modal-overlay">
      <div class="modal-content submission-container">
          <div class="modal-header">
              <h3>Entrega: Ejercicios de Fracciones</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <div class="submission-info">
                  <div class="student-profile">
                      <div class="student-avatar large">
                          <img src="/placeholder.svg?height=80&width=80" alt="Ana García">
                      </div>
                      <div class="student-details">
                          <h4>Ana García</h4>
                      </div>
                  </div>
                  
                  <div class="submission-meta">
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">event</span>
                              Fecha de entrega
                          </div>
                          <div class="meta-value">17/03/2025, 10:45 AM</div>
                      </div>
                      <div class="meta-item">
                          <div class="meta-label">
                              <span class="material-icons">schedule</span>
                              Estado
                          </div>
                          <div class="meta-value">
                              <span class="submission-status on-time">A tiempo</span>
                          </div>
                      </div>
                  </div>
              </div>
              
              <div class="submission-content">
                  <h4>Archivos entregados</h4>
                  <div class="submission-files">
                      <div class="file-item">
                          <div class="file-icon pdf">
                              <span class="material-icons">picture_as_pdf</span>
                          </div>
                          <div class="file-details">
                              <span class="file-name">Ejercicios_Fracciones_AnaGarcia.pdf</span>
                              <span class="file-size">1.2 MB</span>
                          </div>
                          <div class="file-actions">
                              <button class="file-action-btn download-btn" title="Descargar">
                                  <span class="material-icons">download</span>
                              </button>
                              <button class="file-action-btn preview-btn" title="Vista previa">
                                  <span class="material-icons">visibility</span>
                              </button>
                          </div>
                      </div>
                  </div>
                  
                  <div class="submission-comments">
                      <h4>Comentarios del estudiante</h4>
                      <div class="comment-text">
                          <p>Profesor, adjunto mi tarea de ejercicios de fracciones. Tuve algunas dudas en los ejercicios 5 y 7, pero intenté resolverlos lo mejor posible.</p>
                      </div>
                  </div>
              </div>
              
              <div class="grading-section">
                  <h4>Calificación</h4>
                  <div class="grade-form">
                      <div class="form-group">
                          <label for="grade-points">Puntos</label>
                          <div class="points-input">
                              <input type="number" id="grade-points" min="0" max="10" value="8">
                              <span class="points-max">/ 10</span>
                          </div>
                      </div>
                      
                      <div class="form-group">
                          <label for="grade-feedback">Retroalimentación</label>
                          <textarea id="grade-feedback" rows="4" placeholder="Escribe tus comentarios para el estudiante...">Buen trabajo Ana. Los ejercicios están bien resueltos en general. En el ejercicio 5 te faltó simplificar la fracción final, y en el 7 hay un pequeño error en el proceso. Revisa las correcciones.</textarea>
                      </div>
                      
                      <div class="form-actions">
                          <button class="btn-secondary modal-close-btn">Cancelar</button>
                          <button class="btn-primary save-grade-btn" data-student-id="1" data-task-id="1">Guardar calificación</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/tareas_m.js"></script>
</body>
</html>
