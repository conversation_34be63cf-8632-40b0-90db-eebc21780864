document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const loginForm = document.getElementById("login-form")
    const togglePasswordBtn = document.querySelector(".toggle-password")
    const passwordInput = document.getElementById("password")
  
    // Toggle para mostrar/ocultar contraseña
    togglePasswordBtn.addEventListener("click", function () {
      const icon = this.querySelector(".material-icons")
  
      if (passwordInput.type === "password") {
        passwordInput.type = "text"
        icon.textContent = "visibility"
      } else {
        passwordInput.type = "password"
        icon.textContent = "visibility_off"
      }
    })
  
    // Envío del formulario de login
    loginForm.addEventListener("submit", (e) => {
      e.preventDefault()
  
      const usuario = document.getElementById("usuario").value
      const password = passwordInput.value
  
      // Aquí iría la lógica para autenticar al usuario
      // Por ahora, solo simulamos un inicio de sesión
  
      // Mostramos un mensaje de carga
      const loginBtn = document.querySelector(".login-btn")
      const originalBtnText = loginBtn.innerHTML
      loginBtn.innerHTML = '<span class="material-icons rotating">sync</span> Iniciando sesión...'
      loginBtn.disabled = true
  
      // Simulamos un tiempo de carga
      setTimeout(() => {
        // Redirigimos a una página de dashboard (que no existe aún)
        alert("Inicio de sesión exitoso. En un sistema real, serías redirigido al dashboard.")
  
        // Restauramos el botón
        loginBtn.innerHTML = originalBtnText
        loginBtn.disabled = false
  
        // Reseteamos el formulario
        loginForm.reset()
      }, 2000)
    })
  
    // Añadimos una clase CSS para la animación de rotación
    const style = document.createElement("style")
    style.textContent = `
          .rotating {
              animation: rotate 1.5s linear infinite;
          }
          
          @keyframes rotate {
              from {
                  transform: rotate(0deg);
              }
              to {
                  transform: rotate(360deg);
              }
          }
      `
    document.head.appendChild(style)
  })
  
  