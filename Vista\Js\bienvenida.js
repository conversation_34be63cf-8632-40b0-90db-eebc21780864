document.addEventListener('DOMContentLoaded', function() {
    // Menu Toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const navList = document.querySelector('.nav-list');
    const navOverlay = document.querySelector('.nav-overlay');

    function toggleMenu() {
        menuToggle.classList.toggle('active');
        navList.classList.toggle('active');
        navOverlay.classList.toggle('active');
        document.body.style.overflow = navList.classList.contains('active') ? 'hidden' : '';
        
        // Cambiar el ícono del menú
        const icon = menuToggle.querySelector('.material-icons');
        if (navList.classList.contains('active')) {
            icon.textContent = 'close';
        } else {
            icon.textContent = 'menu';
        }
    }

    menuToggle.addEventListener('click', toggleMenu);
    navOverlay.addEventListener('click', toggleMenu);

    // Cerrar menú al hacer clic en un enlace
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navList.classList.contains('active')) {
                toggleMenu();
            }
        });
    });

    // Slider functionality
    const slides = document.querySelectorAll('.slide');
    const prevButton = document.querySelector('.prev-btn');
    const nextButton = document.querySelector('.next-btn');

    let currentSlide = 0;
    let isTransitioning = false;
    let slideInterval;  // Intervalo para el auto slide
    let manualSlideTimeout; // Timeout para los 8 segundos cuando el usuario hace clic

    // Función para actualizar las diapositivas con el tiempo de transición
    function updateSlides(isManual = false) {
        if (isTransitioning) return;
        isTransitioning = true;

        slides.forEach(slide => slide.classList.remove('active'));
        slides[currentSlide].classList.add('active');

        // Establecer el tiempo de transición (2 segundos para manual, 8 segundos para automático)
        const transitionDuration = 2000; // 2 segundos para manual

        // Esperar a que termine la transición
        setTimeout(() => {
            isTransitioning = false;
        }, transitionDuration);
    }

    // Función para avanzar al siguiente slide
    function nextSlide() {
        if (isTransitioning) return;
        currentSlide = (currentSlide + 1) % slides.length;
        updateSlides(true); // Pasamos 'true' para indicar que es un cambio manual

        // Detenemos el auto slide y reiniciamos el intervalo automático después de 8 segundos
        clearInterval(slideInterval);
        clearTimeout(manualSlideTimeout);  // Cancelar cualquier timeout anterior
        manualSlideTimeout = setTimeout(() => {
            slideInterval = setInterval(nextSlide, 8000); // Reinicia el intervalo automático con un intervalo de 8 segundos
        }, 8000);  // 8 segundos de espera antes de reiniciar el intervalo
    }

    // Función para retroceder al slide anterior
    function prevSlide() {
        if (isTransitioning) return;
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        updateSlides(true); // Pasamos 'true' para indicar que es un cambio manual

        // Detenemos el auto slide y reiniciamos el intervalo automático después de 8 segundos
        clearInterval(slideInterval);
        clearTimeout(manualSlideTimeout);  // Cancelar cualquier timeout anterior
        manualSlideTimeout = setTimeout(() => {
            slideInterval = setInterval(nextSlide, 8000); // Reinicia el intervalo automático con un intervalo de 8 segundos
        }, 8000);  // 8 segundos de espera antes de reiniciar el intervalo
    }

    // Event listeners para los botones de navegación
    if (prevButton && nextButton) {
        prevButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevenir comportamiento por defecto
            prevSlide();
        });
        
        nextButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevenir comportamiento por defecto
            nextSlide();
        });
    }

    // Auto slide (cambio automático de diapositivas cada 8 segundos)
    slideInterval = setInterval(nextSlide, 8000);

    // Pausar el auto slide cuando el mouse está sobre el slider
    const sliderContainer = document.querySelector('.slider-container');
    if (sliderContainer) {
        sliderContainer.addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });

        sliderContainer.addEventListener('mouseleave', () => {
            slideInterval = setInterval(nextSlide, 8000);
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') prevSlide();
        if (e.key === 'ArrowRight') nextSlide();
    });

    // Prevenir el scroll cuando el menú móvil está abierto
    document.addEventListener('touchmove', function(e) {
        if (navList.classList.contains('active')) {
            e.preventDefault();
        }
    }, { passive: false });
});
