/* Estilos específicos para la sección de anuncios (vista de padres) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  
    /* Categorías de anuncios */
    --categoria-general: #2a4db7;
    --categoria-academico: #4caf50;
    --categoria-actividades: #ff9800;
    --categoria-administrativo: #9c27b0;
  }
  
  /* Filtros de anuncios */
  .anuncios-filtros {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
  }
  
  .filtros-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }
  
  .filtro-busqueda {
    flex: 2;
    min-width: 250px;
  }
  
  .input-group {
    display: flex;
    align-items: center;
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 0 15px;
    border: 1px solid var(--border-color);
  }
  
  .input-group .material-icons {
    color: var(--text-light);
    margin-right: 10px;
  }
  
  .input-group input {
    width: 100%;
    padding: 12px 0;
    border: none;
    background: transparent;
    outline: none;
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
    transition: all 0.3s ease;
  }

  .input-group input:focus {
    color: var(--primary-color);
  }

  .input-group.searching {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .filtro-categoria,
  .filtro-fecha {
    flex: 1;
    min-width: 180px;
  }
  
  .filtro-categoria select,
  .filtro-fecha select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--secondary-color);
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
    outline: none;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
  }
  
  /* Lista de anuncios */
  .anuncios-lista {
    margin-bottom: 30px;
  }
  
  .anuncios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    transition: opacity 0.3s ease;
  }
  
  .anuncio-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  
  .anuncio-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .anuncio-imagen {
    height: 200px;
    overflow: hidden;
  }
  
  .anuncio-imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
  }
  
  .anuncio-card:hover .anuncio-imagen img {
    transform: scale(1.05);
  }
  
  .anuncio-contenido {
    padding: 20px;
  }
  
  .anuncio-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }
  
  .anuncio-fecha {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .anuncio-categoria {
    font-size: 0.85rem;
    padding: 3px 10px;
    border-radius: 20px;
    color: white;
    font-weight: 500;
  }
  
  .categoria-general {
    background-color: var(--categoria-general);
  }
  
  .categoria-academico {
    background-color: var(--categoria-academico);
  }
  
  .categoria-actividades {
    background-color: var(--categoria-actividades);
  }
  
  .categoria-administrativo {
    background-color: var(--categoria-administrativo);
  }
  
  .anuncio-titulo {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-color);
    line-height: 1.4;
  }
  
  .anuncio-extracto {
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.5;
    font-size: 0.95rem;
  }
  
  .anuncio-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    transition: color 0.3s;
  }
  
  .anuncio-link:hover {
    color: #1e40af;
  }
  
  .anuncio-link .material-icons {
    font-size: 1.1rem;
    margin-left: 5px;
    transition: transform 0.3s;
  }
  
  .anuncio-link:hover .material-icons {
    transform: translateX(3px);
  }
  
  /* Mensaje de no resultados */
  .no-resultados {
    text-align: center;
    padding: 50px 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow-sm);
  }
  
  .no-resultados .material-icons {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 15px;
  }
  
  .no-resultados p {
    color: var(--text-light);
    font-size: 1.1rem;
  }
  
  /* Paginación */
  .paginacion {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 30px;
    margin-bottom: 20px;
  }
  
  .paginacion-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--text-color);
  }
  
  .paginacion-btn:hover:not(:disabled) {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .paginacion-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .paginacion-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Modal de anuncio */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: flex-end;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 0 30px 30px;
  }
  
  .modal-info-autor {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .autor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
  }
  
  .autor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .autor-info {
    flex: 1;
  }
  
  .autor-nombre {
    font-size: 1.1rem;
    margin: 0 0 5px;
    color: var(--text-color);
  }
  
  .autor-cargo {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .modal-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .modal-fecha {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .modal-categoria {
    font-size: 0.9rem;
    padding: 3px 10px;
    border-radius: 20px;
    color: white;
    font-weight: 500;
    background-color: var(--categoria-general);
  }
  
  .modal-titulo {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.3;
  }
  
  .modal-imagen-container {
    width: 100%;
    margin-bottom: 25px;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .modal-imagen-container img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .modal-contenido-texto {
    color: var(--text-color);
    line-height: 1.7;
    font-size: 1.05rem;
    margin-bottom: 30px;
  }
  
  .modal-contenido-texto p {
    margin-bottom: 15px;
  }
  
  .modal-contenido-texto ul {
    margin-bottom: 15px;
    padding-left: 20px;
  }
  
  .modal-contenido-texto li {
    margin-bottom: 5px;
  }
  
  .modal-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
  }
  
  .modal-acciones {
    display: flex;
    gap: 15px;
  }
  
  .compartir-btn,
  .descargar-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 8px 16px;
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .compartir-btn:hover,
  .descargar-btn:hover {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
  }
  
  .compartir-btn .material-icons,
  .descargar-btn .material-icons {
    font-size: 1.1rem;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .anuncios-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .filtros-container {
      flex-direction: column;
    }
  
    .filtro-busqueda,
    .filtro-categoria,
    .filtro-fecha {
      width: 100%;
    }
  
    .anuncios-grid {
      grid-template-columns: 1fr;
    }
  
    .modal-content {
      width: 95%;
      max-height: 85vh;
    }
  
    .modal-titulo {
      font-size: 1.5rem;
    }
  
    .modal-acciones {
      flex-direction: column;
    }
  }
  
  