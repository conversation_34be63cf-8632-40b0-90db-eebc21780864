<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Tareas</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/tareas.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Juan Pérez</h3>
                        <p>5° Primaria</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_e.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_e.html">
                                <span class="material-icons">book</span>
                                <span>Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span class="material-icons">grade</span>
                                <span>Notas</span>
                            </a>
                        </li>
                        <li>
                            <a href="tareas_e.html">
                                <span class="material-icons">assignment</span>
                                <span>Tareas</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #4caf50;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_e.html" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1>Matemáticas</h1>
                            <p>Prof. Carlos García</p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <div class="schedule-day">
                                <span class="day-label">Lunes</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                            <div class="schedule-day">
                                <span class="day-label">Miércoles</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_c.html" class="course-tab">Contenido</a>
                    <a href="tareas_c.html" class="course-tab active">Tareas</a>
                    <a href="estudiantes_c.html" class="course-tab">Estudiantes</a>
                    <a href="calificaciones-c.html" class="course-tab">Calificaciones</a>
                    <a href="asistencias_c.html" class="course-tab">Asistencias</a>
                    <a href="mensajes_c.html" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Tareas pendientes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas pendientes</h2>
                    </div>
                    
                    <div class="tasks-list">
                        <div class="task-item clickable-task" data-task="sumas-restas">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <h3>Sumas y restas</h3>
                                <p>Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                                <div class="task-meta">
                                    <span class="task-date">Fecha límite: 23/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="sumas-restas">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="task-item clickable-task" data-task="problemas-aplicacion">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <h3>Problemas de aplicación</h3>
                                <p>Resuelve los problemas de aplicación de la pág. 22 del libro de texto.</p>
                                <div class="task-meta">
                                    <span class="task-date">Fecha límite: 25/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="problemas-aplicacion">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Tareas atrasadas -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Tareas atrasadas</h2>
                    </div>

                    <div class="tasks-list">
                        <div class="task-item clickable-task" data-task="fracciones-basicas">
                            <div class="task-status late"></div>
                            <div class="task-content">
                                <h3>Fracciones básicas</h3>
                                <p>Resuelve los ejercicios de fracciones básicas de la pág. 15.</p>
                                <div class="task-meta">
                                    <span class="task-date">Fecha límite: 18/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="fracciones-basicas">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>

                        <div class="task-item clickable-task" data-task="geometria-figuras">
                            <div class="task-status late"></div>
                            <div class="task-content">
                                <h3>Geometría - Figuras planas</h3>
                                <p>Identifica y clasifica las figuras geométricas de la pág. 12.</p>
                                <div class="task-meta">
                                    <span class="task-date">Fecha límite: 20/03/2025</span>
                                    <span class="task-grade">Calificación: --</span>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn view-task-btn" data-task="geometria-figuras">
                                    <span class="material-icons">visibility</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">upload_file</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Vista de tarea (oculta por defecto) -->
    <div id="task-view" class="modal-overlay">
        <div class="modal-content assignment-container">
            <div class="assignment-header">
                <h1 class="assignment-title">
                    <span class="material-icons">assignment</span>
                    Tarea: Sumas y restas
                </h1>
                <span class="grade-pending">Calificación: --</span>
            </div>
            
            <div class="assignment-content">
                <div class="assignment-section">
                    <h2 class="assignment-section-title">Descripción de la tarea</h2>
                    <p class="assignment-description">Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
                    <img src="unnie.png" alt="Ejercicios de sumas y restas" class="assignment-image">
                </div>
                
                <div class="assignment-section">
                    <div class="assignment-meta">
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">event</span>
                                Fecha de entrega
                            </div>
                            <div class="meta-value">23/03/2025, 12:00 AM</div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">grade</span>
                                Puntos
                            </div>
                            <div class="meta-value">10 puntos máximos</div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-label">
                                <span class="material-icons">person</span>
                                Asignado por
                            </div>
                            <div class="meta-value">Prof. Carlos García</div>
                        </div>
                        

                    </div>
                </div>
                
                <div class="assignment-section">
                    <div class="submission-section">
                        <h2 class="submission-title">Entrega de tarea</h2>
                        <div class="submission-status">
                            <span class="material-icons">schedule</span>
                            <span>Pendiente - Tienes hasta el 23/03/2025 para entregar esta tarea.</span>
                        </div>
                        <div class="submission-options">
                            <div class="file-upload-area">
                                <span class="material-icons file-upload-icon">cloud_upload</span>
                                <p class="file-upload-text">Arrastra y suelta archivos aquí o haz clic para seleccionar archivos</p>
                                <p class="file-upload-hint">Formatos aceptados: PDF, JPG, PNG. Tamaño máximo: 10MB</p>
                            </div>
                            
                            <div class="text-submission-area">
                                <label class="text-submission-label">Puedes añadir comentarios a tu entrega:</label>
                                <textarea class="text-submission-textarea" placeholder="Escribe tus comentarios aquí..."></textarea>
                            </div>
                            
                            <div class="submission-actions">
                                <button class="assignment-action-btn secondary-btn" id="close-task-btn">Cancelar</button>
                                <button class="assignment-action-btn primary-btn">Entregar tarea</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/tareas_c.js"></script>
</body>
</html>