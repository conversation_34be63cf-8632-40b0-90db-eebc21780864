document.addEventListener("DOMContentLoaded", () => {
    // Datos de ejemplo para tareas y exámenes
    const tasks = [
      {
        id: 1,
        title: "Ejercicios de Fracciones",
        type: "tarea",
        description: "Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.",
        dueDate: "2025-03-18",
        dueTime: "23:59",
        points: 10,
        weekId: 1,
        weekTitle: "Semana 1: Introducción a las fracciones",
        status: "active",
        submissions: 5,
        totalStudents: 10,
        image: "/placeholder.svg?height=200&width=400&text=Ejercicios+de+sumas+y+restas"
      },
      {
        id: 2,
        title: "Representación gráfica de fracciones",
        type: "tarea",
        description: "Representar gráficamente las fracciones indicadas.",
        dueDate: "2025-03-10",
        dueTime: "23:59",
        points: 10,
        weekId: 1,
        weekTitle: "Semana 1: Introducción a las fracciones",
        status: "closed",
        submissions: 8,
        totalStudents: 10,
      },
      {
        id: 3,
        title: "Examen de Fracciones Básicas",
        type: "examen",
        description: "Examen sobre conceptos básicos de fracciones y operaciones.",
        dueDate: "2025-03-20",
        dueTime: "10:00",
        points: 20,
        weekId: 1,
        weekTitle: "Semana 1: Introducción a las fracciones",
        status: "active",
        submissions: 3,
        totalStudents: 10,
      },
      {
        id: 4,
        title: "Multiplicación de fracciones",
        type: "tarea",
        description: "Ejercicios de multiplicación de fracciones.",
        dueDate: "2025-03-25",
        dueTime: "23:59",
        points: 15,
        weekId: 2,
        weekTitle: "Semana 2: Operaciones con fracciones",
        status: "active",
        submissions: 2,
        totalStudents: 10,
      },
      {
        id: 5,
        title: "División de fracciones",
        type: "tarea",
        description: "Ejercicios de división de fracciones.",
        dueDate: "2025-03-28",
        dueTime: "23:59",
        points: 15,
        weekId: 2,
        weekTitle: "Semana 2: Operaciones con fracciones",
        status: "draft",
        submissions: 0,
        totalStudents: 10,
      },
      {
        id: 6,
        title: "Examen de Operaciones con Fracciones",
        type: "examen",
        description: "Examen sobre multiplicación y división de fracciones.",
        dueDate: "2025-03-30",
        dueTime: "09:00",
        points: 25,
        weekId: 2,
        weekTitle: "Semana 2: Operaciones con fracciones",
        status: "draft",
        submissions: 0,
        totalStudents: 10,
      },
      {
        id: 7,
        title: "Fracciones equivalentes",
        type: "tarea",
        description: "Identificar y generar fracciones equivalentes.",
        dueDate: "2025-04-01",
        dueTime: "23:59",
        points: 12,
        weekId: 3,
        weekTitle: "Semana 3: Fracciones equivalentes",
        status: "draft",
        submissions: 0,
        totalStudents: 10,
      },
    ]
  
    // Variables para paginación
    let currentPage = 1
    const itemsPerPage = 5
    let filteredTasks = [...tasks]
  
    // Elementos DOM
    const tasksTableBody = document.getElementById("tasks-table-body")
    const searchInput = document.getElementById("task-search")
    const createTaskBtn = document.getElementById("create-task-btn")
    const taskFormModal = document.getElementById("task-form-modal")
    const deleteConfirmModal = document.getElementById("delete-confirm-modal")
    const taskViewModal = document.getElementById("task-view-modal")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const taskForm = document.getElementById("task-form")
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn")
    const prevPageBtn = document.getElementById("prev-page")
    const nextPageBtn = document.getElementById("next-page")
    const currentPageSpan = document.getElementById("current-page")
    const totalPagesSpan = document.getElementById("total-pages")
  
    // Inicializar la página
    init()
  
    function init() {
      // Cargar tareas iniciales
      renderTasks()
  
      // Configurar event listeners
      setupEventListeners()
    }
  
    function setupEventListeners() {
      // Event listener para búsqueda
      if (searchInput) {
        searchInput.addEventListener("input", () => {
          filterTasks()
          renderTasks()
        })
      }
  
      // Event listener para botón de crear tarea
      if (createTaskBtn) {
        createTaskBtn.addEventListener("click", () => {
          openTaskFormModal()
        })
      }
  
      // Event listeners para cerrar modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          closeAllModals()
        })
      })
  
      // Event listener para envío del formulario
      if (taskForm) {
        taskForm.addEventListener("submit", (e) => {
          e.preventDefault()
          saveTask()
        })
      }
  
      // Event listener para confirmar eliminación
      if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", () => {
          deleteTask()
        })
      }
  
      // Event listeners para paginación
      if (prevPageBtn) {
        prevPageBtn.addEventListener("click", () => {
          if (currentPage > 1) {
            currentPage--
            renderTasks()
          }
        })
      }
  
      if (nextPageBtn) {
        nextPageBtn.addEventListener("click", () => {
          const totalPages = Math.ceil(filteredTasks.length / itemsPerPage)
          if (currentPage < totalPages) {
            currentPage++
            renderTasks()
          }
        })
      }
  
      // Event delegation para botones de acción en la tabla
      document.addEventListener("click", (e) => {
        // Botón de editar
        if (e.target.closest(".task-action-btn.edit-btn")) {
          const btn = e.target.closest(".task-action-btn.edit-btn")
          const taskId = btn.dataset.taskId
          openTaskFormModal(taskId)
        }

        // Botón de eliminar
        if (e.target.closest(".task-action-btn.delete-btn")) {
          const btn = e.target.closest(".task-action-btn.delete-btn")
          const taskId = btn.dataset.taskId
          openDeleteConfirmModal(taskId)
        }

        // Botón de ver
        if (e.target.closest(".task-action-btn.view-btn")) {
          const btn = e.target.closest(".task-action-btn.view-btn")
          const taskId = btn.dataset.taskId
          openTaskViewModal(taskId)
        }

        // Botones del editor de texto enriquecido
        if (e.target.closest(".toolbar-btn")) {
          const btn = e.target.closest(".toolbar-btn")
          const command = btn.dataset.command

          if (command) {
            e.preventDefault()
            if (command === "createLink") {
              const url = prompt("Ingresa la URL:")
              if (url) {
                document.execCommand(command, false, url)
              }
            } else {
              document.execCommand(command, false, null)
            }

            // Actualizar estado de botones
            updateToolbarButtons()
          }
        }

        // Botón de insertar imagen
        if (e.target.closest("#insert-image-btn")) {
          e.preventDefault()
          document.getElementById("image-upload").click()
        }
      })

      // Event listener para subida de imágenes
      const imageUpload = document.getElementById("image-upload")
      if (imageUpload) {
        imageUpload.addEventListener("change", (e) => {
          const file = e.target.files[0]
          if (file) {
            const reader = new FileReader()
            reader.onload = (e) => {
              const img = `<img src="${e.target.result}" alt="Imagen insertada" style="max-width: 100%; height: auto; margin: 10px 0;">`
              document.execCommand("insertHTML", false, img)
            }
            reader.readAsDataURL(file)
          }
        })
      }

      // Event listener para actualizar botones del toolbar
      const richTextEditor = document.getElementById("task-description")
      if (richTextEditor) {
        richTextEditor.addEventListener("keyup", updateToolbarButtons)
        richTextEditor.addEventListener("mouseup", updateToolbarButtons)
      }
    }
  
    function filterTasks() {
      const searchTerm = searchInput.value.toLowerCase()

      if (searchTerm === "") {
        filteredTasks = [...tasks]
      } else {
        filteredTasks = tasks.filter(
          (task) =>
            task.title.toLowerCase().includes(searchTerm) ||
            task.description.toLowerCase().includes(searchTerm) ||
            task.weekTitle.toLowerCase().includes(searchTerm) ||
            (task.type && task.type.toLowerCase().includes(searchTerm)),
        )
      }

      // Resetear a la primera página cuando se filtra
      currentPage = 1
    }
  
    function renderTasks() {
      // Limpiar la tabla
      tasksTableBody.innerHTML = ""
  
      // Calcular paginación
      const totalPages = Math.ceil(filteredTasks.length / itemsPerPage)
      const start = (currentPage - 1) * itemsPerPage
      const end = start + itemsPerPage
      const paginatedTasks = filteredTasks.slice(start, end)
  
      // Actualizar información de paginación
      currentPageSpan.textContent = currentPage
      totalPagesSpan.textContent = totalPages
      prevPageBtn.disabled = currentPage === 1
      nextPageBtn.disabled = currentPage === totalPages || totalPages === 0
  
      // Renderizar tareas
      if (paginatedTasks.length === 0) {
        const emptyRow = document.createElement("tr")
        emptyRow.innerHTML = `
          <td colspan="8" class="empty-table-message">
            No hay contenido disponible.
          </td>
        `
        tasksTableBody.appendChild(emptyRow)
        return
      }
  
      paginatedTasks.forEach((task) => {
        const row = document.createElement("tr")
  
        // Formatear fecha y hora
        const dueDate = new Date(task.dueDate)
        const formattedDate = dueDate.toLocaleDateString("es-ES")
        const formattedTime = task.dueTime || "23:59"

        // Formatear tipo de contenido
        const typeLabel = task.type === 'examen' ? 'Examen' : 'Tarea'
        const typeClass = task.type === 'examen' ? 'content-type-exam' : 'content-type-task'

        row.innerHTML = `
          <td>
            <div class="task-title">${task.title}</div>
          </td>
          <td>
            <div class="content-type ${typeClass}">${typeLabel}</div>
          </td>
          <td>
            <div class="task-week">${task.weekTitle}</div>
          </td>
          <td>
            <div class="task-due-date">${formattedDate}</div>
          </td>
          <td>
            <div class="task-due-time">${formattedTime}</div>
          </td>
          <td>
            <div class="task-points">${task.points}</div>
          </td>
          <td>
            <div class="task-submissions">
              <span class="material-icons">assignment_turned_in</span>
              <span>${task.submissions}/${task.totalStudents}</span>
            </div>
          </td>
          <td>
            <div class="task-actions">
              <button class="task-action-btn edit-btn" data-task-id="${task.id}" title="Editar">
                <span class="material-icons">edit</span>
              </button>
              <button class="task-action-btn delete-btn" data-task-id="${task.id}" title="Eliminar">
                <span class="material-icons">delete</span>
              </button>
              <button class="task-action-btn view-btn" data-task-id="${task.id}" title="Ver detalles">
                <span class="material-icons">visibility</span>
              </button>
            </div>
          </td>
        `
  
        tasksTableBody.appendChild(row)
      })
    }

    function updateToolbarButtons() {
      const buttons = document.querySelectorAll(".toolbar-btn[data-command]")
      buttons.forEach(btn => {
        const command = btn.dataset.command
        try {
          if (document.queryCommandState(command)) {
            btn.classList.add("active")
          } else {
            btn.classList.remove("active")
          }
        } catch (e) {
          // Algunos comandos no soportan queryCommandState
          btn.classList.remove("active")
        }
      })
    }

    function openTaskFormModal(taskId = null) {
      // Limpiar el formulario
      taskForm.reset()

      // Limpiar el editor de texto enriquecido
      const richTextEditor = document.getElementById("task-description")
      richTextEditor.innerHTML = ""

      // Establecer título del modal
      const modalTitle = document.getElementById("task-form-title")
      const saveBtn = document.getElementById("save-task-btn")

      if (taskId) {
        // Modo edición
        // Buscar la tarea
        const task = tasks.find((t) => t.id == taskId)

        if (task) {
          const contentType = task.type || 'tarea'
          const contentLabel = contentType === 'examen' ? 'Examen' : 'Tarea'

          modalTitle.textContent = `Editar ${contentLabel}`
          saveBtn.textContent = `Guardar ${contentLabel}`

          // Llenar el formulario con los datos de la tarea
          document.getElementById("task-id").value = task.id
          document.getElementById("content-type").value = contentType
          document.getElementById("task-title").value = task.title
          document.getElementById("task-week").value = task.weekId
          document.getElementById("task-due-date").value = task.dueDate
          document.getElementById("task-due-time").value = task.dueTime || "23:59"
          document.getElementById("task-points").value = task.points
          richTextEditor.innerHTML = task.description
        }
      } else {
        // Modo creación
        modalTitle.textContent = "Nuevo Contenido"
        saveBtn.textContent = "Guardar"
        document.getElementById("task-id").value = ""

        // Establecer fecha mínima como hoy
        const today = new Date().toISOString().split("T")[0]
        document.getElementById("task-due-date").min = today

        // Establecer hora por defecto
        document.getElementById("task-due-time").value = "23:59"
      }

      // Configurar event listener para cambio de tipo
      const contentTypeSelect = document.getElementById("content-type")
      contentTypeSelect.addEventListener("change", updateFormLabels)

      // Mostrar el modal
      taskFormModal.classList.add("active")
    }

    function updateFormLabels() {
      const contentType = document.getElementById("content-type").value
      const modalTitle = document.getElementById("task-form-title")
      const saveBtn = document.getElementById("save-task-btn")
      const titleInput = document.getElementById("task-title")

      if (contentType === 'examen') {
        if (!document.getElementById("task-id").value) {
          modalTitle.textContent = "Nuevo Examen"
          saveBtn.textContent = "Guardar Examen"
        }
        titleInput.placeholder = "Ej: Examen de fracciones"
      } else if (contentType === 'tarea') {
        if (!document.getElementById("task-id").value) {
          modalTitle.textContent = "Nueva Tarea"
          saveBtn.textContent = "Guardar Tarea"
        }
        titleInput.placeholder = "Ej: Ejercicios de fracciones"
      } else {
        if (!document.getElementById("task-id").value) {
          modalTitle.textContent = "Nuevo Contenido"
          saveBtn.textContent = "Guardar"
        }
        titleInput.placeholder = "Ej: Ejercicios de fracciones"
      }
    }
  
    function openDeleteConfirmModal(taskId) {
      // Buscar la tarea para obtener su tipo
      const task = tasks.find((t) => t.id == taskId)
      const confirmationText = document.getElementById("delete-confirmation-text")

      if (task) {
        const contentType = task.type === 'examen' ? 'examen' : 'tarea'
        confirmationText.textContent = `¿Desea eliminar esta ${contentType}?`
      } else {
        confirmationText.textContent = "¿Desea eliminar este contenido?"
      }

      // Guardar el ID de la tarea a eliminar
      confirmDeleteBtn.dataset.taskId = taskId

      // Mostrar el modal
      deleteConfirmModal.classList.add("active")
    }

    function openTaskViewModal(taskId) {
      // Buscar la tarea
      const task = tasks.find((t) => t.id == taskId)

      if (task) {
        const contentType = task.type === 'examen' ? 'examen' : 'tarea'
        const contentLabel = task.type === 'examen' ? 'Examen' : 'Tarea'

        // Formatear fecha y hora
        const dueDate = new Date(task.dueDate + "T" + (task.dueTime || "23:59"))
        const formattedDate = dueDate.toLocaleDateString("es-ES", {
          day: "numeric",
          month: "numeric",
          year: "numeric"
        })
        const formattedTime = dueDate.toLocaleTimeString("es-ES", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true
        })

        // Actualizar títulos dinámicamente
        document.getElementById("task-view-title").innerHTML = `<span class="material-icons">assignment</span>${contentLabel}: ${task.title}`
        document.getElementById("view-content-description-title").textContent = `Descripción del ${contentType}`
        document.getElementById("view-content-options-title").textContent = `Opciones del ${contentType}`

        // Llenar el modal con los datos de la tarea
        document.getElementById("view-task-description").innerHTML = task.description
        document.getElementById("view-task-due-date").textContent = `${formattedDate}, ${formattedTime}`
        document.getElementById("view-task-points").textContent = `${task.points} puntos máximos`

        // Actualizar estado de entrega
        const statusValue = document.querySelector("#task-view-modal .status-value")
        if (statusValue) {
          statusValue.textContent = `Activa - Los estudiantes pueden entregar hasta el ${formattedDate}`
        }

        // Mostrar imagen si existe
        const imageSection = document.getElementById("task-image-section")
        const taskImage = document.getElementById("view-task-image")

        if (task.image) {
          taskImage.src = task.image
          taskImage.alt = `Imagen de ${task.title}`
          imageSection.style.display = "block"
        } else {
          imageSection.style.display = "none"
        }

        // Mostrar el modal
        taskViewModal.classList.add("active")
      }
    }
  
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  
    function saveTask() {
      // Obtener valores del formulario
      const taskId = document.getElementById("task-id").value
      const contentType = document.getElementById("content-type").value
      const title = document.getElementById("task-title").value
      const weekId = document.getElementById("task-week").value
      const weekTitle =
        document.getElementById("task-week").options[document.getElementById("task-week").selectedIndex].text
      const dueDate = document.getElementById("task-due-date").value
      const dueTime = document.getElementById("task-due-time").value
      const points = document.getElementById("task-points").value
      const description = document.getElementById("task-description").innerHTML

      if (taskId) {
        // Actualizar tarea existente
        const taskIndex = tasks.findIndex((t) => t.id == taskId)

        if (taskIndex !== -1) {
          tasks[taskIndex] = {
            ...tasks[taskIndex],
            type: contentType,
            title,
            weekId,
            weekTitle,
            dueDate,
            dueTime,
            points,
            description,
          }
        }
      } else {
        // Crear nueva tarea (por defecto será activa)
        const newTask = {
          id: tasks.length > 0 ? Math.max(...tasks.map((t) => t.id)) + 1 : 1,
          type: contentType,
          title,
          weekId,
          weekTitle,
          dueDate,
          dueTime,
          points,
          status: "active", // Estado por defecto
          description,
          submissions: 0,
          totalStudents: 10,
        }

        tasks.push(newTask)
      }

      // Actualizar la lista filtrada
      filterTasks()

      // Renderizar tareas
      renderTasks()

      // Cerrar el modal
      closeAllModals()
    }
  
    function deleteTask() {
      const taskId = confirmDeleteBtn.dataset.taskId
  
      // Buscar el índice de la tarea
      const taskIndex = tasks.findIndex((t) => t.id == taskId)
  
      if (taskIndex !== -1) {
        // Eliminar la tarea
        tasks.splice(taskIndex, 1)
  
        // Actualizar la lista filtrada
        filterTasks()
  
        // Renderizar tareas
        renderTasks()
  
        // Cerrar el modal
        closeAllModals()
      }
    }
  })
  