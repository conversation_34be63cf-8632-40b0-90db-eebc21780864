<?php
session_start();

// Debug de sesión
error_log('Sesión en estudiantes_m.php: ' . json_encode($_SESSION));

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    // Redirigir a login si no hay sesión válida
    header('Location: intranet.php');
    exit;
}

// Se espera recibir el parámetro curso_id por GET
$curso_id = isset($_GET['curso_id']) ? intval($_GET['curso_id']) : 0;

if (!$curso_id) {
    header('Location: cursos_m.php');
    exit;
}

$maestroId = $_SESSION['usuario_id'];

// Obtener información del curso
require_once '../Controlador/CursoController.php';
$cursoController = new CursoController();
$curso = $cursoController->obtenerCursoPorId($curso_id, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Cargar estudiantes directamente desde PHP
require_once '../Controlador/InscripcionController.php';
$inscripcionController = new InscripcionController();
$alumnos_inscritos = $inscripcionController->obtenerInscritos($curso_id);
$estudiantes_disponibles = $inscripcionController->obtenerDisponibles($curso_id);

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil
          FROM usuarios u
          JOIN personas p ON u.id = p.usuario_id
          JOIN maestros m ON p.id = m.persona_id
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados
$grados = $cursoController->obtenerGrados();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Gestión de Estudiantes</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/estudiantes_m.css">
</head>
<body class="has-course-header" data-curso-id="<?php echo $curso_id; ?>">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>

            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php if ($maestro && $maestro['foto_perfil']): ?>
                            <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                        <?php else: ?>
                            <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                        <p>Profesor</p>
                    </div>
                </div>

                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.php">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_m.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../logout.php">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #2196f3;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_m.php" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1><?php echo htmlspecialchars($curso['nombre']); ?></h1>
                            <p><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <?php if (!empty($curso['horarios_procesados'])): ?>
                                <?php foreach ($curso['horarios_procesados'] as $dia => $horario): ?>
                                    <?php
                                    $dias = [
                                        'lunes' => 'Lun',
                                        'martes' => 'Mar',
                                        'miercoles' => 'Mié',
                                        'jueves' => 'Jue',
                                        'viernes' => 'Vie'
                                    ];
                                    $partes = explode(' - ', $horario);
                                    if (count($partes) === 2 && $partes[0] && $partes[1]) {
                                        $hora_inicio = substr($partes[0], 0, 5);
                                        $hora_fin = substr($partes[1], 0, 5);
                                    ?>
                                        <div class="schedule-day">
                                            <span class="day-label"><?php echo $dias[$dia]; ?></span>
                                            <span class="day-time"><?php echo $hora_inicio . ' - ' . $hora_fin; ?></span>
                                        </div>
                                    <?php } ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="schedule-day">
                                    <span class="day-label">Sin horario</span>
                                    <span class="day-time">definido</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_m.php?curso_id=<?php echo $curso_id; ?>" class="course-tab">Contenido</a>
                    <a href="tareas_menu.html?curso_id=<?php echo $curso_id; ?>" class="course-tab">Tareas</a>
                    <a href="estudiantes_m.php?curso_id=<?php echo $curso_id; ?>" class="course-tab active">Estudiantes</a>
                    <a href="calificaciones_m.html?curso_id=<?php echo $curso_id; ?>" class="course-tab">Calificaciones</a>
                    <a href="asistencias_estudiantes.html?curso_id=<?php echo $curso_id; ?>" class="course-tab">Asistencia</a>
                    <a href="mensajes_m.html?curso_id=<?php echo $curso_id; ?>" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de acciones y búsqueda -->
                <section class="filter-section">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="student-search" placeholder="Buscar estudiantes por nombre...">
                        </div>
                        <div class="filter-actions">
                            <button class="action-btn primary-btn" id="add-student-btn">
                                <span class="material-icons">person_add</span>
                                Agregar estudiante
                            </button>
                            <button class="action-btn secondary-btn" id="export-excel-btn">
                                <span class="material-icons">file_download</span>
                                Exportar Excel
                            </button>
                        </div>
                    </div>
                </section>
                
                <!-- Lista de estudiantes del curso -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Estudiantes del curso</h2>
                        <span class="student-count"><?php echo count($alumnos_inscritos); ?> estudiantes</span>
                    </div>

                    <div class="students-grid">
                        <?php if (empty($alumnos_inscritos)): ?>
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <span class="material-icons">groups</span>
                                </div>
                                <h3>No hay estudiantes inscritos</h3>
                                <p>Agrega estudiantes al curso para comenzar</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($alumnos_inscritos as $estudiante): ?>
                                <div class="student-card enrolled">
                                    <div class="student-avatar">
                                        <img src="<?php echo htmlspecialchars($estudiante['avatar']); ?>" alt="Foto de <?php echo htmlspecialchars($estudiante['nombre']); ?>">
                                    </div>
                                    <div class="student-info">
                                        <h3><?php echo htmlspecialchars($estudiante['nombre']); ?></h3>
                                        <p class="student-grade"><?php echo htmlspecialchars($estudiante['grado']); ?></p>
                                    </div>
                                    <div class="student-actions">
                                        <button class="student-action-btn delete-btn" title="Eliminar estudiante del curso"
                                                data-student-id="<?php echo $estudiante['id']; ?>"
                                                data-student-name="<?php echo htmlspecialchars($estudiante['nombre']); ?>">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>María López</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="María López">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Pedro Gómez</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Pedro Gómez">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Ana Rodríguez</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Ana Rodríguez">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Lucía Vargas</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Lucía Vargas">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pagination">
                        <button class="pagination-btn" disabled>
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modal para agregar estudiante -->
    <div id="add-student-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Agregar estudiante al curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="search-students-section">
                    <div class="search-box">
                        <span class="material-icons">search</span>
                        <input type="text" id="search-available-students" placeholder="Buscar estudiantes disponibles...">
                    </div>

                    <div class="available-students-list" id="available-students-list">
                        <?php if (empty($estudiantes_disponibles)): ?>
                            <div class="no-students">
                                <div class="empty-icon">
                                    <span class="material-icons">school</span>
                                </div>
                                <p>No hay estudiantes disponibles para inscribir en este curso.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($estudiantes_disponibles as $estudiante): ?>
                                <div class="student-search-item">
                                    <div class="student-avatar">
                                        <img src="<?php echo htmlspecialchars($estudiante['avatar']); ?>" alt="Foto de <?php echo htmlspecialchars($estudiante['nombre']); ?>">
                                    </div>
                                    <div class="student-info">
                                        <h4><?php echo htmlspecialchars($estudiante['nombre']); ?></h4>
                                        <p class="student-grade"><?php echo htmlspecialchars($estudiante['grado']); ?></p>
                                    </div>
                                    <button class="add-student-btn" data-student-id="<?php echo $estudiante['id']; ?>">
                                        <span class="material-icons">person_add</span>
                                        Agregar
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Diego Herrera Vega</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="027">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Valentina Jiménez Ortiz</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="028">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Sebastián Ramírez Luna</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="029">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Carmen Silva Torres</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="030">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Andrés Mendoza Ruiz</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="031">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Sofía Castillo Pérez</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="032">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancel-add-student">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmación para eliminar estudiante -->
    <div id="delete-student-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Eliminar estudiante del curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="confirmation-message">
                    <span class="material-icons warning-icon">warning</span>
                    <p id="delete-student-message">¿Está seguro que desea eliminar a este estudiante del curso?</p>
                    <p class="warning-text">El estudiante será removido del curso pero no se eliminará de la base de datos. Puede volver a agregarlo más tarde.</p>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancel-delete-student">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-student">Eliminar del curso</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/estudiantes_m.js"></script>
</body>
</html>
