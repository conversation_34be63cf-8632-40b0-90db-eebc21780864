document.addEventListener("DOMContentLoaded", () => {
    console.log('Iniciando sistema de cursos simplificado...')
    
    // Referencias básicas
    const courseModal = document.getElementById("course-modal")
    const courseForm = document.getElementById("course-form")
    const modalTitle = document.getElementById("modal-title")
    const courseIdInput = document.getElementById("course-id")
    const courseNameInput = document.getElementById("course-name")
    const courseGradeInput = document.getElementById("course-grade")
    const courseIconInput = document.getElementById("course-icon")
    const createCourseBtn = document.getElementById("create-course-btn")
    const createFirstCourseBtn = document.getElementById("create-first-course-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const scheduleDayCheckboxes = document.querySelectorAll('input[name="schedule-day"]')
    
    console.log('Elementos encontrados:', {
        courseModal: !!courseModal,
        courseForm: !!courseForm,
        modalTitle: !!modalTitle,
        courseIdInput: !!courseIdInput,
        courseNameInput: !!courseNameInput,
        courseGradeInput: !!courseGradeInput,
        courseIconInput: !!courseIconInput
    })

    // Función simple para mostrar notificaciones
    function showNotification(message, type = 'success') {
        alert(`${type.toUpperCase()}: ${message}`)
    }

    // Función simplificada para cargar datos del curso
    async function cargarDatosCurso(cursoId) {
        try {
            console.log('Cargando curso ID:', cursoId)
            
            const response = await fetch(`../api_cursos.php?action=obtener_curso&id=${cursoId}`)
            const data = await response.json()
            
            console.log('Respuesta API:', data)
            
            if (data.success && data.curso) {
                const curso = data.curso
                
                // Llenar formulario básico
                if (courseIdInput) courseIdInput.value = curso.id
                if (courseNameInput) courseNameInput.value = curso.nombre
                if (courseGradeInput) courseGradeInput.value = curso.grado
                if (courseIconInput) courseIconInput.value = curso.icono || 'school'
                
                // Limpiar horarios
                scheduleDayCheckboxes.forEach(checkbox => {
                    checkbox.checked = false
                    const day = checkbox.value
                    const startInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endInput = document.querySelector(`input[name="end-time-${day}"]`)
                    if (startInput) startInput.disabled = true
                    if (endInput) endInput.disabled = true
                })
                
                // Cargar horarios si existen
                if (curso.horarios_procesados) {
                    Object.keys(curso.horarios_procesados).forEach(dia => {
                        const checkbox = document.querySelector(`input[name="schedule-day"][value="${dia}"]`)
                        if (checkbox) {
                            checkbox.checked = true
                            const startInput = document.querySelector(`input[name="start-time-${dia}"]`)
                            const endInput = document.querySelector(`input[name="end-time-${dia}"]`)
                            if (startInput && endInput) {
                                startInput.disabled = false
                                endInput.disabled = false
                                const horario = curso.horarios_procesados[dia]
                                const [inicio, fin] = horario.split('-')
                                startInput.value = inicio
                                endInput.value = fin
                            }
                        }
                    })
                }
                
                if (modalTitle) modalTitle.textContent = "Editar Curso"
                
                console.log('Curso cargado exitosamente')
                return true
            } else {
                console.error('Error en respuesta:', data.error)
                showNotification('Error al cargar curso: ' + (data.error || 'Error desconocido'), 'error')
                return false
            }
        } catch (error) {
            console.error('Error:', error)
            showNotification('Error al cargar curso: ' + error.message, 'error')
            return false
        }
    }

    // Función para abrir modal de crear
    function abrirModalCrear() {
        console.log('Abriendo modal para crear curso')
        if (courseForm) courseForm.reset()
        if (courseIdInput) courseIdInput.value = ""
        if (modalTitle) modalTitle.textContent = "Crear Nuevo Curso"
        
        // Limpiar horarios
        scheduleDayCheckboxes.forEach(checkbox => {
            checkbox.checked = false
            const day = checkbox.value
            const startInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endInput = document.querySelector(`input[name="end-time-${day}"]`)
            if (startInput) startInput.disabled = true
            if (endInput) endInput.disabled = true
        })
        
        if (courseModal) {
            courseModal.classList.add("active")
            document.body.style.overflow = "hidden"
        }
    }

    // Event listeners básicos
    if (createCourseBtn) {
        createCourseBtn.addEventListener("click", abrirModalCrear)
    }
    
    if (createFirstCourseBtn) {
        createFirstCourseBtn.addEventListener("click", abrirModalCrear)
    }

    // Cerrar modales
    modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", () => {
            document.querySelectorAll(".modal-overlay").forEach(modal => {
                modal.classList.remove("active")
            })
            document.body.style.overflow = ""
        })
    })

    // Cerrar al hacer clic fuera
    document.querySelectorAll(".modal-overlay").forEach(modal => {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.classList.remove("active")
                document.body.style.overflow = ""
            }
        })
    })

    // Manejar checkboxes de horario
    scheduleDayCheckboxes.forEach(checkbox => {
        checkbox.addEventListener("change", () => {
            const day = checkbox.value
            const startInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endInput = document.querySelector(`input[name="end-time-${day}"]`)
            
            if (startInput && endInput) {
                startInput.disabled = !checkbox.checked
                endInput.disabled = !checkbox.checked
                if (checkbox.checked) {
                    startInput.required = true
                    endInput.required = true
                } else {
                    startInput.required = false
                    endInput.required = false
                    startInput.value = ''
                    endInput.value = ''
                }
            }
        })
    })

    // BOTÓN DE EDITAR - FUNCIÓN PRINCIPAL
    document.addEventListener("click", async (e) => {
        if (e.target.closest('.edit-btn')) {
            e.preventDefault()
            e.stopPropagation()
            
            const btn = e.target.closest('.edit-btn')
            const courseId = btn.getAttribute("data-id")
            
            console.log('Botón editar clickeado para curso:', courseId)
            
            if (courseId) {
                const success = await cargarDatosCurso(courseId)
                if (success && courseModal) {
                    courseModal.classList.add("active")
                    document.body.style.overflow = "hidden"
                    console.log('Modal de edición abierto exitosamente')
                } else {
                    console.error('No se pudo abrir el modal de edición')
                }
            } else {
                console.error('ID de curso no encontrado')
                showNotification('Error: ID de curso no encontrado', 'error')
            }
        }
    })

    // Manejar envío del formulario
    if (courseForm) {
        courseForm.addEventListener("submit", async (e) => {
            e.preventDefault()
            
            const courseId = courseIdInput ? courseIdInput.value : ""
            const isNewCourse = courseId === ""
            
            // Recopilar datos básicos
            const formData = {
                nombre: courseNameInput ? courseNameInput.value : "",
                grado: courseGradeInput ? courseGradeInput.value : "",
                icono: courseIconInput ? courseIconInput.value : "school"
            }
            
            // Recopilar horarios
            const horarios = {}
            scheduleDayCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const day = checkbox.value
                    const startInput = document.querySelector(`input[name="start-time-${day}"]`)
                    const endInput = document.querySelector(`input[name="end-time-${day}"]`)
                    
                    if (startInput && endInput && startInput.value && endInput.value) {
                        horarios[day] = {
                            activo: true,
                            inicio: startInput.value,
                            fin: endInput.value
                        }
                    }
                }
            })
            formData.horarios = horarios
            
            try {
                const url = isNewCourse ? 
                    '../api_cursos.php?action=crear_curso' : 
                    '../api_cursos.php?action=actualizar_curso'
                
                if (!isNewCourse) {
                    formData.id = courseId
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                })
                
                const data = await response.json()
                
                if (data.success) {
                    showNotification(data.mensaje || `Curso ${isNewCourse ? 'creado' : 'actualizado'} correctamente`)
                    
                    // Cerrar modal
                    if (courseModal) {
                        courseModal.classList.remove("active")
                        document.body.style.overflow = ""
                    }
                    
                    // Recargar página
                    window.location.reload()
                } else {
                    showNotification(data.error || `Error al ${isNewCourse ? 'crear' : 'actualizar'} curso`, 'error')
                }
            } catch (error) {
                console.error('Error al guardar:', error)
                showNotification(`Error al ${isNewCourse ? 'crear' : 'actualizar'} curso: ` + error.message, 'error')
            }
        })
    }

    console.log('Sistema de cursos simplificado cargado correctamente')
}) 