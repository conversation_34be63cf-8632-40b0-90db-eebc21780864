<?php
require_once __DIR__.'/../Modelo/Inscripcion.php';
require_once __DIR__.'/../Modelo/Estudiante.php';

class InscripcionController {
    private $modelo;
    private $estudianteModelo;

    public function __construct() {
        $this->modelo = new Inscripcion();
        $this->estudianteModelo = new Estudiante();
    }

    public function obtenerInscritos($cursoId) {
        $datos = $this->modelo->obtenerInscritos($cursoId);
        return $this->formatearEstudiantes($datos);
    }

    public function obtenerDisponibles($cursoId) {
        $datos = $this->modelo->obtenerDisponibles($cursoId);
        return $this->formatearEstudiantes($datos);
    }

    private function formatearEstudiantes($datos) {
        $resultado = [];
        foreach ($datos as $estudiante) {
            // Validar que los campos existan antes de procesarlos
            $nombres = $estudiante['nombres'] ?? '';
            $apellido_paterno = $estudiante['apellido_paterno'] ?? '';
            $apellido_materno = $estudiante['apellido_materno'] ?? '';
            
            // Construir el nombre completo
            $nombre_completo = trim($nombres . ' ' . $apellido_paterno . ' ' . $apellido_materno);
            
            // Si no hay nombre, usar un valor por defecto
            if (empty($nombre_completo)) {
                $nombre_completo = 'Estudiante sin nombre';
            }
            
            $resultado[] = [
                'id' => $estudiante['id'] ?? 0,
                'nombre' => $nombre_completo,
                'grado' => $estudiante['grado_actual'] ?? $estudiante['grado'] ?? '5° Primaria',
                'avatar' => $estudiante['foto_perfil'] ?? '/placeholder.svg?height=80&width=80'
            ];
        }
        return $resultado;
    }

    public function agregarEstudiante($cursoId, $estudianteId) {
        try {
            // Log de debugging
            error_log("Controller: Intentando agregar estudiante ID: $estudianteId al curso ID: $cursoId");
            
            // Verificar que el estudiante existe
            $sqlEstudiante = "SELECT COUNT(*) FROM estudiantes WHERE id = :estudiante_id";
            $stmtEstudiante = $this->modelo->obtenerConexion()->prepare($sqlEstudiante);
            $stmtEstudiante->bindParam(':estudiante_id', $estudianteId, PDO::PARAM_INT);
            $stmtEstudiante->execute();
            
            if ($stmtEstudiante->fetchColumn() == 0) {
                error_log("Controller: Estudiante ID: $estudianteId no existe en la base de datos");
                return false;
            }
            
            // Verificar que el curso existe
            $sqlCurso = "SELECT COUNT(*) FROM cursos WHERE id = :curso_id";
            $stmtCurso = $this->modelo->obtenerConexion()->prepare($sqlCurso);
            $stmtCurso->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
            $stmtCurso->execute();
            
            if ($stmtCurso->fetchColumn() == 0) {
                error_log("Controller: Curso ID: $cursoId no existe en la base de datos");
                return false;
            }
            
            // El modelo maneja toda la lógica de inscripción/reactivación
            $resultado = $this->modelo->agregar($cursoId, $estudianteId);
            error_log("Controller: Resultado de la operación: " . ($resultado ? 'éxito' : 'fallo'));
            
            return $resultado;
        } catch (Exception $e) {
            error_log('Controller: Error en agregarEstudiante: ' . $e->getMessage());
            error_log('Controller: Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    public function eliminarEstudiante($cursoId, $estudianteId) {
        return $this->modelo->eliminar($cursoId, $estudianteId);
    }

    /**
     * Obtiene el número de estudiantes inscritos en un curso
     * @param int $cursoId
     * @return int
     */
    public function contarInscritos($cursoId) {
        return $this->modelo->contarInscritos($cursoId);
    }

    /**
     * Obtiene los cursos en los que está inscrito un estudiante
     * @param int $estudianteId
     * @return array
     */
    public function obtenerCursosPorEstudiante($estudianteId) {
        return $this->modelo->obtenerCursosPorEstudiante($estudianteId);
    }
}
?>
