/* Estilos específicos para tareas y archivos */

/* Variables */
:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Estilos para la vista de tarea */
.assignment-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 30px;
  width: 100%;
  max-width: 1400px; /* Aumentado para que sea más ancho */
  margin: 0 auto;
}

.assignment-header {
  padding: 20px 30px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9f9f9;
}

.assignment-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.assignment-title .material-icons {
  color: var(--primary-color);
}

.assignment-actions {
  display: flex;
  gap: 10px;
}

.assignment-action-btn {
  padding: 8px 16px;
  border-radius: 5px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: var(--transition);
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.primary-btn:hover {
  background-color: #1e40af;
}

.secondary-btn {
  background-color: white;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.secondary-btn:hover {
  background-color: var(--secondary-color);
}

.assignment-content {
  padding: 30px;
  background-color: white;
}

.assignment-section {
  margin-bottom: 30px;
}

.assignment-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.assignment-description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 20px;
}

.assignment-image {
  max-width: 100%;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid var(--border-color);
  display: block;
}

.assignment-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.meta-label {
  font-size: 0.9rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
  gap: 5px;
}

.meta-label .material-icons {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.meta-value {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
}

.meta-value a {
  color: var(--primary-color);
  text-decoration: none;
}

.meta-value a:hover {
  text-decoration: underline;
}

.grade-pending {
  display: inline-block;
  padding: 3px 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-weight: 500;
  color: var(--text-light);
}

.submission-section {
  background-color: var(--secondary-color);
  border-radius: 8px;
  padding: 25px;
}

.submission-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
}

.submission-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background-color: #fff8e1;
  border-radius: 8px;
  margin-bottom: 20px;
  color: var(--text-color);
}

.submission-status .material-icons {
  color: var(--warning-color);
}

.submission-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background-color: white;
  cursor: pointer;
  transition: var(--transition);
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

.file-upload-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.file-upload-text {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 10px;
}

.file-upload-hint {
  font-size: 0.85rem;
  color: var(--text-light);
}

.selected-files-list {
  margin-top: 15px;
}

.selected-file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: var(--background-light);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.selected-file-item .material-icons {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.file-name {
  flex: 1;
  font-weight: 500;
  color: var(--text-color);
}

.file-size {
  font-size: 0.85rem;
  color: var(--text-light);
}

.remove-file-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: var(--text-light);
  transition: var(--transition);
}

.remove-file-btn:hover {
  background-color: var(--error-light);
  color: var(--error-color);
}

.text-submission-area {
  margin-top: 20px;
}

.text-submission-label {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--text-color);
  display: block;
}

.text-submission-textarea {
  width: 100%;
  min-height: 150px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--text-color);
  resize: vertical;
  font-family: inherit;
}

.submission-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Estilos para modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: 10px;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  transform: translateY(20px);
  transition: transform 0.3s;
}

.modal-overlay.active .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: #f9f9f9;
}

.modal-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.modal-close-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
}

/* Estilos para anuncios */
.announcement-meta {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.announcement-text {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-color);
}

.announcement-text p {
  margin-bottom: 15px;
}

.announcement-text ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.announcement-text ul li {
  margin-bottom: 8px;
}

/* Estilos para contenido de carpetas */
.folder-content {
  padding: 10px 20px;
  transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
  transition: background-color 0.3s;
}

.content-item:hover {
  background-color: #f9f9f9;
}

.content-item:last-child {
  border-bottom: none;
}

.content-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.content-icon.pdf {
  background-color: #ffebee;
}

.content-icon.pdf .material-icons {
  color: #f44336;
}

.content-icon.ppt {
  background-color: #fff8e1;
}

.content-icon.ppt .material-icons {
  color: #ff9800;
}

.content-icon.task {
  background-color: #e8f5e9;
}

.content-icon.task .material-icons {
  color: #4caf50;
}

.content-icon.link {
  background-color: #e3f2fd;
}

.content-icon.link .material-icons {
  color: #2196f3;
}

.content-icon.announcement {
  background-color: #ede7f6;
}

.content-icon.announcement .material-icons {
  color: #673ab7;
}

.content-details {
  flex: 1;
  cursor: pointer;
}

.content-details h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
  transition: color 0.3s;
}

.content-details:hover h4 {
  color: var(--primary-color);
}

.content-details p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.content-date {
  font-size: 0.8rem;
  color: var(--text-light);
  display: block;
}

.content-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.content-action-btn {
  background: none;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.content-action-btn:hover {
  background-color: var(--secondary-color);
  color: var(--primary-color);
}

.task-grade {
  background-color: #f0f0f0;
  padding: 3px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-light);
}

/* Mensaje de contenido vacío */
.empty-content {
  padding: 30px;
  text-align: center;
  color: var(--text-light);
}

.empty-content .material-icons {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-content p {
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 992px) {
  .assignment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .assignment-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .assignment-meta {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .assignment-meta {
    grid-template-columns: 1fr;
  }

  .assignment-content {
    padding: 20px;
  }

  .file-upload-area {
    padding: 20px;
  }
}


/* Estilos adicionales para las tarjetas de resumen de tareas */

.task-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.task-summary-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.task-summary-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.pending-card {
  background-color: #fff8e1;
  border-left: 4px solid var(--warning-color);
}

.completed-card {
  background-color: #e8f5e9;
  border-left: 4px solid var(--success-color);
}

.late-card {
  background-color: #ffebee;
  border-left: 4px solid var(--danger-color);
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.pending-card .summary-icon {
  background-color: var(--warning-color);
}

.completed-card .summary-icon {
  background-color: var(--success-color);
}

.late-card .summary-icon {
  background-color: var(--danger-color);
}

.summary-icon .material-icons {
  color: white;
  font-size: 1.8rem;
}

.summary-info {
  flex: 1;
}

.summary-info h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
}

.summary-count {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.pending-card .summary-count {
  color: var(--warning-color);
}

.completed-card .summary-count {
  color: var(--success-color);
}

.late-card .summary-count {
  color: var(--danger-color);
}

.summary-text {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Estilos para asistencias */
.attendance-summary {
  display: flex;
  gap: 20px;
  margin-left: auto;
  align-items: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.summary-label {
  font-size: 0.85rem;
  color: var(--text-light);
  font-weight: 500;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 5px 10px;
  border-radius: 20px;
  min-width: 35px;
  text-align: center;
}

.summary-value.present {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.summary-value.absent {
  background-color: #ffebee;
  color: #c62828;
}

.summary-value.late {
  background-color: #fff8e1;
  color: #f57c00;
}

.attendance-table-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.attendance-table {
  width: 100%;
  border-collapse: collapse;
}

.attendance-table th {
  background-color: #f5f5f5;
  padding: 15px 20px;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--border-color);
}

.attendance-table td {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.attendance-table tr:hover {
  background-color: #f9f9f9;
}

.attendance-status {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

.attendance-status.present {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.attendance-status.absent {
  background-color: #ffebee;
  color: #c62828;
}

.attendance-status.late {
  background-color: #fff8e1;
  color: #f57c00;
}

.attendance-status.justified {
  background-color: #e3f2fd;
  color: #1565c0;
}

/* Responsive */
@media (max-width: 768px) {
  .task-summary-cards {
    grid-template-columns: 1fr;
  }

  .attendance-summary {
    flex-direction: column;
    gap: 10px;
    margin-left: 0;
    margin-top: 15px;
  }

  .attendance-table-container {
    overflow-x: auto;
  }

  .attendance-table th,
  .attendance-table td {
    padding: 10px 15px;
    white-space: nowrap;
  }
}