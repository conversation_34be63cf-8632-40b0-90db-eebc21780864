<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'Controlador/TareaController.php';

$tareaController = new TareaController();

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'obtener_tareas_curso':
                case 'get_tareas_curso':
                    $cursoId = $_GET['curso_id'] ?? null;
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    $tareas = $tareaController->obtenerTareasPorCurso($cursoId);
                    echo json_encode(['success' => true, 'data' => $tareas]);
                    break;
                    
                case 'obtener_tareas_semanas':
                    $cursoId = $_GET['curso_id'] ?? null;
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    $tareas = $tareaController->obtenerTareasAgrupadasPorSemana($cursoId);
                    echo json_encode(['success' => true, 'data' => $tareas]);
                    break;
                    
                case 'obtener_tarea':
                    $tareaId = $_GET['id'] ?? null;
                    if (!$tareaId) {
                        throw new Exception('ID de la tarea requerido');
                    }
                    
                    $tarea = $tareaController->obtenerTareaPorId($tareaId);
                    if (!$tarea) {
                        throw new Exception('Tarea no encontrada');
                    }
                    
                    echo json_encode(['success' => true, 'data' => $tarea]);
                    break;
                    
                case 'obtener_entregas':
                    $contenidoId = $_GET['contenido_id'] ?? null;
                    if (!$contenidoId) {
                        throw new Exception('ID del contenido requerido');
                    }
                    
                    $entregas = $tareaController->obtenerEntregasPorTarea($contenidoId);
                    echo json_encode(['success' => true, 'data' => $entregas]);
                    break;
                    
                case 'obtener_estadisticas':
                    $contenidoId = $_GET['contenido_id'] ?? null;
                    if (!$contenidoId) {
                        throw new Exception('ID del contenido requerido');
                    }
                    
                    $estadisticas = $tareaController->obtenerEstadisticasEntregas($contenidoId);
                    echo json_encode(['success' => true, 'data' => $estadisticas]);
                    break;
                    
                case 'obtener_semanas':
                case 'get_semanas_academicas':
                    $cursoId = $_GET['curso_id'] ?? null;
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    $semanas = $tareaController->obtenerSemanasAcademicas($cursoId);
                    echo json_encode(['success' => true, 'data' => $semanas]);
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'crear_tarea':
                    $datosRequeridos = ['curso_id', 'semana_id', 'tipo', 'titulo', 'descripcion', 'fecha_limite', 'hora_limite', 'puntos'];
                    foreach ($datosRequeridos as $campo) {
                        if (!isset($input[$campo]) || empty($input[$campo])) {
                            throw new Exception("Campo requerido: $campo");
                        }
                    }
                    
                    $resultado = $tareaController->crearTarea($input);
                    if ($resultado) {
                        echo json_encode(['success' => true, 'message' => 'Tarea creada exitosamente', 'id' => $resultado]);
                    } else {
                        throw new Exception('Error al crear la tarea');
                    }
                    break;
                    
                case 'calificar_entrega':
                    $entregaId = $input['entrega_id'] ?? null;
                    $calificacion = $input['calificacion'] ?? null;
                    $comentario = $input['comentario'] ?? '';
                    
                    if (!$entregaId || $calificacion === null) {
                        throw new Exception('ID de entrega y calificación requeridos');
                    }
                    
                    $resultado = $tareaController->calificarEntrega($entregaId, $calificacion, $comentario);
                    if ($resultado) {
                        echo json_encode(['success' => true, 'message' => 'Entrega calificada exitosamente']);
                    } else {
                        throw new Exception('Error al calificar la entrega');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'actualizar_tarea':
                    $tareaId = $_GET['id'] ?? null;
                    if (!$tareaId) {
                        throw new Exception('ID de la tarea requerido');
                    }
                    
                    $datosRequeridos = ['tipo', 'titulo', 'descripcion', 'fecha_limite', 'hora_limite', 'puntos'];
                    foreach ($datosRequeridos as $campo) {
                        if (!isset($input[$campo]) || empty($input[$campo])) {
                            throw new Exception("Campo requerido: $campo");
                        }
                    }
                    
                    $resultado = $tareaController->actualizarTarea($tareaId, $input);
                    if ($resultado) {
                        echo json_encode(['success' => true, 'message' => 'Tarea actualizada exitosamente']);
                    } else {
                        throw new Exception('Error al actualizar la tarea');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'DELETE':
            switch ($action) {
                case 'eliminar_tarea':
                    $tareaId = $_GET['id'] ?? null;
                    if (!$tareaId) {
                        throw new Exception('ID de la tarea requerido');
                    }
                    $resultado = $tareaController->eliminarTarea($tareaId);
                    if ($resultado) {
                        echo json_encode(['success' => true, 'message' => 'Tarea eliminada exitosamente']);
                    } else {
                        throw new Exception('No se pudo eliminar la tarea. Puede que no exista o ya haya sido eliminada.');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        default:
            throw new Exception('Método no permitido');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?> 