/* Estilos específicos para la página de cursos de maestros */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  
    /* Colores para íconos de cursos */
    --math-color: #2196f3;
    --science-color: #4caf50;
    --language-color: #9c27b0;
    --english-color: #00bcd4;
    --history-color: #e91e63;
    --art-color: #ff9800;
    --pe-color: #ff5722;
    --music-color: #795548;
  }
  
  /* Estadísticas */
  .stats-section {
    margin-bottom: 30px;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
  }
  
  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .stat-icon .material-icons {
    color: var(--primary-color);
    font-size: 1.5rem;
  }
  
  .stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 5px 0;
  }
  
  .stat-content p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
  }
  
  /* Sección de cursos */
  .courses-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
  }
  
  .section-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .view-options {
    display: flex;
    gap: 5px;
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 4px;
  }
  
  .view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
  }
  
  .view-btn.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
  }
  
  .view-btn:hover {
    color: var(--primary-color);
  }
  
  /* Filtro y acciones de cursos */
  .courses-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .filter-container {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: var(--shadow-sm);
    flex: 1;
    min-width: 300px;
    flex-wrap: wrap;
  }
  
  .search-box {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 10px 15px;
    min-width: 200px;
  }
  
  .search-box .material-icons {
    color: var(--text-light);
  }
  
  .search-box input {
    flex: 1;
    border: none;
    background: none;
    font-size: 0.9rem;
    color: var(--text-color);
    outline: none;
    width: 100%;
  }
  
  .filter-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  .filter-options select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    cursor: pointer;
  }
  
  .create-course-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .create-course-btn:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
  }
  
  /* Lista de cursos */
  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
  }
  
  .courses-grid.list-view {
    grid-template-columns: 1fr;
  }
  
  .courses-grid.list-view .course-card {
    flex-direction: row;
    height: auto;
  }
  
  .courses-grid.list-view .course-card-header {
    width: 200px;
    height: auto;
    min-height: 150px;
  }
  
  .courses-grid.list-view .course-card-body {
    flex: 1;
    padding: 20px;
  }
  
  .courses-grid.list-view .course-card-footer {
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    min-width: 150px;
  }
  
  .course-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    position: relative;
  }
  
  .course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .course-card-header {
    height: 160px;
    position: relative;
    overflow: hidden;
  }
  
  .course-card-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .course-card:hover .course-card-header img {
    transform: scale(1.05);
  }
  
  .course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-light), #d1e7ff);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .course-placeholder .material-icons {
    font-size: 3rem;
    color: var(--primary-color);
    opacity: 0.7;
  }
  
  .course-icon {
    position: absolute;
    bottom: 15px;
    left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    z-index: 1;
  }
  
  .course-icon .material-icons {
    color: white;
    font-size: 1.8rem;
  }
  
  .course-icon.calculate-icon {
    background-color: var(--math-color);
  }
  
  .course-icon.science-icon {
    background-color: var(--science-color);
  }
  
  .course-icon.menu_book-icon {
    background-color: var(--language-color);
  }
  
  .course-icon.language-icon {
    background-color: var(--english-color);
  }
  
  .course-icon.history_edu-icon {
    background-color: var(--history-color);
  }
  
  .course-icon.brush-icon {
    background-color: var(--art-color);
  }
  
  .course-icon.sports_soccer-icon {
    background-color: var(--pe-color);
  }
  
  .course-icon.music_note-icon {
    background-color: var(--music-color);
  }
  
  .course-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: var(--transition);
  }
  
  .course-card:hover .course-actions {
    opacity: 1;
  }
  
  .course-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    backdrop-filter: blur(10px);
  }
  
  .course-action-btn .material-icons {
    font-size: 1.2rem;
  }
  
  .course-action-btn:hover {
    transform: scale(1.1);
  }
  
  .edit-btn:hover {
    background: var(--primary-color);
    color: white;
  }
  
  .delete-btn:hover {
    background: var(--danger-color);
    color: white;
  }
  
  .course-status {
    position: absolute;
    top: 15px;
    left: 15px;
  }
  
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
  }
  
  .status-badge.active {
    background: var(--success-color);
    color: white;
  }
  
  .course-card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .course-card-body h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 8px 0;
    line-height: 1.3;
  }
  
  .course-grade {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
    margin: 0 0 15px 0;
  }
  
  .course-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
  }
  
  .course-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .course-detail .material-icons {
    font-size: 1rem;
    color: var(--text-light);
  }
  
  .course-card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
  }

  .view-course-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
  }

  .view-course-btn:hover {
    background: #1e40af;
    transform: translateY(-1px);
  }

  .students-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: #4caf50;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
  }

  .students-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
  }
  
  .course-actions-footer {
    display: flex;
    gap: 5px;
  }
  
  .action-btn {
    width: 35px;
    height: 35px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
  }
  
  .action-btn:hover {
    background: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .action-btn .material-icons {
    font-size: 1.1rem;
  }
  
  .no-courses {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    border: 2px dashed var(--border-color);
  }
  
  .no-courses-icon {
    margin-bottom: 20px;
  }
  
  .no-courses-icon .material-icons {
    font-size: 4rem;
    color: var(--text-light);
    opacity: 0.5;
  }
  
  .no-courses h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 10px 0;
  }
  
  .no-courses p {
    color: var(--text-light);
    margin: 0 0 25px 0;
    font-size: 1rem;
    line-height: 1.5;
  }
  
  /* Notificaciones */
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--success-color);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    transform: translateX(100%);
    transition: var(--transition);
    max-width: 400px;
  }
  
  .notification.show {
    transform: translateX(0);
  }
  
  .notification-success {
    border-left-color: var(--success-color);
  }
  
  .notification-error {
    border-left-color: var(--danger-color);
  }
  
  .notification .material-icons {
    font-size: 1.2rem;
  }
  
  .notification-success .material-icons {
    color: var(--success-color);
  }
  
  .notification-error .material-icons {
    color: var(--danger-color);
  }
  
  /* Modales */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .modal-overlay.active {
    opacity: 1;
    visibility: visible;
  }
  
  .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
  }
  
  .modal-overlay.active .modal-content {
    transform: scale(1);
  }
  
  .modal-small {
    max-width: 400px;
  }
  
  @keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
  }
  
  .modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-close-btn:hover {
    background: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
  
  .form-row .form-group {
    margin-bottom: 0;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
  }
  
  .form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--text-light);
    line-height: 1.4;
  }
  
  .form-group input[type="text"],
  .form-group input[type="time"],
  .form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    color: var(--text-color);
    background: white;
    transition: var(--transition);
    box-sizing: border-box;
  }
  
  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 77, 183, 0.1);
  }
  
  .form-group input:disabled,
  .form-group select:disabled {
    background: var(--secondary-color);
    color: var(--text-light);
    cursor: not-allowed;
  }
  
  .schedule-inputs {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
  }
  
  .schedule-day {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--secondary-color);
  }
  
  .schedule-day label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    margin: 0;
    min-width: 100px;
  }
  
  .schedule-day label:hover {
    color: var(--primary-color);
  }
  
  .schedule-day input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
  }
  
  .time-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
  }
  
  .time-inputs input[type="time"] {
    flex: 1;
    max-width: 120px;
  }
  
  .time-inputs span {
    color: var(--text-light);
    font-weight: 500;
  }
  
  .file-upload {
    position: relative;
    margin-top: 10px;
  }
  
  .file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  
  .file-upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--secondary-color);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    justify-content: center;
  }
  
  .file-upload:hover .file-upload-btn {
    border-color: var(--primary-color);
    background: var(--primary-light);
    color: var(--primary-color);
  }
  
  .form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
  
  .btn-secondary,
  .btn-primary,
  .btn-danger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
  }
  
  .btn-secondary {
    background: var(--secondary-color);
    color: var(--text-color);
  }
  
  .btn-secondary:hover {
    background: #e0e0e0;
  }
  
  .btn-primary {
    background: var(--primary-color);
    color: white;
  }
  
  .btn-primary:hover {
    background: #1e40af;
  }
  
  .btn-danger {
    background: var(--danger-color);
    color: white;
  }
  
  .btn-danger:hover {
    background: #d32f2f;
  }
  
  /* Modal de eliminación */
  .delete-warning {
    text-align: center;
    margin-bottom: 25px;
  }
  
  .delete-warning .material-icons {
    font-size: 3rem;
    color: var(--warning-color);
    margin-bottom: 15px;
  }
  
  .delete-warning p {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    color: var(--text-color);
  }
  
  .warning-text {
    color: var(--text-light) !important;
    font-size: 0.9rem !important;
    line-height: 1.5;
  }
  
  /* Modal de carga */
  .loading-spinner {
    text-align: center;
    padding: 40px 20px;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-spinner p {
    color: var(--text-light);
    margin: 0;
  }
  
  /* Vista previa de imagen */
  .image-preview-container {
    margin-top: 15px;
  }
  
  .image-preview {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  .preview-image {
    max-width: 200px;
    max-height: 150px;
    object-fit: cover;
    display: block;
  }
  
  .remove-image-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
  }
  
  .remove-image-btn:hover {
    background: var(--danger-color);
    transform: scale(1.1);
  }
  
  .remove-image-btn .material-icons {
    font-size: 1rem;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .courses-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-container {
        min-width: auto;
    }
    
    .create-course-btn {
        align-self: flex-start;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .schedule-day {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .time-inputs {
        width: 100%;
        justify-content: space-between;
    }
  }
  
  @media (max-width: 768px) {
    .filter-options {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-options select {
        width: 100%;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .create-course-btn {
        width: 100%;
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn-secondary,
    .btn-primary,
    .btn-danger {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .preview-image {
        max-width: 100%;
        height: auto;
    }
  }
  
  