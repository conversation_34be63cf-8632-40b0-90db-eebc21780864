document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const solicitudesTable = document.getElementById("solicitudes-table");
    const solicitudesTableBody = document.getElementById("solicitudes-table-body");
    const searchInput = document.getElementById("search-input");
    const nivelSelect = document.getElementById("nivel-select");
    const exportBtn = document.getElementById("export-btn");
    const totalCountElement = document.getElementById("total-count");
    const paginationContainer = document.getElementById("pagination");
    const itemsPerPageSelect = document.getElementById("items-per-page");
    const solicitudModal = document.getElementById("solicitud-modal");
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn");
    const aprobarSolicitudBtn = document.getElementById("aprobar-solicitud-btn");
    
    // Verificar que los elementos existen
    console.log("Elementos encontrados:");
    console.log("- solicitudesTableBody:", !!solicitudesTableBody);
    console.log("- searchInput:", !!searchInput);
    console.log("- nivelSelect:", !!nivelSelect);
    console.log("- totalCountElement:", !!totalCountElement);
    console.log("- solicitudModal:", !!solicitudModal);
  
    // Variables de estado
    let currentPage = 1;
    let itemsPerPage = 10;
    let totalPages = 1;
    let solicitudes = [];
    let filteredSolicitudes = [];
    let currentSolicitudId = null;
  
    // Inicializar la página
    init();
  
    function init() {
      // Cargar datos de solicitudes (simulado)
      loadSolicitudes();

      // Configurar event listeners
      setupEventListeners();
    }
  
    function setupEventListeners() {
      // Búsqueda en tiempo real
      if (searchInput) {
        searchInput.addEventListener("input", handleSearch);
      }

      // Filtro de nivel
      if (nivelSelect) {
        nivelSelect.addEventListener("change", handleSearch);
      }

      // Exportar datos
      if (exportBtn) {
        exportBtn.addEventListener("click", exportData);
      }

      // Cambiar items por página
      if (itemsPerPageSelect) {
        itemsPerPageSelect.addEventListener("change", changeItemsPerPage);
      }

      // Cerrar modales
      modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", closeModals);
      });

      // Aprobar solicitud
      if (aprobarSolicitudBtn) {
        aprobarSolicitudBtn.addEventListener("click", aprobarSolicitud);
      }

      // Delegación de eventos para botones de acción en la tabla
      if (solicitudesTable) {
        solicitudesTable.addEventListener("click", handleTableActions);
      }
    }
  
    // Cargar solicitudes (datos simulados)
    function loadSolicitudes() {
      console.log("Cargando solicitudes...");

      // Simulación de datos de solicitudes de preinscripción
      solicitudes = [
        {
          id: 1,
          nombre: "Juan",
          apellidoPaterno: "Pérez",
          apellidoMaterno: "González",
          dniEstudiante: "12345678",
          dniPadre: "87654321",
          correoApoderado: "<EMAIL>",
          telefonoApoderado: "991 663 041",
          grado: "inicial-4",
          gradoNombre: "Inicial 4 años",
          fecha: "2023-05-01",
          estado: "pendiente",
          notas: ""
        },
        {
          id: 2,
          nombre: "María",
          apellidoPaterno: "Rodríguez",
          apellidoMaterno: "López",
          dniEstudiante: "23456789",
          dniPadre: "98765432",
          correoApoderado: "<EMAIL>",
          telefonoApoderado: "992 456 789",
          grado: "primaria-2",
          gradoNombre: "2° Primaria",
          fecha: "2023-05-02",
          estado: "pendiente",
          notas: ""
        },
        {
          id: 3,
          nombre: "Carlos",
          apellidoPaterno: "Sánchez",
          apellidoMaterno: "Martínez",
          dniEstudiante: "34567890",
          dniPadre: "09876543",
          correoApoderado: "<EMAIL>",
          telefonoApoderado: "993 789 123",
          grado: "primaria-5",
          gradoNombre: "5° Primaria",
          fecha: "2023-05-03",
          estado: "pendiente",
          notas: "Contactado por teléfono el 05/05/2023. Programada visita para el 10/05/2023."
        },
        {
          id: 4,
          nombre: "Ana",
          apellidoPaterno: "Gómez",
          apellidoMaterno: "Fernández",
          dniEstudiante: "45678901",
          dniPadre: "10987654",
          correoApoderado: "<EMAIL>",
          telefonoApoderado: "994 321 654",
          grado: "inicial-3",
          gradoNombre: "Inicial 3 años",
          fecha: "2023-05-04",
          estado: "pendiente",
          notas: ""
        },
        {
          id: 5,
          nombre: "Roberto",
          apellidoPaterno: "Torres",
          apellidoMaterno: "Díaz",
          dniEstudiante: "56789012",
          dniPadre: "21098765",
          correoApoderado: "<EMAIL>",
          telefonoApoderado: "995 654 987",
          grado: "primaria-1",
          gradoNombre: "1° Primaria",
          fecha: "2023-05-05",
          estado: "pendiente",
          notas: ""
        }
      ];
  
      // Generar algunos datos adicionales para demostración
      for (let i = 6; i <= 15; i++) {
        const grados = [
          { id: "inicial-3", nombre: "Inicial 3 años" },
          { id: "inicial-4", nombre: "Inicial 4 años" },
          { id: "inicial-5", nombre: "Inicial 5 años" },
          { id: "primaria-1", nombre: "1° Primaria" },
          { id: "primaria-2", nombre: "2° Primaria" },
          { id: "primaria-3", nombre: "3° Primaria" }
        ];

        const nombres = ["Luis", "Pedro", "Sofia", "Carmen", "Miguel", "Lucia", "Diego", "Valeria"];
        const apellidos = ["García", "Martínez", "López", "Rodríguez", "González", "Fernández"];

        const nombre = nombres[Math.floor(Math.random() * nombres.length)];
        const apellidoPaterno = apellidos[Math.floor(Math.random() * apellidos.length)];
        const apellidoMaterno = apellidos[Math.floor(Math.random() * apellidos.length)];
        const grado = grados[Math.floor(Math.random() * grados.length)];

        solicitudes.push({
          id: i,
          nombre: nombre,
          apellidoPaterno: apellidoPaterno,
          apellidoMaterno: apellidoMaterno,
          dniEstudiante: Math.floor(10000000 + Math.random() * 90000000).toString(),
          dniPadre: Math.floor(10000000 + Math.random() * 90000000).toString(),
          correoApoderado: `apoderado${i}@example.com`,
          telefonoApoderado: `99${Math.floor(1000000 + Math.random() * 9000000)}`,
          grado: grado.id,
          gradoNombre: grado.nombre,
          fecha: `2025-06-${Math.floor(1 + Math.random() * 24).toString().padStart(2, '0')}`,
          estado: "pendiente",
          notas: ""
        });
      }

      console.log("Solicitudes cargadas:", solicitudes.length);

      // Ordenar por fecha (más reciente primero)
      solicitudes.sort((a, b) => new Date(b.fecha) - new Date(a.fecha));

      // Inicializar filteredSolicitudes
      filteredSolicitudes = [...solicitudes];

      console.log("Solicitudes filtradas:", filteredSolicitudes.length);

      // Actualizar tabla inicial
      updateTable();
    }
  

  
    // Actualizar la tabla con los datos filtrados y paginados
    function updateTable() {
      console.log("Actualizando tabla...");

      if (!solicitudesTableBody) {
        console.error("No se encontró el elemento solicitudesTableBody");
        return;
      }

      // Calcular paginación
      totalPages = Math.ceil(filteredSolicitudes.length / itemsPerPage);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedSolicitudes = filteredSolicitudes.slice(startIndex, endIndex);

      console.log("Solicitudes paginadas:", paginatedSolicitudes.length);

      // Limpiar tabla
      solicitudesTableBody.innerHTML = "";

      // Actualizar contadores
      updateCounters();

      // Verificar si hay solicitudes
      if (paginatedSolicitudes.length === 0) {
        console.log("No hay solicitudes para mostrar");
        const emptyRow = document.createElement("tr");
        emptyRow.innerHTML = `
          <td colspan="8" class="empty-table">
            <div class="empty-state">
              <span class="material-icons">search_off</span>
              <p>No se encontraron solicitudes que coincidan con los criterios de búsqueda.</p>
              <button type="button" class="btn-secondary" onclick="location.reload()">Actualizar</button>
            </div>
          </td>
        `;
        solicitudesTableBody.appendChild(emptyRow);
      } else {
        console.log("Agregando filas a la tabla...");
        // Agregar filas a la tabla
        paginatedSolicitudes.forEach(solicitud => {
          const row = document.createElement("tr");
          row.dataset.id = solicitud.id;

          // Construir nombre completo
          const nombreCompleto = `${solicitud.nombre} ${solicitud.apellidoPaterno} ${solicitud.apellidoMaterno}`.trim();

          row.innerHTML = `
            <td>#${solicitud.id}</td>
            <td>
              <div class="solicitud-info">
                <div class="solicitud-nombre">${nombreCompleto}</div>
              </div>
            </td>
            <td>${solicitud.dniEstudiante}</td>
            <td>${solicitud.dniPadre}</td>
            <td>
              <div class="solicitud-info">
                <div class="solicitud-email">${solicitud.correoApoderado}</div>
                <div class="solicitud-telefono">${solicitud.telefonoApoderado}</div>
              </div>
            </td>
            <td>${solicitud.gradoNombre}</td>
            <td>${formatDate(solicitud.fecha)}</td>
            <td>
              <div class="table-actions">
                <button type="button" class="action-btn view-btn" title="Ver detalles" data-id="${solicitud.id}">
                  <span class="material-icons">visibility</span>
                </button>
                <button type="button" class="action-btn mark-btn" title="Aprobar solicitud" data-id="${solicitud.id}">
                  <span class="material-icons">check_circle</span>
                </button>
              </div>
            </td>
          `;

          solicitudesTableBody.appendChild(row);
        });
      }
      
      // Actualizar paginación
      updatePagination();
    }
  
    // Actualizar contadores
    function updateCounters() {
      if (totalCountElement) {
        totalCountElement.textContent = filteredSolicitudes.length;
      }
    }
  
    // Actualizar paginación
    function updatePagination() {
      if (!paginationContainer) return;
      
      paginationContainer.innerHTML = "";
      
      if (totalPages <= 1) return;
      
      // Botón anterior
      const prevBtn = document.createElement("button");
      prevBtn.type = "button";
      prevBtn.className = "pagination-btn prev-btn";
      prevBtn.disabled = currentPage === 1;
      prevBtn.innerHTML = `<span class="material-icons">chevron_left</span>`;
      prevBtn.addEventListener("click", () => {
        if (currentPage > 1) {
          currentPage--;
          updateTable();
        }
      });
      paginationContainer.appendChild(prevBtn);
      
      // Números de página
      const maxVisiblePages = 5;
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      // Primera página
      if (startPage > 1) {
        const firstPageBtn = document.createElement("button");
        firstPageBtn.type = "button";
        firstPageBtn.className = "pagination-btn page-btn";
        firstPageBtn.textContent = "1";
        firstPageBtn.addEventListener("click", () => {
          currentPage = 1;
          updateTable();
        });
        paginationContainer.appendChild(firstPageBtn);
        
        if (startPage > 2) {
          const ellipsis = document.createElement("span");
          ellipsis.className = "pagination-ellipsis";
          ellipsis.textContent = "...";
          paginationContainer.appendChild(ellipsis);
        }
      }
      
      // Páginas visibles
      for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement("button");
        pageBtn.type = "button";
        pageBtn.className = `pagination-btn page-btn ${i === currentPage ? "active" : ""}`;
        pageBtn.textContent = i.toString();
        pageBtn.addEventListener("click", () => {
          currentPage = i;
          updateTable();
        });
        paginationContainer.appendChild(pageBtn);
      }
      
      // Última página
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          const ellipsis = document.createElement("span");
          ellipsis.className = "pagination-ellipsis";
          ellipsis.textContent = "...";
          paginationContainer.appendChild(ellipsis);
        }
        
        const lastPageBtn = document.createElement("button");
        lastPageBtn.type = "button";
        lastPageBtn.className = "pagination-btn page-btn";
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener("click", () => {
          currentPage = totalPages;
          updateTable();
        });
        paginationContainer.appendChild(lastPageBtn);
      }
      
      // Botón siguiente
      const nextBtn = document.createElement("button");
      nextBtn.type = "button";
      nextBtn.className = "pagination-btn next-btn";
      nextBtn.disabled = currentPage === totalPages;
      nextBtn.innerHTML = `<span class="material-icons">chevron_right</span>`;
      nextBtn.addEventListener("click", () => {
        if (currentPage < totalPages) {
          currentPage++;
          updateTable();
        }
      });
      paginationContainer.appendChild(nextBtn);
    }
  
    // Manejar búsqueda y filtros
    function handleSearch() {
      const searchTerm = searchInput.value.toLowerCase().trim();
      const nivelValue = nivelSelect.value;

      // Empezar con todas las solicitudes pendientes
      filteredSolicitudes = [...solicitudes];

      // Aplicar filtro de búsqueda
      if (searchTerm !== "") {
        filteredSolicitudes = filteredSolicitudes.filter(solicitud => {
          const nombreCompleto = `${solicitud.nombre} ${solicitud.apellidoPaterno} ${solicitud.apellidoMaterno}`.toLowerCase();
          return (
            nombreCompleto.includes(searchTerm) ||
            solicitud.dniEstudiante.includes(searchTerm) ||
            solicitud.dniPadre.includes(searchTerm) ||
            solicitud.correoApoderado.toLowerCase().includes(searchTerm) ||
            solicitud.telefonoApoderado.includes(searchTerm) ||
            solicitud.id.toString().includes(searchTerm)
          );
        });
      }

      // Aplicar filtro de nivel
      if (nivelValue !== "todos") {
        filteredSolicitudes = filteredSolicitudes.filter(solicitud => {
          if (nivelValue === "inicial") {
            return solicitud.grado.startsWith("inicial");
          } else if (nivelValue === "primaria") {
            return solicitud.grado.startsWith("primaria");
          }
          return true;
        });
      }

      // Resetear a la primera página
      currentPage = 1;

      // Actualizar tabla
      updateTable();
    }
  
    // Aprobar solicitud
    function aprobarSolicitud() {
      if (!currentSolicitudId) return;

      // Mostrar confirmación
      if (confirm("¿Está seguro de que desea aprobar esta solicitud? Esta acción eliminará la solicitud de la lista de pendientes.")) {
        // Encontrar la solicitud
        const solicitudIndex = solicitudes.findIndex(s => s.id === currentSolicitudId);

        if (solicitudIndex !== -1) {
          // Eliminar la solicitud de la lista
          solicitudes.splice(solicitudIndex, 1);

          // Actualizar filteredSolicitudes
          filteredSolicitudes = [...solicitudes];

          // Aplicar filtros actuales
          handleSearch();

          // Cerrar modal
          closeModals();

          // Mostrar mensaje de éxito
          showSuccessMessage("Solicitud aprobada exitosamente");
        }
      }
    }

    // Mostrar mensaje de éxito
    function showSuccessMessage(message) {
      // Crear elemento de notificación
      const notification = document.createElement('div');
      notification.className = 'success-notification';
      notification.innerHTML = `
        <div class="notification-content">
          <span class="material-icons">check_circle</span>
          <span>${message}</span>
        </div>
      `;

      // Agregar estilos
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
      `;

      // Agregar al DOM
      document.body.appendChild(notification);

      // Eliminar después de 3 segundos
      setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }
  
    // Exportar datos a Excel
    function exportData() {
      try {
        console.log("Iniciando exportación a Excel...");

        // Preparar datos para exportación
        const dataToExport = filteredSolicitudes.map(solicitud => {
          return {
            'ID': solicitud.id,
            'Nombre Completo': `${solicitud.nombre} ${solicitud.apellidoPaterno} ${solicitud.apellidoMaterno}`,
            'DNI Estudiante': solicitud.dniEstudiante,
            'DNI Padre': solicitud.dniPadre,
            'Correo Apoderado': solicitud.correoApoderado,
            'Teléfono Apoderado': solicitud.telefonoApoderado,
            'Grado': solicitud.gradoNombre,
            'Fecha de Solicitud': formatDate(solicitud.fecha)
          };
        });

        // Crear libro de trabajo
        const wb = XLSX.utils.book_new();

        // Crear hoja de trabajo
        const ws = XLSX.utils.json_to_sheet(dataToExport);

        // Configurar ancho de columnas
        const colWidths = [
          { wch: 8 },   // ID
          { wch: 25 },  // Nombre Completo
          { wch: 15 },  // DNI Estudiante
          { wch: 15 },  // DNI Padre
          { wch: 30 },  // Correo
          { wch: 15 },  // Teléfono
          { wch: 20 },  // Grado
          { wch: 15 }   // Fecha
        ];
        ws['!cols'] = colWidths;

        // Agregar hoja al libro
        XLSX.utils.book_append_sheet(wb, ws, "Solicitudes Pendientes");

        // Generar nombre de archivo con fecha actual
        const fecha = new Date().toISOString().split('T')[0];
        const nombreArchivo = `solicitudes_preinscripcion_${fecha}.xlsx`;

        // Descargar archivo
        XLSX.writeFile(wb, nombreArchivo);

        // Mostrar mensaje de éxito
        showSuccessMessage(`Archivo Excel exportado: ${nombreArchivo}`);

        console.log("Exportación completada:", nombreArchivo);

      } catch (error) {
        console.error("Error al exportar:", error);
        alert("Error al exportar los datos. Por favor, inténtelo de nuevo.");
      }
    }
  
    // Cambiar items por página
    function changeItemsPerPage() {
      itemsPerPage = parseInt(itemsPerPageSelect.value);
      currentPage = 1;
      updateTable();
    }
  
    // Manejar acciones de la tabla
    function handleTableActions(e) {
      const target = e.target.closest("button");

      if (!target) return;

      const id = parseInt(target.dataset.id);

      if (target.classList.contains("view-btn")) {
        viewSolicitud(id);
      } else if (target.classList.contains("mark-btn")) {
        aprobarSolicitudDirecto(id);
      }
    }

    // Aprobar solicitud directamente desde la tabla
    function aprobarSolicitudDirecto(id) {
      if (confirm("¿Está seguro de que desea aprobar esta solicitud? Esta acción eliminará la solicitud de la lista de pendientes.")) {
        // Encontrar la solicitud
        const solicitudIndex = solicitudes.findIndex(s => s.id === id);

        if (solicitudIndex !== -1) {
          // Eliminar la solicitud de la lista
          solicitudes.splice(solicitudIndex, 1);

          // Actualizar filteredSolicitudes
          filteredSolicitudes = [...solicitudes];

          // Aplicar filtros actuales
          handleSearch();

          // Mostrar mensaje de éxito
          showSuccessMessage("Solicitud aprobada exitosamente");
        }
      }
    }
  
    // Ver solicitud
    function viewSolicitud(id) {
      const solicitud = solicitudes.find(s => s.id === id);
      
      if (!solicitud) return;
      
      // Guardar ID actual
      currentSolicitudId = id;
      
      // Actualizar modal con datos de la solicitud
      document.getElementById("solicitud-id").textContent = solicitud.id;
      document.getElementById("solicitud-fecha").textContent = formatDate(solicitud.fecha);
      
      // Actualizar información del estudiante
      document.getElementById("solicitud-nombre").textContent = solicitud.nombre;
      document.getElementById("solicitud-apellido-paterno").textContent = solicitud.apellidoPaterno;
      document.getElementById("solicitud-apellido-materno").textContent = solicitud.apellidoMaterno;
      document.getElementById("solicitud-dni-estudiante").textContent = solicitud.dniEstudiante;
      
      // Actualizar información del apoderado
      document.getElementById("solicitud-dni-padre").textContent = solicitud.dniPadre;
      document.getElementById("solicitud-email").textContent = solicitud.correoApoderado;
      document.getElementById("solicitud-telefono").textContent = solicitud.telefonoApoderado;
      
      // Actualizar información académica
      document.getElementById("solicitud-grado").textContent = solicitud.gradoNombre;

      // Mostrar modal
      solicitudModal.classList.add("active");
    }
  

  

  
    // Cerrar modales
    function closeModals() {
      solicitudModal.classList.remove("active");
    }
  
    // Formatear fecha
    function formatDate(dateString) {
      if (!dateString) return "";
      
      const date = new Date(dateString);
      return date.toLocaleDateString("es-ES", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    }
  
    // Capitalizar primera letra
    function capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    }
  });