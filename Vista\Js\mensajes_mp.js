document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const studentSelect = document.getElementById("student-select")
    const contactItems = document.querySelectorAll(".contact-item")
    const searchInput = document.querySelector(".search-box input")
    const chatInput = document.querySelector(".chat-input input")
    const sendButton = document.querySelector(".chat-send-btn")
    const chatMessages = document.querySelector(".chat-messages")
  
    // Función para obtener la hora actual en formato HH:MM
    const getCurrentTime = () => {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, "0")
      const minutes = now.getMinutes().toString().padStart(2, "0")
      return `${hours}:${minutes}`
    }
  
    // Agregar indicador de scroll para mensajes anteriores
    if (chatMessages) {
      // Crear el indicador de scroll
      const scrollIndicator = document.createElement("div")
      scrollIndicator.className = "scroll-indicator"
      scrollIndicator.innerHTML = '<span class="material-icons">keyboard_arrow_down</span>'
  
      // Agregar al DOM
      chatMessages.appendChild(scrollIndicator)
  
      // Inicialmente oculto
      scrollIndicator.style.display = "none"
  
      // Mostrar/ocultar según la posición del scroll
      chatMessages.addEventListener("scroll", () => {
        const isScrollable = chatMessages.scrollHeight > chatMessages.clientHeight
        const isScrolledUp = chatMessages.scrollTop < chatMessages.scrollHeight - chatMessages.clientHeight - 50
  
        if (isScrollable && isScrolledUp) {
          scrollIndicator.style.display = "flex"
        } else {
          scrollIndicator.style.display = "none"
        }
      })
  
      // Hacer scroll al final al hacer clic
      scrollIndicator.addEventListener("click", () => {
        chatMessages.scrollTop = chatMessages.scrollHeight
      })
    }
  
    // Cambiar estudiante seleccionado
    if (studentSelect) {
      studentSelect.addEventListener("change", () => {
        const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
        console.log(`Estudiante seleccionado: ${selectedStudent}`)
  
        // Filtrar contactos según el estudiante seleccionado
        if (selectedStudent.includes("Todos los estudiantes")) {
          // Mostrar todos los contactos
          contactItems.forEach((item) => {
            item.style.display = "flex"
          })
        } else {
          // Filtrar contactos según el estudiante seleccionado
          const studentName = selectedStudent.split(" - ")[0]
          contactItems.forEach((item) => {
            const contactName = item.querySelector(".contact-name").textContent
            if (contactName.includes(studentName)) {
              item.style.display = "flex"
            } else {
              item.style.display = "none"
            }
          })
        }
      })
    }
  
    // Seleccionar contacto
    if (contactItems.length > 0) {
      contactItems.forEach((item) => {
        item.addEventListener("click", () => {
          // Remover clase active de todos los contactos
          contactItems.forEach((contact) => {
            contact.classList.remove("active")
          })
  
          // Agregar clase active al contacto seleccionado
          item.classList.add("active")
  
          // Obtener nombre del contacto
          const contactName = item.querySelector(".contact-name").textContent
  
          // Actualizar encabezado del chat
          const chatContactName = document.querySelector(".chat-contact .contact-name")
          if (chatContactName) {
            chatContactName.textContent = contactName
          }
  
          // Quitar badge de mensajes no leídos
          const contactBadge = item.querySelector(".contact-badge")
          if (contactBadge) {
            contactBadge.remove()
          }
  
          console.log(`Contacto seleccionado: ${contactName}`)
        })
      })
    }
  
    // Buscar contactos
    if (searchInput) {
      searchInput.addEventListener("input", () => {
        const searchTerm = searchInput.value.toLowerCase()
  
        contactItems.forEach((item) => {
          const contactName = item.querySelector(".contact-name").textContent.toLowerCase()
  
          if (contactName.includes(searchTerm)) {
            item.style.display = "flex"
          } else {
            item.style.display = "none"
          }
        })
      })
    }
  
    // Función para enviar mensaje
    const sendMessage = () => {
      if (chatInput && chatInput.value.trim() !== "") {
        const messageText = chatInput.value.trim()
  
        // Crear nuevo mensaje
        const messageHTML = `
          <div class="message sent">
            <div class="message-content">
              <div class="message-bubble">
                ${messageText}
              </div>
              <div class="message-time">${getCurrentTime()}</div>
            </div>
          </div>
        `
  
        // Agregar mensaje al chat
        if (chatMessages) {
          chatMessages.insertAdjacentHTML("beforeend", messageHTML)
          chatMessages.scrollTop = chatMessages.scrollHeight
        }
  
        // Limpiar input
        chatInput.value = ""
  
        // Simular respuesta después de 1 segundo
        setTimeout(() => {
          const responseHTML = `
            <div class="message received">
              <div class="message-content">
                <div class="message-bubble">
                  Gracias por su mensaje. Le responderé a la brevedad.
                </div>
                <div class="message-time">${getCurrentTime()}</div>
              </div>
            </div>
          `
  
          if (chatMessages) {
            chatMessages.insertAdjacentHTML("beforeend", responseHTML)
            chatMessages.scrollTop = chatMessages.scrollHeight
          }
        }, 1000)
      }
    }
  
    // Evento para enviar mensaje con el botón
    if (sendButton) {
      sendButton.addEventListener("click", sendMessage)
    }
  
    // Evento para enviar mensaje con Enter
    if (chatInput) {
      chatInput.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
          sendMessage()
        }
      })
    }
  
    // Hacer scroll al final del chat al cargar la página
    if (chatMessages) {
      chatMessages.scrollTop = chatMessages.scrollHeight
    }
  })  