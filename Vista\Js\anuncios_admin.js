document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const busquedaTitulo = document.getElementById("busqueda-titulo");
    const filtroEstado = document.getElementById("filtro-estado");
    const anuncioModal = document.getElementById("anuncio-modal");
    const confirmacionModal = document.getElementById("confirmacion-modal");
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn");
    const confirmarContinuarBtn = document.getElementById("confirmar-continuar-btn");
    
    // Configurar fecha actual en la cabecera
    const currentDateElement = document.querySelector(".current-date");
    if (currentDateElement) {
      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      const today = new Date();
      currentDateElement.textContent = today.toLocaleDateString('es-ES', options);
    }
    
    // Filtrar anuncios
    const filtrarAnuncios = () => {
      const textoBusqueda = busquedaTitulo.value.toLowerCase();
      const estadoSeleccionado = filtroEstado.value;

      // En una implementación real, aquí se enviaría una petición al servidor con los filtros
      console.log(`Filtros aplicados: Texto="${textoBusqueda}", Estado=${estadoSeleccionado}`);

      // Aquí iría la lógica para filtrar los anuncios en la tabla
      const filas = document.querySelectorAll(".anuncios-tabla tbody tr");

      filas.forEach(fila => {
        const titulo = fila.querySelector(".anuncio-info h3").textContent.toLowerCase();
        const estado = fila.querySelector(".badge-estado").textContent.toLowerCase();

        let mostrar = true;

        if (textoBusqueda && !titulo.includes(textoBusqueda)) {
          mostrar = false;
        }

        if (estadoSeleccionado !== "todos" && estado.toLowerCase() !== estadoSeleccionado) {
          mostrar = false;
        }

        fila.style.display = mostrar ? "" : "none";
      });
    };
    
    // Eventos para los filtros
    if (busquedaTitulo) {
      busquedaTitulo.addEventListener("input", filtrarAnuncios);
    }

    if (filtroEstado) {
      filtroEstado.addEventListener("change", filtrarAnuncios);
    }
    
    // Abrir modal de anuncio
    const abrirModal = (anuncioId) => {
      // En una implementación real, aquí se cargarían los detalles del anuncio desde el servidor
      anuncioModal.classList.add("active");
      document.body.style.overflow = "hidden"; // Evitar scroll en el body
    };
    
    // Eventos para abrir modal al hacer clic en "Ver"
    const verBtns = document.querySelectorAll(".ver-btn");
    if (verBtns.length > 0) {
      verBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          // Obtener el ID del anuncio desde el atributo data o desde la fila
          const fila = btn.closest("tr");
          const titulo = fila.querySelector(".anuncio-info h3").textContent;
          console.log(`Ver anuncio: ${titulo}`);
          abrirModal();
        });
      });
    }
    
    // Eventos para editar anuncio
    const editarBtns = document.querySelectorAll(".editar-btn");
    if (editarBtns.length > 0) {
      editarBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          const fila = btn.closest("tr");
          const titulo = fila.querySelector(".anuncio-info h3").textContent;
          console.log(`Editar anuncio: ${titulo}`);

          // Generar un ID único basado en el título para simular la edición
          const anuncioId = titulo.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');

          // Redireccionar a la página de edición con parámetros
          window.location.href = `crear_anuncio.html?edit=true&id=${anuncioId}&titulo=${encodeURIComponent(titulo)}`;
        });
      });
    }
    
    // Eventos para eliminar anuncio
    const eliminarBtns = document.querySelectorAll(".eliminar-btn");
    if (eliminarBtns.length > 0) {
      eliminarBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          const fila = btn.closest("tr");
          const titulo = fila.querySelector(".anuncio-info h3").textContent;
          console.log(`Eliminar anuncio: ${titulo}`);
          
          // Mostrar modal de confirmación
          confirmacionModal.classList.add("active");
          document.getElementById("confirmacion-mensaje").textContent = `¿Está seguro que desea eliminar el anuncio "${titulo}"? Esta acción no se puede deshacer.`;
          
          // Guardar referencia a la fila para eliminarla si se confirma
          confirmarContinuarBtn.dataset.fila = Array.from(fila.parentNode.children).indexOf(fila);
        });
      });
    }
    
    // Eventos para restaurar anuncio
    const restaurarBtns = document.querySelectorAll(".restaurar-btn");
    if (restaurarBtns.length > 0) {
      restaurarBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          const fila = btn.closest("tr");
          const titulo = fila.querySelector(".anuncio-info h3").textContent;
          console.log(`Restaurar anuncio: ${titulo}`);
          
          // Aquí iría la lógica para restaurar el anuncio
          alert(`El anuncio "${titulo}" ha sido restaurado.`);
          
          // Cambiar el estado del anuncio en la interfaz
          const estadoBadge = fila.querySelector(".badge-estado");
          estadoBadge.textContent = "Publicado";
          estadoBadge.className = "badge badge-estado badge-publicado";
          
          // Cambiar el botón de restaurar por el de editar
          const accionesGrupo = btn.closest(".acciones-grupo");
          accionesGrupo.innerHTML = `
            <button class="accion-btn ver-btn" title="Ver anuncio">
              <span class="material-icons">visibility</span>
            </button>
            <button class="accion-btn editar-btn" title="Editar anuncio">
              <span class="material-icons">edit</span>
            </button>
            <button class="accion-btn eliminar-btn" title="Eliminar anuncio">
              <span class="material-icons">delete</span>
            </button>
          `;
          
          // Volver a agregar los event listeners a los nuevos botones
          accionesGrupo.querySelector(".ver-btn").addEventListener("click", () => abrirModal());
          accionesGrupo.querySelector(".editar-btn").addEventListener("click", () => {
            console.log(`Editar anuncio: ${titulo}`);
            const anuncioId = titulo.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
            window.location.href = `crear_anuncio.html?edit=true&id=${anuncioId}&titulo=${encodeURIComponent(titulo)}`;
          });
          accionesGrupo.querySelector(".eliminar-btn").addEventListener("click", () => {
            console.log(`Eliminar anuncio: ${titulo}`);
            confirmacionModal.classList.add("active");
            document.getElementById("confirmacion-mensaje").textContent = `¿Está seguro que desea eliminar el anuncio "${titulo}"? Esta acción no se puede deshacer.`;
            confirmarContinuarBtn.dataset.fila = Array.from(fila.parentNode.children).indexOf(fila);
          });
        });
      });
    }
    

    
    // Cerrar modales
    if (modalCloseBtns.length > 0) {
      modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          anuncioModal.classList.remove("active");
          confirmacionModal.classList.remove("active");
          document.body.style.overflow = ""; // Restaurar scroll en el body
        });
      });
    }
    
    // Cerrar modales al hacer clic fuera del contenido
    window.addEventListener("click", (e) => {
      if (e.target === anuncioModal) {
        anuncioModal.classList.remove("active");
        document.body.style.overflow = ""; // Restaurar scroll en el body
      }
      
      if (e.target === confirmacionModal) {
        confirmacionModal.classList.remove("active");
        document.body.style.overflow = ""; // Restaurar scroll en el body
      }
    });
    
    // Confirmar eliminación
    if (confirmarContinuarBtn) {
      confirmarContinuarBtn.addEventListener("click", () => {
        const filaIndex = confirmarContinuarBtn.dataset.fila;
        if (filaIndex !== undefined) {
          const filas = document.querySelectorAll(".anuncios-tabla tbody tr");
          const fila = filas[filaIndex];
          
          if (fila) {
            // En una implementación real, aquí se enviaría una petición al servidor para eliminar el anuncio
            console.log(`Eliminando anuncio...`);
            
            // Eliminar la fila de la tabla
            fila.remove();
            
            // Cerrar el modal
            confirmacionModal.classList.remove("active");
            document.body.style.overflow = ""; // Restaurar scroll en el body
            
            // Mostrar mensaje de éxito
            alert("El anuncio ha sido eliminado correctamente.");
          }
        }
      });
    }
    
    // Botón de editar en el modal
    const editarBtnModal = document.querySelector(".editar-btn-modal");
    if (editarBtnModal) {
      editarBtnModal.addEventListener("click", () => {
        const titulo = document.getElementById("modal-titulo").textContent;
        console.log(`Editar anuncio desde modal: ${titulo}`);

        // Generar un ID único basado en el título para simular la edición
        const anuncioId = titulo.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');

        // Redireccionar a la página de edición con parámetros
        window.location.href = `crear_anuncio.html?edit=true&id=${anuncioId}&titulo=${encodeURIComponent(titulo)}`;
      });
    }
  });