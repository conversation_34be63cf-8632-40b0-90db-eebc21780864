<?php
require_once 'config.php';
require_once 'Controlador/TareaController.php';

use Controlador\TareaController;

$tareaController = new TareaController();

echo "<h2>Script de prueba para insertar datos de tareas y entregas</h2>";

try {
    // 1. Insertar una semana académica
    echo "<h3>1. Insertando semana académica...</h3>";
    $semanaData = [
        'curso_id' => 1, // Asumiendo que existe un curso con ID 1
        'titulo' => 'Semana 1: Introducción a las fracciones',
        'fecha_inicio' => '2025-03-15',
        'fecha_fin' => '2025-03-21',
        'descripcion' => 'Primera semana del curso de matemáticas'
    ];
    
    $semanaId = $tareaController->crearSemanaAcademica($semanaData);
    echo "Semana académica creada con ID: $semanaId<br>";

    // 2. Insertar una tarea
    echo "<h3>2. Insertando tarea...</h3>";
    $tareaData = [
        'curso_id' => 1,
        'titulo' => 'Ejercicios de Fracciones',
        'descripcion' => 'Realiza las sumas y restas de la pág. 19 - 20 del libro de texto. Asegúrate de simplificar las fracciones cuando sea posible.',
        'tipo' => 'tarea',
        'fecha_limite' => '2025-03-18',
        'hora_limite' => '23:59',
        'puntos' => 10,
        'semana_id' => $semanaId
    ];
    
    $tareaId = $tareaController->crearTarea($tareaData);
    echo "Tarea creada con ID: $tareaId<br>";

    // 3. Insertar un examen
    echo "<h3>3. Insertando examen...</h3>";
    $examenData = [
        'curso_id' => 1,
        'titulo' => 'Examen: Evaluación de fracciones',
        'descripcion' => 'Examen sobre conceptos básicos y operaciones con fracciones. Incluye sumas, restas, multiplicación y división.',
        'tipo' => 'examen',
        'fecha_limite' => '2025-03-20',
        'hora_limite' => '10:00',
        'puntos' => 20,
        'semana_id' => $semanaId
    ];
    
    $examenId = $tareaController->crearTarea($examenData);
    echo "Examen creado con ID: $examenId<br>";

    // 4. Insertar entregas de estudiantes (asumiendo que existen estudiantes con IDs 1, 2, 3)
    echo "<h3>4. Insertando entregas de estudiantes...</h3>";
    
    // Entrega 1 - Estudiante 1 para la tarea
    $entrega1 = [
        'tarea_id' => $tareaId,
        'estudiante_id' => 1,
        'fecha_entrega' => '2025-03-17 10:45:00',
        'comentarios' => 'Profesor, adjunto mi tarea de ejercicios de fracciones. Tuve algunas dudas en los ejercicios 5 y 7, pero intenté resolverlos lo mejor posible.',
        'archivo_url' => '/archivos/entregas/ejercicios_fracciones_estudiante1.pdf'
    ];
    
    $entrega1Id = $tareaController->crearEntrega($entrega1);
    echo "Entrega 1 creada con ID: $entrega1Id<br>";

    // Entrega 2 - Estudiante 2 para la tarea (tardía)
    $entrega2 = [
        'tarea_id' => $tareaId,
        'estudiante_id' => 2,
        'fecha_entrega' => '2025-03-18 23:30:00',
        'comentarios' => 'Disculpe la tardanza profesor, tuve problemas con mi conexión a internet.',
        'archivo_url' => '/archivos/entregas/ejercicios_fracciones_estudiante2.pdf'
    ];
    
    $entrega2Id = $tareaController->crearEntrega($entrega2);
    echo "Entrega 2 creada con ID: $entrega2Id<br>";

    // Entrega 3 - Estudiante 3 para la tarea
    $entrega3 = [
        'tarea_id' => $tareaId,
        'estudiante_id' => 3,
        'fecha_entrega' => '2025-03-16 14:20:00',
        'comentarios' => 'Aquí está mi tarea profesor.',
        'archivo_url' => '/archivos/entregas/ejercicios_fracciones_estudiante3.pdf'
    ];
    
    $entrega3Id = $tareaController->crearEntrega($entrega3);
    echo "Entrega 3 creada con ID: $entrega3Id<br>";

    // Entrega 4 - Estudiante 1 para el examen
    $entrega4 = [
        'tarea_id' => $examenId,
        'estudiante_id' => 1,
        'fecha_entrega' => '2025-03-20 09:30:00',
        'comentarios' => 'Profesor, aquí está mi examen. Espero haber respondido bien todas las preguntas.',
        'archivo_url' => '/archivos/entregas/examen_fracciones_estudiante1.pdf'
    ];
    
    $entrega4Id = $tareaController->crearEntrega($entrega4);
    echo "Entrega 4 (examen) creada con ID: $entrega4Id<br>";

    // 5. Calificar algunas entregas
    echo "<h3>5. Calificando entregas...</h3>";
    
    // Calificar entrega 1
    $calificacion1 = $tareaController->calificarEntrega($entrega1Id, 8, 'Buen trabajo. Los ejercicios están bien resueltos en general. En el ejercicio 5 te faltó simplificar la fracción final, y en el 7 hay un pequeño error en el proceso. Revisa las correcciones.');
    echo "Entrega 1 calificada con 8/10<br>";
    
    // Calificar entrega 3
    $calificacion3 = $tareaController->calificarEntrega($entrega3Id, 9, 'Excelente trabajo. Todos los ejercicios están correctamente resueltos y bien presentados.');
    echo "Entrega 3 calificada con 9/10<br>";
    
    // Calificar entrega 4 (examen)
    $calificacion4 = $tareaController->calificarEntrega($entrega4Id, 18, 'Excelente trabajo. Dominas muy bien los conceptos de fracciones. Solo un pequeño error en la pregunta 7.');
    echo "Entrega 4 (examen) calificada con 18/20<br>";

    echo "<h3>✅ Datos de prueba insertados exitosamente</h3>";
    echo "<p>Se han creado:</p>";
    echo "<ul>";
    echo "<li>1 semana académica</li>";
    echo "<li>1 tarea</li>";
    echo "<li>1 examen</li>";
    echo "<li>4 entregas de estudiantes</li>";
    echo "<li>3 calificaciones</li>";
    echo "</ul>";
    
    echo "<h3>IDs creados:</h3>";
    echo "<ul>";
    echo "<li>Semana académica: $semanaId</li>";
    echo "<li>Tarea: $tareaId</li>";
    echo "<li>Examen: $examenId</li>";
    echo "<li>Entregas: $entrega1Id, $entrega2Id, $entrega3Id, $entrega4Id</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h3>❌ Error: " . $e->getMessage() . "</h3>";
    echo "<p>Detalles: " . $e->getTraceAsString() . "</p>";
}

echo "<hr>";
echo "<h3>Pruebas de API:</h3>";
echo "<p>Ahora puedes probar las siguientes URLs:</p>";
echo "<ul>";
echo "<li><a href='api_tareas.php?action=get_tareas_curso&curso_id=1' target='_blank'>Obtener tareas del curso 1</a></li>";
echo "<li><a href='api_tareas.php?action=get_entregas&tarea_id=$tareaId' target='_blank'>Obtener entregas de la tarea $tareaId</a></li>";
echo "<li><a href='api_tareas.php?action=get_estadisticas&curso_id=1' target='_blank'>Obtener estadísticas del curso 1</a></li>";
echo "</ul>";
?> 