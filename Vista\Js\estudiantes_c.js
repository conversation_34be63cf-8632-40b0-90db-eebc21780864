document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const studentSearch = document.getElementById("student-search")
  const classmateCards = document.querySelectorAll(".classmate-card")
  const messageButtons = document.querySelectorAll(".message-btn")
  const chatModal = document.getElementById("chat-modal")
  const closeChatBtn = document.getElementById("close-chat-btn")
  const sendBtn = document.querySelector(".send-btn")
  const chatInput = document.querySelector(".chat-input input")
  
  // Filtrar compañeros
  if (studentSearch) {
    studentSearch.addEventListener("input", filterClassmates)
  }
  
  function filterClassmates() {
    const searchTerm = studentSearch.value.toLowerCase()
    
    classmateCards.forEach((card) => {
      const studentName = card.querySelector("h3").textContent.toLowerCase()
      const studentSection = card.querySelector("p").textContent.toLowerCase()
      
      // Filtrar por texto
      const matchesSearch = studentName.includes(searchTerm) || studentSection.includes(searchTerm)
      
      // Mostrar u ocultar según los filtros
      if (matchesSearch) {
        card.style.display = ""
      } else {
        card.style.display = "none"
      }
    })
  }
  
  // Abrir modal de chat
  if (messageButtons.length > 0) {
    messageButtons.forEach((button) => {
      button.addEventListener("click", () => {
        if (chatModal) {
          chatModal.classList.add("active")
        }
      })
    })
  }
  
  // Cerrar modal de chat
  if (closeChatBtn) {
    closeChatBtn.addEventListener("click", () => {
      chatModal.classList.remove("active")
    })
  }
  
  // Cerrar modal al hacer clic fuera del contenido
  if (chatModal) {
    chatModal.addEventListener("click", (e) => {
      if (e.target === chatModal) {
        chatModal.classList.remove("active")
      }
    })
  }
  
  // Enviar mensaje
  if (sendBtn && chatInput) {
    sendBtn.addEventListener("click", sendMessage)
    chatInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        sendMessage()
      }
    })
  }
  
  function sendMessage() {
    const messageText = chatInput.value.trim()
    if (messageText) {
      // Crear nuevo mensaje
      const messagesContainer = document.querySelector(".chat-messages")
      const newMessage = document.createElement("div")
      newMessage.className = "message sent"
      
      // Obtener hora actual
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, "0")
      const minutes = now.getMinutes().toString().padStart(2, "0")
      const timeString = `${hours}:${minutes}`
      
      // Contenido del mensaje
      newMessage.innerHTML = `
        <div class="message-content">
          <div class="message-bubble">
            ${messageText}
          </div>
          <div class="message-time">${timeString}</div>
        </div>
      `
      
      // Añadir mensaje al chat
      messagesContainer.appendChild(newMessage)
      
      // Limpiar input y hacer scroll al final
      chatInput.value = ""
      messagesContainer.scrollTop = messagesContainer.scrollHeight
    }
  }
  
  // Paginación
  const paginationButtons = document.querySelectorAll(".pagination-btn")
  if (paginationButtons.length > 0) {
    paginationButtons.forEach((button) => {
      if (!button.disabled && !button.classList.contains("active")) {
        button.addEventListener("click", () => {
          // Aquí iría la lógica de paginación real
          // Por ahora solo cambiamos la clase active
          document.querySelector(".pagination-btn.active").classList.remove("active")
          button.classList.add("active")
        })
      }
    })
  }
  
  // Botones de unirse a grupo
  const joinGroupButtons = document.querySelectorAll(".join-group-btn")
  if (joinGroupButtons.length > 0) {
    joinGroupButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const groupName = e.target.closest(".study-group-card").querySelector("h3").textContent
        alert(`Te has unido al grupo: ${groupName}`)
        button.textContent = "Unido"
        button.disabled = true
        button.style.backgroundColor = "#4caf50"
      })
    })
  }
})