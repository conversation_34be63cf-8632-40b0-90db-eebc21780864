document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const studentSelect = document.getElementById("student-select")
    const payButtons = document.querySelectorAll(".pay-btn")
    const paymentModal = document.getElementById("payment-modal")
    const closeModalButtons = document.querySelectorAll(".modal-close-btn")
    const confirmPaymentBtn = document.getElementById("confirm-payment-btn")
    const printBtn = document.querySelector(".payment-action-btn:first-child")
    const downloadBtn = document.querySelector(".payment-action-btn:last-child")
    const onlinePaymentBtn = document.querySelector(".online-payment-btn")
    
    // Cambiar estudiante seleccionado
    if (studentSelect) {
      studentSelect.addEventListener("change", () => {
        // Aquí iría la lógica para cargar los datos del estudiante seleccionado
        const selectedStudent = studentSelect.options[studentSelect.selectedIndex].text
        console.log(`Estudiante seleccionado: ${selectedStudent}`)
      })
    }
    
    // Mostrar modal de pago
    if (payButtons.length > 0) {
      payButtons.forEach((button) => {
        button.addEventListener("click", () => {
          if (paymentModal) {
            paymentModal.classList.add("active")
          }
        })
      })
    }
    
    // Botón de pago en línea
    if (onlinePaymentBtn) {
      onlinePaymentBtn.addEventListener("click", () => {
        if (paymentModal) {
          paymentModal.classList.add("active")
        }
      })
    }
    
    // Cerrar modal
    if (closeModalButtons.length > 0) {
      closeModalButtons.forEach((button) => {
        button.addEventListener("click", () => {
          const modal = button.closest(".modal-overlay")
          if (modal) {
            modal.classList.remove("active")
          }
        })
      })
    }
    
    // Cerrar modal al hacer clic fuera del contenido
    if (paymentModal) {
      paymentModal.addEventListener("click", (e) => {
        if (e.target === paymentModal) {
          paymentModal.classList.remove("active")
        }
      })
    }
    
    // Confirmar pago
    if (confirmPaymentBtn) {
      confirmPaymentBtn.addEventListener("click", () => {
        // Aquí iría la lógica para procesar el pago
        // Por ahora, solo mostramos un mensaje de éxito
        alert("Pago realizado con éxito. Se ha enviado un comprobante a su correo electrónico.")
        
        // Cerrar modal
        if (paymentModal) {
          paymentModal.classList.remove("active")
        }
      })
    }
    
    // Imprimir cronograma
    if (printBtn) {
      printBtn.addEventListener("click", () => {
        window.print()
      })
    }
    
    // Descargar cronograma
    if (downloadBtn) {
      downloadBtn.addEventListener("click", () => {
        // Aquí iría la lógica para descargar el cronograma
        // Por ahora, solo mostramos un mensaje
        alert("Descargando cronograma de pagos...")
      })
    }
    
    // Mostrar comprobante
    const receiptButtons = document.querySelectorAll(".table-action-btn:not(.pay-btn)")
    if (receiptButtons.length > 0) {
      receiptButtons.forEach((button) => {
        button.addEventListener("click", () => {
          // Aquí iría la lógica para mostrar el comprobante
          // Por ahora, solo mostramos un mensaje
          alert("Mostrando comprobante de pago...")
        })
      })
    }
  })