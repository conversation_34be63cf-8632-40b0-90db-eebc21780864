-- INSERT DE EJEMPLO: TAREA CON ENTREGAS DE ESTUDIANTES

-- Insertar una nueva tarea en el curso 6, semana 11
INSERT INTO `contenido` (`id`, `sesion_id`, `tipo`, `titulo`, `descripcion`, `imagen`, `archivo`, `url`, `fecha_limite`, `hora_limite`, `puntos`, `activo`, `created_at`, `updated_at`) VALUES
(12, 5, 'tarea', 'Ejercicios de Matemáticas - Fracciones',
'<p>Resolver los ejercicios del capítulo 5 sobre fracciones. Incluir:</p>
<ul>
<li>Suma y resta de fracciones</li>
<li>Multiplicación y división</li>
<li>Simplificación de fracciones</li>
<li>Problemas de aplicación</li>
</ul>
<p><strong>Fecha de entrega:</strong> 2025-07-05</p>
<p><strong>Puntos:</strong> 20</p>',
NULL, NULL, NULL, '2025-07-05', '23:59:00', 20, 1, NOW(), NOW());

-- ENTREGAS DE LOS ESTUDIANTES

-- Entrega de Diego Martínez López (estudiante_id = 2, usuario_id = 9)
-- Estado: ENTREGADO (único que envió)
INSERT INTO `entregas` (`id`, `contenido_id`, `estudiante_id`, `archivo`, `comentario_estudiante`, `fecha_entrega`, `estado`, `calificacion`, `comentario_maestro`, `fecha_calificacion`, `created_at`, `updated_at`) VALUES
(1, 12, 2, 'ejercicios_fracciones_diego_martinez.pdf',
'Profesor, adjunto mis ejercicios resueltos. Tuve algunas dudas en los problemas de aplicación pero creo que están bien resueltos. Espero su retroalimentación.',
'2025-07-03 14:30:00', 'entregado', 18.5,
'Excelente trabajo Diego. Los ejercicios están bien resueltos, solo algunos errores menores en la simplificación. Sigue así.',
'2025-07-06 10:15:00', '2025-07-03 14:30:00', '2025-07-06 10:15:00');

-- Entrega de Sofía Sánchez Díaz (estudiante_id = 1, usuario_id = 8)
-- Estado: PENDIENTE (no ha enviado)
INSERT INTO `entregas` (`id`, `contenido_id`, `estudiante_id`, `archivo`, `comentario_estudiante`, `fecha_entrega`, `estado`, `calificacion`, `comentario_maestro`, `fecha_calificacion`, `created_at`, `updated_at`) VALUES
(2, 12, 1, NULL, NULL,
'2025-06-29 08:00:00', 'pendiente', NULL, NULL, NULL, '2025-06-29 08:00:00', '2025-06-29 08:00:00');

-- VERIFICACIÓN DE DATOS

-- Consulta para verificar la tarea creada
-- SELECT * FROM contenido WHERE id = 12;

-- Consulta para verificar las entregas
-- SELECT
--     e.id,
--     e.contenido_id,
--     e.estudiante_id,
--     CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as estudiante_nombre,
--     e.estado,
--     e.calificacion,
--     e.fecha_entrega,
--     e.archivo
-- FROM entregas e
-- INNER JOIN estudiantes est ON e.estudiante_id = est.id
-- INNER JOIN personas p ON est.persona_id = p.id
-- WHERE e.contenido_id = 12
-- ORDER BY e.estudiante_id;

-- NOTAS IMPORTANTES:
-- 1. La tarea se crea en el curso 6 (que tiene estudiantes inscritos)
-- 2. Se usa la sesión 5 que corresponde a la semana 11
-- 3. Diego Martínez (estudiante_id = 2) SÍ entregó la tarea
-- 4. Sofía Sánchez (estudiante_id = 1) NO entregó (estado pendiente)
-- 5. La fecha límite es 2025-07-05 a las 23:59
-- 6. La tarea vale 20 puntos
-- 7. Diego recibió calificación de 18.5/20
