<?php
require_once __DIR__ . '/Conexion.php';

/**
 * Modelo para manejar usuarios del sistema educativo
 */
class Usuario {
    public $pdo;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
    }

    /**
     * Autentica un usuario con nombre de usuario y contraseña
     * @param string $nombreUsuario
     * @param string $password
     * @return array|false Datos del usuario si es válido, false si no
     */
    public function autenticar($nombreUsuario, $password) {
        try {
            $sql = "SELECT u.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.nombre_foto 
                    FROM usuarios u 
                    LEFT JOIN personas p ON u.id = p.usuario_id 
                    WHERE u.nombre_usuario = :nombre_usuario AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':nombre_usuario', $nombreUsuario);
            $stmt->execute();
            
            $usuario = $stmt->fetch();
            
            if ($usuario && password_verify($password, $usuario['password'])) {
                // No devolver la contraseña por seguridad
                unset($usuario['password']);
                return $usuario;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Error en autenticación: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene información específica del usuario según su rol
     * @param int $usuarioId
     * @param string $rol
     * @return array|false
     */
    public function obtenerInformacionPorRol($usuarioId, $rol) {
        try {
            switch ($rol) {
                case 'estudiante':
                    return $this->obtenerDatosEstudiante($usuarioId);
                case 'maestro':
                    return $this->obtenerDatosMaestro($usuarioId);
                case 'padre':
                    return $this->obtenerDatosPadre($usuarioId);
                case 'administrador':
                    return $this->obtenerDatosAdministrador($usuarioId);
                default:
                    return false;
            }
        } catch (PDOException $e) {
            error_log("Error obteniendo información por rol: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene datos específicos de un estudiante
     */
    private function obtenerDatosEstudiante($usuarioId) {
        $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM estudiantes e
                INNER JOIN personas p ON e.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * Obtiene datos específicos de un maestro
     */
    private function obtenerDatosMaestro($usuarioId) {
        $sql = "SELECT m.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM maestros m
                INNER JOIN personas p ON m.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * Obtiene datos específicos de un padre
     */
    private function obtenerDatosPadre($usuarioId) {
        $sql = "SELECT pa.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM padres pa
                INNER JOIN personas p ON pa.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * Obtiene datos de administrador
     */
    private function obtenerDatosAdministrador($usuarioId) {
        $sql = "SELECT a.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, p.sexo, p.nombre_foto
                FROM administradores a
                INNER JOIN personas p ON a.persona_id = p.id
                WHERE p.usuario_id = :usuario_id";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindParam(':usuario_id', $usuarioId);
        $stmt->execute();

        return $stmt->fetch();
    }

    /**
     * Crea un nuevo usuario en el sistema
     * @param array $datosUsuario
     * @param array $datosPersona
     * @return int|false ID del usuario creado o false si hay error
     */
    public function crearUsuario($datosUsuario, $datosPersona) {
        try {
            $this->pdo->beginTransaction();

            // Insertar usuario
            $sqlUsuario = "INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES (:nombre_usuario, :email, :password, :rol)";
            $stmtUsuario = $this->pdo->prepare($sqlUsuario);
            $stmtUsuario->bindParam(':nombre_usuario', $datosUsuario['nombre_usuario']);
            $stmtUsuario->bindParam(':email', $datosUsuario['email']);
            $stmtUsuario->bindParam(':password', password_hash($datosUsuario['password'], PASSWORD_DEFAULT));
            $stmtUsuario->bindParam(':rol', $datosUsuario['rol']);
            $stmtUsuario->execute();

            $usuarioId = $this->pdo->lastInsertId();

            // Insertar persona
            $sqlPersona = "INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) 
                          VALUES (:usuario_id, :nombres, :apellido_paterno, :apellido_materno, :dni, :fecha_nacimiento, :sexo, :direccion, :telefono)";
            $stmtPersona = $this->pdo->prepare($sqlPersona);
            $stmtPersona->bindParam(':usuario_id', $usuarioId);
            $stmtPersona->bindParam(':nombres', $datosPersona['nombres']);
            $stmtPersona->bindParam(':apellido_paterno', $datosPersona['apellido_paterno']);
            $stmtPersona->bindParam(':apellido_materno', $datosPersona['apellido_materno']);
            $stmtPersona->bindParam(':dni', $datosPersona['dni']);
            $stmtPersona->bindParam(':fecha_nacimiento', $datosPersona['fecha_nacimiento']);
            $stmtPersona->bindParam(':sexo', $datosPersona['sexo']);
            $stmtPersona->bindParam(':direccion', $datosPersona['direccion']);
            $stmtPersona->bindParam(':telefono', $datosPersona['telefono']);
            $stmtPersona->execute();

            $personaId = $this->pdo->lastInsertId();

            $this->pdo->commit();
            return ['usuario_id' => $usuarioId, 'persona_id' => $personaId];

        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log("Error creando usuario: " . $e->getMessage());
            return false;
        }
    }
}
?>
