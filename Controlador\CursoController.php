<?php
require_once __DIR__ . '/../Modelo/Conexion.php';
require_once __DIR__ . '/../Modelo/Curso.php';

class CursoController {
    private $conexion;
    private $curso;

    public function __construct() {
        $this->conexion = Conexion::getConexion();
        $this->curso = new Curso($this->conexion);
    }

    // Obtener todos los cursos de un maestro
    public function obtenerCursosPorMaestro($maestroId) {
        try {
            // Obtener el ID del maestro de manera más simple
            $queryMaestro = "SELECT m.id as maestro_id FROM maestros m 
                           JOIN personas p ON m.persona_id = p.id 
                           JOIN usuarios u ON p.usuario_id = u.id 
                           WHERE u.id = ? AND u.rol = 'maestro'";
            $stmtMaestro = $this->conexion->prepare($queryMaestro);
            $stmtMaestro->execute([$maestroId]);
            $maestro = $stmtMaestro->fetch();
            
            if (!$maestro) {
                return [];
            }
            
            $maestroRealId = $maestro['maestro_id'];
            
            $query = "SELECT c.*, 
                             COUNT(i.estudiante_id) as total_estudiantes,
                             GROUP_CONCAT(DISTINCT h.dia_semana, ':', h.hora_inicio, '-', h.hora_fin SEPARATOR '|') as horarios
                      FROM cursos c 
                      LEFT JOIN inscripciones i ON c.id = i.curso_id AND i.activo = 1
                      LEFT JOIN horarios h ON c.id = h.curso_id
                      WHERE c.maestro_id = ? AND c.activo = 1
                      GROUP BY c.id
                      ORDER BY c.nombre";
            
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([$maestroRealId]);
            
            $cursos = [];
            while ($row = $stmt->fetch()) {
                // Procesar horarios
                $horarios = [];
                if ($row['horarios']) {
                    $horariosArray = explode('|', $row['horarios']);
                    foreach ($horariosArray as $horario) {
                        $partes = explode(':', $horario);
                        if (count($partes) >= 2) {
                            $dia = $partes[0];
                            $tiempo = $partes[1];
                            $horas = explode('-', $tiempo);
                            if (count($horas) == 2) {
                                $horarios[$dia] = trim($horas[0]) . ' - ' . trim($horas[1]);
                            } else {
                                $horarios[$dia] = $tiempo;
                            }
                        }
                    }
                }
                $row['horarios_procesados'] = $horarios;
                $cursos[] = $row;
            }
            
            return $cursos;
        } catch (Exception $e) {
            error_log("Error al obtener cursos del maestro: " . $e->getMessage());
            return [];
        }
    }

    // Obtener un curso específico por ID
    public function obtenerCursoPorId($cursoId, $maestroId = null) {
        try {
            $query = "SELECT c.*, 
                             GROUP_CONCAT(DISTINCT h.dia_semana, ':', h.hora_inicio, '-', h.hora_fin SEPARATOR '|') as horarios
                      FROM cursos c 
                      LEFT JOIN horarios h ON c.id = h.curso_id
                      WHERE c.id = ?";
            
            $params = [$cursoId];
            
            if ($maestroId) {
                // Obtener el ID real del maestro
                $queryMaestro = "SELECT m.id as maestro_id FROM maestros m 
                               JOIN personas p ON m.persona_id = p.id 
                               JOIN usuarios u ON p.usuario_id = u.id 
                               WHERE u.id = ? AND u.rol = 'maestro'";
                $stmtMaestro = $this->conexion->prepare($queryMaestro);
                $stmtMaestro->execute([$maestroId]);
                $maestro = $stmtMaestro->fetch();
                
                if ($maestro) {
                    $query .= " AND c.maestro_id = ?";
                    $params[] = $maestro['maestro_id'];
                }
            }
            
            $query .= " GROUP BY c.id";
            
            $stmt = $this->conexion->prepare($query);
            $stmt->execute($params);
            $row = $stmt->fetch();
            
            if ($row) {
                // Procesar horarios
                $horarios = [];
                if ($row['horarios']) {
                    $horariosArray = explode('|', $row['horarios']);
                    foreach ($horariosArray as $horario) {
                        $partes = explode(':', $horario);
                        if (count($partes) >= 2) {
                            $dia = $partes[0];
                            $tiempo = $partes[1];
                            $horas = explode('-', $tiempo);
                            if (count($horas) == 2) {
                                $horarios[$dia] = trim($horas[0]) . ' - ' . trim($horas[1]);
                            } else {
                                $horarios[$dia] = $tiempo;
                            }
                        }
                    }
                }
                $row['horarios_procesados'] = $horarios;
                return $row;
            }
            
            return null;
        } catch (Exception $e) {
            error_log("Error al obtener curso por ID: " . $e->getMessage());
            return null;
        }
    }

    // Crear un nuevo curso
    public function crearCurso($datos) {
        try {
            $this->conexion->beginTransaction();
            
            // Obtener el ID real del maestro
            $queryMaestro = "SELECT m.id as maestro_id FROM maestros m 
                           JOIN personas p ON m.persona_id = p.id 
                           JOIN usuarios u ON p.usuario_id = u.id 
                           WHERE u.id = ? AND u.rol = 'maestro'";
            $stmtMaestro = $this->conexion->prepare($queryMaestro);
            $stmtMaestro->execute([$datos['maestro_id']]);
            $maestro = $stmtMaestro->fetch();
            
            if (!$maestro) {
                throw new Exception("Maestro no encontrado");
            }
            
            // Procesar imagen si existe
            $imagen = null;
            if (!empty($datos['imagen']) && $datos['imagen'] !== 'null') {
                // Si la imagen viene en base64, guardarla como URL
                if (strpos($datos['imagen'], 'data:image') === 0) {
                    $imagen = $datos['imagen'];
                } else {
                    $imagen = $datos['imagen'];
                }
            }
            
            // Insertar curso (sin especialidad ni descripción)
            $query = "INSERT INTO cursos (nombre, maestro_id, grado, anio_escolar, icono, imagen) 
                      VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([
                $datos['nombre'],
                $maestro['maestro_id'],
                $datos['grado'],
                $datos['anio_escolar'],
                $datos['icono'],
                $imagen
            ]);
            
            $cursoId = $this->conexion->lastInsertId();
            
            // Insertar horarios
            if (isset($datos['horarios']) && is_array($datos['horarios'])) {
                foreach ($datos['horarios'] as $dia => $horario) {
                    if ($horario['activo']) {
                        $queryHorario = "INSERT INTO horarios (curso_id, dia_semana, hora_inicio, hora_fin) 
                                        VALUES (?, ?, ?, ?)";
                        $stmtHorario = $this->conexion->prepare($queryHorario);
                        $stmtHorario->execute([
                            $cursoId,
                            $dia,
                            $horario['inicio'],
                            $horario['fin']
                        ]);
                    }
                }
            }
            
            $this->conexion->commit();
            return $cursoId;
            
        } catch (Exception $e) {
            $this->conexion->rollback();
            error_log("Error al crear curso: " . $e->getMessage());
            return false;
        }
    }

    // Actualizar un curso existente
    public function actualizarCurso($cursoId, $datos) {
        try {
            $this->conexion->beginTransaction();
            
            // Obtener el ID real del maestro
            $queryMaestro = "SELECT m.id as maestro_id FROM maestros m 
                           JOIN personas p ON m.persona_id = p.id 
                           JOIN usuarios u ON p.usuario_id = u.id 
                           WHERE u.id = ? AND u.rol = 'maestro'";
            $stmtMaestro = $this->conexion->prepare($queryMaestro);
            $stmtMaestro->execute([$datos['maestro_id']]);
            $maestro = $stmtMaestro->fetch();
            
            if (!$maestro) {
                throw new Exception("Maestro no encontrado");
            }
            
            // Procesar imagen si existe
            $imagen = null;
            if (!empty($datos['imagen']) && $datos['imagen'] !== 'null') {
                // Si la imagen viene en base64, guardarla como URL
                if (strpos($datos['imagen'], 'data:image') === 0) {
                    $imagen = $datos['imagen'];
                } else {
                    $imagen = $datos['imagen'];
                }
            }
            
            // Actualizar curso (sin especialidad ni descripción)
            $query = "UPDATE cursos SET 
                      nombre = ?, 
                      grado = ?, 
                      icono = ?, 
                      imagen = ?
                      WHERE id = ? AND maestro_id = ?";
            
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([
                $datos['nombre'],
                $datos['grado'],
                $datos['icono'],
                $imagen,
                $cursoId,
                $maestro['maestro_id']
            ]);
            
            // Eliminar horarios existentes
            $queryDeleteHorarios = "DELETE FROM horarios WHERE curso_id = ?";
            $stmtDelete = $this->conexion->prepare($queryDeleteHorarios);
            $stmtDelete->execute([$cursoId]);
            
            // Insertar nuevos horarios
            if (isset($datos['horarios']) && is_array($datos['horarios'])) {
                foreach ($datos['horarios'] as $dia => $horario) {
                    if ($horario['activo']) {
                        $queryHorario = "INSERT INTO horarios (curso_id, dia_semana, hora_inicio, hora_fin) 
                                        VALUES (?, ?, ?, ?)";
                        $stmtHorario = $this->conexion->prepare($queryHorario);
                        $stmtHorario->execute([
                            $cursoId,
                            $dia,
                            $horario['inicio'],
                            $horario['fin']
                        ]);
                    }
                }
            }
            
            $this->conexion->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conexion->rollback();
            error_log("Error al actualizar curso: " . $e->getMessage());
            return false;
        }
    }

    // Eliminar un curso (borrado físico)
    public function eliminarCurso($cursoId, $maestroId) {
        try {
            $this->conexion->beginTransaction();
            // Obtener el ID real del maestro
            $queryMaestro = "SELECT m.id as maestro_id FROM maestros m 
                           JOIN personas p ON m.persona_id = p.id 
                           JOIN usuarios u ON p.usuario_id = u.id 
                           WHERE u.id = ? AND u.rol = 'maestro'";
            $stmtMaestro = $this->conexion->prepare($queryMaestro);
            $stmtMaestro->execute([$maestroId]);
            $maestro = $stmtMaestro->fetch();
            
            if (!$maestro) {
                $this->conexion->rollBack();
                return false;
            }
            // Eliminar horarios asociados
            $queryDeleteHorarios = "DELETE FROM horarios WHERE curso_id = ?";
            $stmtDeleteHorarios = $this->conexion->prepare($queryDeleteHorarios);
            $stmtDeleteHorarios->execute([$cursoId]);
            // Eliminar inscripciones asociadas (opcional, si existe la tabla)
            if ($this->conexion->query("SHOW TABLES LIKE 'inscripciones'")->rowCount() > 0) {
                $queryDeleteInscripciones = "DELETE FROM inscripciones WHERE curso_id = ?";
                $stmtDeleteInscripciones = $this->conexion->prepare($queryDeleteInscripciones);
                $stmtDeleteInscripciones->execute([$cursoId]);
            }
            // Eliminar el curso
            $query = "DELETE FROM cursos WHERE id = ? AND maestro_id = ?";
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([$cursoId, $maestro['maestro_id']]);
            $this->conexion->commit();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            $this->conexion->rollBack();
            error_log("Error al eliminar curso: " . $e->getMessage());
            return false;
        }
    }

    // Obtener grados disponibles
    public function obtenerGrados() {
        return [
            'inicial-3' => '3 años',
            'inicial-4' => '4 años',
            'inicial-5' => '5 años',
            'primaria-1' => '1° Primaria',
            'primaria-2' => '2° Primaria',
            'primaria-3' => '3° Primaria',
            'primaria-4' => '4° Primaria',
            'primaria-5' => '5° Primaria',
            'primaria-6' => '6° Primaria'
        ];
    }

    // Obtener iconos disponibles
    public function obtenerIconos() {
        return [
            'calculate' => 'Matemáticas',
            'science' => 'Ciencias',
            'menu_book' => 'Lenguaje',
            'language' => 'Inglés',
            'history_edu' => 'Historia',
            'brush' => 'Arte',
            'sports_soccer' => 'Educación Física',
            'music_note' => 'Música'
        ];
    }

    // --- VIDEOCONFERENCIA ---
    public function obtenerVideoconferenciasPorCurso($cursoId) {
        // Solo devuelve la activa
        $videoconferencia = $this->curso->obtenerVideoconferenciaActiva($cursoId);
        return $videoconferencia ? [$videoconferencia] : [];
    }

    public function crearVideoconferencia($input) {
        return $this->curso->crearOActualizarVideoconferencia(
            $input['curso_id'],
            $input['titulo'],
            $input['fecha'],
            $input['hora'],
            $input['url']
        );
    }

    public function actualizarVideoconferencia($id, $input) {
        // Simplemente llama a crearOActualizarVideoconferencia
        return $this->curso->crearOActualizarVideoconferencia(
            $input['curso_id'],
            $input['titulo'],
            $input['fecha'],
            $input['hora'],
            $input['url']
        );
    }

    public function eliminarVideoconferencia($id) {
        return $this->curso->eliminarVideoconferencia($id);
    }
}
?> 