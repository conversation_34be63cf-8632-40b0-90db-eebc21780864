/* Estilos para la página de Intranet Escolar - Diseño <PERSON>o */
/*
/* Importación de fuentes */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

/* Estilos base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Contenedor principal de login */
.login-container {
  display: flex;
  width: 100%;
  max-width: 1000px;
  height: 600px;
  background: white;
  border-radius: 30px;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
}

/* Panel izquierdo con branding */
.login-left {
  flex: 1;
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 40px;
  color: white;
  overflow: hidden;
}

/* Formas decorativas de fondo */
.login-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

.login-left::after {
  content: '';
  position: absolute;
  top: 20%;
  right: -10%;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

/* Contenedor del branding */
.brand-container {
  text-align: center;
  z-index: 2;
  position: relative;
}

/* Logo circular */
.logo-circle {
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 14px;
  font-weight: 700;
  color: #ff6b35;
  letter-spacing: 1px;
}

/* Nombre de la marca */
.brand-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 1px;
  text-align: center;
  line-height: 1.2;
}

/* Tagline */
.brand-tagline {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 0;
  text-align: center;
}

/* Texto decorativo */
.decorative-text {
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.8;
  text-align: center;
  max-width: 280px;
  z-index: 2;
  position: relative;
}

/* Panel derecho con formulario */
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  background: white;
}

/* Contenedor del formulario */
.login-form-container {
  width: 100%;
  max-width: 350px;
}

/* Header de bienvenida */
.welcome-header {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.welcome-subtitle {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

/* Formulario */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Grupos de campos */
.form-group {
  position: relative;
}

/* Inputs */
input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 16px 20px;
  border: none;
  border-radius: 25px;
  background: #fff4e6;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  color: #333;
  outline: none;
  transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="password"]:focus {
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

input::placeholder {
  color: #999;
  font-weight: 400;
}

/* Opciones del formulario */
.form-options {
  text-align: center;
  margin: 10px 0;
}

.forgot-password {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #ff6b35;
}

/* Botón de login */
.login-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
  margin-top: 10px;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 25px rgba(255, 107, 53, 0.4);
}

.login-btn:active {
  transform: translateY(0);
}



/* Animaciones */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .login-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
    max-width: 400px;
    border-radius: 20px;
  }

  .login-left {
    padding: 25px 30px 15px 30px;
    min-height: 150px;
  }

  .logo-circle {
    width: 60px;
    height: 60px;
    margin-bottom: 12px;
  }

  .logo-text {
    font-size: 12px;
  }

  .brand-name {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .brand-tagline {
    font-size: 11px;
    margin-bottom: 0;
  }

  /* Ocultar la descripción en móviles para ahorrar espacio */
  .decorative-text {
    display: none;
  }

  .login-right {
    padding: 30px 30px 40px 30px;
  }

  .welcome-header h1 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .login-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }

  .login-left {
    padding: 20px 20px 10px 20px;
    min-height: 130px;
  }

  .logo-circle {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
  }

  .logo-text {
    font-size: 10px;
  }

  .brand-name {
    font-size: 14px;
    margin-bottom: 5px;
  }

  .brand-tagline {
    font-size: 10px;
    margin-bottom: 0;
  }

  /* Asegurar que la descripción esté oculta en móviles pequeños */
  .decorative-text {
    display: none;
  }

  .login-right {
    padding: 25px 20px 30px 20px;
  }

  .login-form-container {
    max-width: 100%;
  }

  .welcome-header {
    margin-bottom: 30px;
  }

  .welcome-header h1 {
    font-size: 24px;
  }

  input[type="text"],
  input[type="password"] {
    padding: 14px 18px;
  }

  .login-btn {
    padding: 14px;
  }
}

/* Efectos adicionales */
.login-left .logo-circle {
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}

/* Hover effects para inputs */
input[type="text"]:hover,
input[type="password"]:hover {
  background: #ffede0;
}

/* Focus states mejorados */
input[type="text"]:focus,
input[type="password"]:focus {
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15);
  border: 2px solid #ff6b35;
}
