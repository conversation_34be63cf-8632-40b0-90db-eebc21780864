<?php
session_start();
header('Content-Type: application/json');

// DEBUG: Agregar información de depuración
error_log("=== DEBUG API CONTENIDO ===");
error_log("Usuario ID: " . ($_SESSION['usuario_id'] ?? 'no definido'));
error_log("Rol: " . ($_SESSION['rol'] ?? 'no definido'));
error_log("Action: " . ($_GET['action'] ?? 'no definido'));

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    error_log("ERROR: Usuario no autorizado");
    http_response_code(401);
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

require_once 'Controlador/ContenidoController.php';

$controller = new ContenidoController();
$maestroId = $_SESSION['usuario_id'];

error_log("Maestro ID: " . $maestroId);

// Obtener el método HTTP
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'semanas':
                    $cursoId = (int)($_GET['curso_id'] ?? 0);
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    error_log("Verificando acceso para curso: " . $cursoId);
                    if (!$controller->verificarAccesoMaestro($cursoId, $maestroId)) {
                        error_log("ERROR: No tiene acceso al curso");
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $semanas = $controller->obtenerSemanasPorCurso($cursoId);
                    echo json_encode(['success' => true, 'data' => $semanas]);
                    break;
                    
                case 'contenido':
                    $cursoId = (int)($_GET['curso_id'] ?? 0);
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    if (!$controller->verificarAccesoMaestro($cursoId, $maestroId)) {
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $contenido = $controller->obtenerContenidoPorCurso($cursoId);
                    echo json_encode(['success' => true, 'data' => $contenido]);
                    break;
                    
                case 'videoconferencias':
                    $cursoId = (int)($_GET['curso_id'] ?? 0);
                    if (!$cursoId) {
                        throw new Exception('ID del curso requerido');
                    }
                    
                    if (!$controller->verificarAccesoMaestro($cursoId, $maestroId)) {
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $videoconferencias = $controller->obtenerVideoconferenciasPorCurso($cursoId);
                    echo json_encode(['success' => true, 'data' => $videoconferencias]);
                    break;
                    
                case 'obtener_contenido':
                    $contenidoId = (int)($_GET['id'] ?? 0);
                    if (!$contenidoId) {
                        throw new Exception('ID del contenido requerido');
                    }
                    
                    $contenido = $controller->obtenerContenidoPorId($contenidoId);
                    if (!$contenido) {
                        throw new Exception('Contenido no encontrado');
                    }
                    
                    // Verificar acceso al contenido
                    if (!$controller->verificarAccesoContenido($contenidoId, $maestroId)) {
                        throw new Exception('No tienes acceso a este contenido');
                    }
                    
                    echo json_encode(['success' => true, 'data' => $contenido]);
                    break;
                    
                case 'obtener_semana':
                    $semanaId = (int)($_GET['id'] ?? 0);
                    if (!$semanaId) {
                        throw new Exception('ID de la semana requerido');
                    }
                    
                    $semana = $controller->obtenerSemanaPorId($semanaId);
                    if (!$semana) {
                        throw new Exception('Semana no encontrada');
                    }
                    
                    // Verificar acceso a la semana
                    if (!$controller->verificarAccesoSemana($semanaId, $maestroId)) {
                        throw new Exception('No tienes acceso a esta semana');
                    }
                    
                    echo json_encode(['success' => true, 'data' => $semana]);
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            error_log("POST Input: " . json_encode($input));
            
            switch ($action) {
                case 'crear_semana':
                    if (!$controller->verificarAccesoMaestro($input['curso_id'], $maestroId)) {
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $semanaId = $controller->crearSemana($input);
                    if ($semanaId) {
                        echo json_encode(['success' => true, 'id' => $semanaId]);
                    } else {
                        throw new Exception('Error al crear la semana');
                    }
                    break;
                    
                case 'crear_contenido':
                    if (!$controller->verificarAccesoMaestro($input['curso_id'], $maestroId)) {
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $contenidoId = $controller->crearContenido($input);
                    if ($contenidoId) {
                        echo json_encode(['success' => true, 'id' => $contenidoId]);
                    } else {
                        throw new Exception('Error al crear el contenido');
                    }
                    break;
                    
                case 'crear_videoconferencia':
                    error_log("Verificando acceso para crear videoconferencia en curso: " . $input['curso_id']);
                    if (!$controller->verificarAccesoMaestro($input['curso_id'], $maestroId)) {
                        error_log("ERROR: No tiene acceso al curso para crear videoconferencia");
                        throw new Exception('No tienes acceso a este curso');
                    }
                    
                    $videoconferenciaId = $controller->crearVideoconferencia($input);
                    if ($videoconferenciaId) {
                        echo json_encode(['success' => true, 'id' => $videoconferenciaId]);
                    } else {
                        throw new Exception('Error al crear la videoconferencia');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'actualizar_semana':
                    $semanaId = (int)($_GET['id'] ?? 0);
                    if (!$semanaId) {
                        throw new Exception('ID de la semana requerido');
                    }
                    
                    // Verificar acceso antes de actualizar
                    if (!$controller->verificarAccesoSemana($semanaId, $maestroId)) {
                        throw new Exception('No tienes acceso a esta semana');
                    }
                    
                    $resultado = $controller->actualizarSemana($semanaId, $input);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al actualizar la semana');
                    }
                    break;
                    
                case 'actualizar_contenido':
                    $contenidoId = (int)($_GET['id'] ?? 0);
                    if (!$contenidoId) {
                        throw new Exception('ID del contenido requerido');
                    }
                    
                    // Verificar acceso antes de actualizar
                    if (!$controller->verificarAccesoContenido($contenidoId, $maestroId)) {
                        throw new Exception('No tienes acceso a este contenido');
                    }
                    
                    $resultado = $controller->actualizarContenido($contenidoId, $input);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al actualizar el contenido');
                    }
                    break;
                    
                case 'actualizar_videoconferencia':
                    $videoconferenciaId = (int)($_GET['id'] ?? 0);
                    if (!$videoconferenciaId) {
                        throw new Exception('ID de la videoconferencia requerido');
                    }
                    
                    // Verificar acceso antes de actualizar
                    if (!$controller->verificarAccesoVideoconferencia($videoconferenciaId, $maestroId)) {
                        throw new Exception('No tienes acceso a esta videoconferencia');
                    }
                    
                    $resultado = $controller->actualizarVideoconferencia($videoconferenciaId, $input);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al actualizar la videoconferencia');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        case 'DELETE':
            switch ($action) {
                case 'eliminar_semana':
                    $semanaId = (int)($_GET['id'] ?? 0);
                    if (!$semanaId) {
                        throw new Exception('ID de la semana requerido');
                    }
                    
                    // Verificar acceso antes de eliminar
                    if (!$controller->verificarAccesoSemana($semanaId, $maestroId)) {
                        throw new Exception('No tienes acceso a esta semana');
                    }
                    
                    $resultado = $controller->eliminarSemana($semanaId);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al eliminar la semana');
                    }
                    break;
                    
                case 'eliminar_contenido':
                    $contenidoId = (int)($_GET['id'] ?? 0);
                    if (!$contenidoId) {
                        throw new Exception('ID del contenido requerido');
                    }
                    
                    // Verificar acceso antes de eliminar
                    if (!$controller->verificarAccesoContenido($contenidoId, $maestroId)) {
                        throw new Exception('No tienes acceso a este contenido');
                    }
                    
                    $resultado = $controller->eliminarContenido($contenidoId);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al eliminar el contenido');
                    }
                    break;
                    
                case 'eliminar_videoconferencia':
                    $videoconferenciaId = (int)($_GET['id'] ?? 0);
                    if (!$videoconferenciaId) {
                        throw new Exception('ID de la videoconferencia requerido');
                    }
                    
                    // Verificar acceso antes de eliminar
                    if (!$controller->verificarAccesoVideoconferencia($videoconferenciaId, $maestroId)) {
                        throw new Exception('No tienes acceso a esta videoconferencia');
                    }
                    
                    $resultado = $controller->eliminarVideoconferencia($videoconferenciaId);
                    if ($resultado) {
                        echo json_encode(['success' => true]);
                    } else {
                        throw new Exception('Error al eliminar la videoconferencia');
                    }
                    break;
                    
                default:
                    throw new Exception('Acción no válida');
            }
            break;
            
        default:
            throw new Exception('Método HTTP no soportado');
    }
} catch (Exception $e) {
    error_log("ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?> 