<?php
session_start();
require_once '../Modelo/conexion.php';

// Verificar que el usuario esté logueado y sea maestro
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'maestro') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

$maestroId = $_SESSION['user_id'];
$contenidoId = $_GET['contenido_id'] ?? null;

if (!$contenidoId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'ID de contenido requerido']);
    exit;
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Verificar que el contenido pertenece a un curso del maestro
    $stmtVerificar = $pdo->prepare("
        SELECT c.id, c.titulo, c.tipo, cur.maestro_id 
        FROM contenido c 
        INNER JOIN cursos cur ON c.curso_id = cur.id 
        WHERE c.id = ? AND cur.maestro_id = ?
    ");
    $stmtVerificar->execute([$contenidoId, $maestroId]);
    $contenido = $stmtVerificar->fetch();

    if (!$contenido) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Contenido no encontrado o no autorizado']);
        exit;
    }

    // Verificar que el contenido sea de tipo participación
    if ($contenido['tipo'] !== 'participacion') {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Este contenido no es de tipo participación']);
        exit;
    }

    // Obtener calificaciones de participación para este contenido
    $stmtCalificaciones = $pdo->prepare("
        SELECT 
            cp.id,
            cp.estudiante_id,
            cp.calificacion,
            cp.fecha_creacion,
            cp.fecha_actualizacion,
            u.nombre as estudiante_nombre,
            u.apellido as estudiante_apellido,
            u.avatar as estudiante_avatar,
            e.grado as estudiante_grado
        FROM calificaciones_participacion cp
        INNER JOIN usuarios u ON cp.estudiante_id = u.id
        INNER JOIN estudiantes e ON u.id = e.usuario_id
        WHERE cp.contenido_id = ?
        ORDER BY u.nombre, u.apellido
    ");
    $stmtCalificaciones->execute([$contenidoId]);
    $calificaciones = $stmtCalificaciones->fetchAll();

    // Formatear datos para el frontend
    $calificacionesFormateadas = [];
    foreach ($calificaciones as $cal) {
        $calificacionesFormateadas[] = [
            'id' => $cal['id'],
            'estudiante_id' => $cal['estudiante_id'],
            'estudiante_nombre' => $cal['estudiante_nombre'] . ' ' . $cal['estudiante_apellido'],
            'estudiante_avatar' => $cal['estudiante_avatar'],
            'estudiante_grado' => $cal['estudiante_grado'],
            'calificacion' => floatval($cal['calificacion']),
            'fecha_creacion' => $cal['fecha_creacion'],
            'fecha_actualizacion' => $cal['fecha_actualizacion']
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'contenido' => [
                'id' => $contenido['id'],
                'titulo' => $contenido['titulo'],
                'tipo' => $contenido['tipo']
            ],
            'calificaciones' => $calificacionesFormateadas
        ]
    ]);

} catch (PDOException $e) {
    error_log("Error en obtener_calificaciones_participacion.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Error interno del servidor']);
}
?>
