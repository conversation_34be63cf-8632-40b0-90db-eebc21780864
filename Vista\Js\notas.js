document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const periodButtons = document.querySelectorAll(".period-btn")
    const gradesTableTitle = document.querySelector(".section-header h2")
  
    // Cambiar período activo
    periodButtons.forEach((button) => {
      button.addEventListener("click", () => {
        // Quitar clase activa de todos los botones
        periodButtons.forEach((btn) => btn.classList.remove("active"))
  
        // Añadir clase activa al botón clickeado
        button.classList.add("active")
  
        // Actualizar título de la tabla
        if (gradesTableTitle) {
          gradesTableTitle.textContent = `Notas por curso - ${button.textContent}`
        }
  
        // Aquí se cargarían los datos del período seleccionado
        // Por ahora solo simulamos un cambio visual
        simulateDataChange(button.textContent)
      })
    })
  
    // Función para simular cambio de datos según el período
    function simulateDataChange(period) {
      // Esta función simularía la carga de datos reales
      // Por ahora solo cambiamos algunos valores para demostración
  
      // Obtener todas las barras del gráfico
      const chartBars = document.querySelectorAll(".bar")
  
      // Valores simulados para cada período
      const periodData = {
        "1er Bimestre": [
          { subject: "math", value: 14.2, height: "71%" },
          { subject: "language", value: 17.0, height: "85%" },
          { subject: "science", value: 19.5, height: "97.5%" },
          { subject: "social", value: 16.2, height: "81%" },
          { subject: "english", value: 17.6, height: "88%" },
          { subject: "art", value: 16.4, height: "82%" },
        ],
        "2do Bimestre": [
          { subject: "math", value: 15.5, height: "77.5%" },
          { subject: "language", value: 17.5, height: "87.5%" },
          { subject: "science", value: 18.0, height: "90%" },
          { subject: "social", value: 16.8, height: "84%" },
          { subject: "english", value: 18.0, height: "90%" },
          { subject: "art", value: 17.0, height: "85%" },
        ],
        "3er Bimestre": [
          { subject: "math", value: 16.0, height: "80%" },
          { subject: "language", value: 16.5, height: "82.5%" },
          { subject: "science", value: 18.5, height: "92.5%" },
          { subject: "social", value: 17.0, height: "85%" },
          { subject: "english", value: 17.5, height: "87.5%" },
          { subject: "art", value: 18.0, height: "90%" },
        ],
        "4to Bimestre": [
          { subject: "math", value: 16.5, height: "82.5%" },
          { subject: "language", value: 17.0, height: "85%" },
          { subject: "science", value: 19.0, height: "95%" },
          { subject: "social", value: 17.5, height: "87.5%" },
          { subject: "english", value: 18.5, height: "92.5%" },
          { subject: "art", value: 18.5, height: "92.5%" },
        ],
        Final: [
          { subject: "math", value: 15.5, height: "77.5%" },
          { subject: "language", value: 17.0, height: "85%" },
          { subject: "science", value: 18.8, height: "94%" },
          { subject: "social", value: 16.9, height: "84.5%" },
          { subject: "english", value: 17.9, height: "89.5%" },
          { subject: "art", value: 17.5, height: "87.5%" },
        ],
      }
  
      // Obtener datos del período seleccionado
      const data = periodData[period] || periodData["1er Bimestre"]
  
      // Actualizar barras del gráfico
      chartBars.forEach((bar, index) => {
        if (data[index]) {
          bar.style.height = data[index].height
          bar.setAttribute("title", `${bar.classList[1]}: ${data[index].value}`)
  
          // Actualizar valor mostrado
          const valueSpan = bar.querySelector(".bar-value")
          if (valueSpan) {
            valueSpan.textContent = data[index].value
          }
        }
      })
  
      // También podríamos actualizar la tabla de notas aquí
      // Por simplicidad, no lo implementamos completamente
    }
  
    // Inicializar tooltips para las barras (versión simple)
    const bars = document.querySelectorAll(".bar")
    bars.forEach((bar) => {
      bar.addEventListener("mouseover", (e) => {
        const title = bar.getAttribute("title")
        if (title) {
          // Aquí se podría implementar un tooltip personalizado
          // Por ahora usamos el título nativo del navegador
        }
      })
    })
  })
  
  