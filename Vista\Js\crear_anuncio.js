document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const crearAnuncioForm = document.getElementById("crear-anuncio-form");
    const tituloInput = document.getElementById("titulo");
    const descripcionCortaInput = document.getElementById("descripcion-corta");
    const editor = document.getElementById("editor");
    const contenidoTextarea = document.getElementById("contenido");
    const imagenPrincipalInput = document.getElementById("imagen-principal");
    const imagenPreview = document.getElementById("imagen-preview");
    const eliminarImagenBtn = document.getElementById("eliminar-imagen");
    const estadoSelect = document.getElementById("estado");
    const destacadoCheckbox = document.getElementById("destacado");
    const cancelarBtn = document.getElementById("cancelar-btn");
    const vistaPreviaBtn = document.getElementById("vista-previa-btn");
    const guardarBtn = document.getElementById("guardar-btn");
    const vistaPreviaModal = document.getElementById("vista-previa-modal");
    const confirmacionModal = document.getElementById("confirmacion-modal");
    const confirmarContinuarBtn = document.getElementById("confirmar-continuar-btn");
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn");
    const toolbarBtns = document.querySelectorAll(".toolbar-btn");
    
    // Configurar fecha actual en la cabecera
    const currentDateElement = document.querySelector(".current-date");
    if (currentDateElement) {
      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      const today = new Date();
      currentDateElement.textContent = today.toLocaleDateString('es-ES', options);
    }
    
    // Inicializar la página
    init();

    function init() {
      // Verificar si estamos en modo de edición
      checkEditMode();

      // Configurar event listeners
      setupEventListeners();
    }

    // Verificar si estamos en modo de edición
    function checkEditMode() {
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get('edit') === 'true';
      const anuncioId = urlParams.get('id');
      const titulo = urlParams.get('titulo');

      if (isEdit && anuncioId) {
        // Cambiar títulos para modo de edición
        document.getElementById('page-title').textContent = 'Editar Anuncio';
        document.getElementById('form-title').textContent = 'Editar Anuncio';
        document.getElementById('form-description').textContent = 'Modifique los campos necesarios para actualizar el anuncio';

        // Cambiar texto del botón de guardar
        const guardarBtn = document.getElementById('guardar-btn');
        if (guardarBtn) {
          guardarBtn.innerHTML = `
            <span class="material-icons">save</span>
            <span>Actualizar Anuncio</span>
          `;
        }

        // Cargar datos del anuncio para edición
        loadAnuncioData(anuncioId, titulo);
      }
    }

    // Cargar datos del anuncio para edición (simulado)
    function loadAnuncioData(anuncioId, titulo) {
      // En una implementación real, aquí se cargarían los datos desde el servidor
      // Por ahora, simulamos datos basados en el título

      if (titulo) {
        tituloInput.value = decodeURIComponent(titulo);
      }

      // Datos simulados basados en el anuncioId
      const datosSimulados = {
        'reunion-de-padres-de-familia-2do-bimestre': {
          descripcionCorta: 'Estimados padres, les informamos que la reunión para la entrega de libretas...',
          contenido: `
            <p>Estimados padres de familia:</p>
            <p>Nos complace invitarlos a la reunión de entrega de libretas correspondiente al 2do bimestre del año escolar 2025.</p>
            <p><strong>Horarios por grado:</strong></p>
            <ul>
              <li>1° y 2° grado: 8:00 am - 9:30 am</li>
              <li>3° y 4° grado: 10:00 am - 11:30 am</li>
              <li>5° y 6° grado: 12:00 pm - 1:30 pm</li>
            </ul>
          `,
          estado: 'publicado'
        },
        'feria-de-ciencias-2025': {
          descripcionCorta: 'Invitamos a todos los estudiantes a participar en nuestra feria anual de ciencias...',
          contenido: '<p>Invitamos a todos los estudiantes a participar en nuestra feria anual de ciencias donde podrán mostrar sus proyectos innovadores.</p>',
          estado: 'publicado'
        },
        'taller-de-lectura-para-padres-e-hijos': {
          descripcionCorta: 'Les invitamos a participar en nuestro taller de lectura para padres e hijos...',
          contenido: '<p>Les invitamos a participar en nuestro taller de lectura para padres e hijos, una actividad que fortalecerá los vínculos familiares.</p>',
          estado: 'publicado'
        },
        'actualizacion-de-datos-familiares': {
          descripcionCorta: 'Solicitamos a todos los padres de familia actualizar sus datos de contacto...',
          contenido: '<p>Solicitamos a todos los padres de familia actualizar sus datos de contacto para mantener una comunicación efectiva.</p>',
          estado: 'borrador'
        }
      };

      const datos = datosSimulados[anuncioId];
      if (datos) {
        descripcionCortaInput.value = datos.descripcionCorta;
        editor.innerHTML = datos.contenido;
        estadoSelect.value = datos.estado;

        // Actualizar textarea oculto
        updateHiddenTextarea();
      }
    }
    
    function setupEventListeners() {
      // Editor de texto
      editor.addEventListener("input", updateHiddenTextarea);
      
      // Botones de la barra de herramientas
      toolbarBtns.forEach(btn => {
        btn.addEventListener("click", handleToolbarAction);
      });
      
      // Carga de imagen
      imagenPrincipalInput.addEventListener("change", handleImageUpload);
      eliminarImagenBtn.addEventListener("click", handleImageRemove);
      
      // Botones de acción
      cancelarBtn.addEventListener("click", handleCancel);
      vistaPreviaBtn.addEventListener("click", handleVistaPrevia);
      crearAnuncioForm.addEventListener("submit", handleSubmit);
      
      // Cerrar modales
      modalCloseBtns.forEach(btn => {
        btn.addEventListener("click", closeModals);
      });
      
      // Confirmar acción
      confirmarContinuarBtn.addEventListener("click", confirmAction);
    }
    
    // Actualizar textarea oculto con el contenido del editor
    function updateHiddenTextarea() {
      contenidoTextarea.value = editor.innerHTML;
    }
    
    // Manejar acciones de la barra de herramientas
    function handleToolbarAction(e) {
      e.preventDefault();
      
      const button = e.currentTarget;
      const format = button.dataset.format;
      
      switch (format) {
        case "bold":
          document.execCommand("bold", false, null);
          break;
        case "italic":
          document.execCommand("italic", false, null);
          break;
        case "underline":
          document.execCommand("underline", false, null);
          break;
        case "h2":
          document.execCommand("formatBlock", false, "<h2>");
          break;
        case "h3":
          document.execCommand("formatBlock", false, "<h3>");
          break;
        case "ul":
          document.execCommand("insertUnorderedList", false, null);
          break;
        case "ol":
          document.execCommand("insertOrderedList", false, null);
          break;
        case "link":
          const url = prompt("Ingrese la URL del enlace:", "https://");
          if (url) {
            document.execCommand("createLink", false, url);
          }
          break;
        case "image":
          const imageUrl = prompt("Ingrese la URL de la imagen:", "https://");
          if (imageUrl) {
            document.execCommand("insertImage", false, imageUrl);
          }
          break;
      }
      
      // Actualizar textarea oculto
      updateHiddenTextarea();
      
      // Mantener el foco en el editor
      editor.focus();
    }
    
    // Manejar carga de imagen
    function handleImageUpload(e) {
      const file = e.target.files[0];
      
      if (!file) return;
      
      // Validar tipo de archivo
      const validTypes = ["image/jpeg", "image/png", "image/gif"];
      if (!validTypes.includes(file.type)) {
        alert("Por favor, seleccione un archivo de imagen válido (JPG, PNG o GIF).");
        imagenPrincipalInput.value = "";
        return;
      }
      
      // Validar tamaño de archivo (2MB máximo)
      const maxSize = 2 * 1024 * 1024; // 2MB en bytes
      if (file.size > maxSize) {
        alert("El archivo es demasiado grande. El tamaño máximo permitido es 2MB.");
        imagenPrincipalInput.value = "";
        return;
      }
      
      // Mostrar vista previa
      const reader = new FileReader();
      reader.onload = function(event) {
        imagenPreview.innerHTML = `<img src="${event.target.result}" alt="Vista previa de la imagen">`;
        eliminarImagenBtn.disabled = false;
      };
      reader.readAsDataURL(file);
    }
    
    // Manejar eliminación de imagen
    function handleImageRemove() {
      imagenPrincipalInput.value = "";
      imagenPreview.innerHTML = `
        <span class="material-icons">image</span>
        <p>Ninguna imagen seleccionada</p>
      `;
      eliminarImagenBtn.disabled = true;
    }
    
    // Manejar cancelación
    function handleCancel() {
      // Mostrar modal de confirmación
      confirmacionModal.classList.add("active");
      document.getElementById("confirmacion-mensaje").textContent = "¿Está seguro que desea cancelar? Los cambios no guardados se perderán.";
      
      // Configurar acción a realizar si se confirma
      confirmarContinuarBtn.dataset.action = "cancelar";
    }
    
    // Manejar vista previa
    function handleVistaPrevia() {
      // Obtener valores del formulario
      const titulo = tituloInput.value || "Título del Anuncio";
      const descripcionCorta = descripcionCortaInput.value || "Descripción corta del anuncio que aparecerá en las vistas previas.";
      const contenido = editor.innerHTML || "<p>Contenido completo del anuncio...</p>";

      // Obtener fecha actual formateada
      const fechaActual = new Date();
      const fechaFormateada = fechaActual.toLocaleDateString("es-ES", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
      
      // Actualizar elementos de la vista previa
      document.getElementById("preview-titulo").textContent = titulo;
      document.getElementById("preview-fecha").textContent = fechaFormateada;
      document.getElementById("preview-descripcion").textContent = descripcionCorta;
      document.getElementById("preview-contenido").innerHTML = contenido;
      
      // Mostrar imagen si está disponible
      const previewImagen = document.getElementById("preview-imagen");
      if (imagenPrincipalInput.files && imagenPrincipalInput.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          previewImagen.src = e.target.result;
        };
        reader.readAsDataURL(imagenPrincipalInput.files[0]);
      } else {
        previewImagen.src = "/placeholder.svg?height=400&width=800";
      }
      
      // Mostrar modal de vista previa
      vistaPreviaModal.classList.add("active");
    }
    
    // Manejar envío del formulario
    function handleSubmit(e) {
      e.preventDefault();

      // Validar campos requeridos
      if (!validateForm()) {
        return;
      }

      // Verificar si estamos en modo de edición
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get('edit') === 'true';

      // Simulación de envío de datos
      const formData = new FormData(crearAnuncioForm);

      // Agregar contenido del editor
      formData.set("contenido", editor.innerHTML);

      // Mostrar mensaje de éxito apropiado
      if (isEdit) {
        alert("¡Anuncio actualizado correctamente!");
        console.log("Actualizando anuncio...");
      } else {
        alert("¡Anuncio creado correctamente!");
        console.log("Creando nuevo anuncio...");
      }

      // Redireccionar a la página de anuncios
      setTimeout(() => {
        window.location.href = "anuncios_admin.html";
      }, 1000);

      // En una implementación real, aquí se enviarían los datos al servidor
      console.log("Datos del formulario:", Object.fromEntries(formData));
    }
    
    // Validar formulario
    function validateForm() {
      // Validar título
      if (!tituloInput.value.trim()) {
        alert("Por favor, ingrese un título para el anuncio.");
        tituloInput.focus();
        return false;
      }

      // Validar descripción corta
      if (!descripcionCortaInput.value.trim()) {
        alert("Por favor, ingrese una descripción corta.");
        descripcionCortaInput.focus();
        return false;
      }
      
      // Validar contenido
      if (!editor.textContent.trim()) {
        alert("Por favor, ingrese el contenido del anuncio.");
        editor.focus();
        return false;
      }
      
      // Validar imagen (solo si es requerida y no estamos editando)
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get('edit') === 'true';

      if (!isEdit && (!imagenPrincipalInput.files || !imagenPrincipalInput.files[0])) {
        alert("Por favor, seleccione una imagen principal.");
        return false;
      }
      
      return true;
    }
    
    // Confirmar acción
    function confirmAction() {
      const action = confirmarContinuarBtn.dataset.action;
      
      if (action === "cancelar") {
        // Redireccionar a la página de anuncios
        window.location.href = "anuncios_admin.html";
      }
      
      // Cerrar modal
      closeModals();
    }
    
    // Cerrar modales
    function closeModals() {
      vistaPreviaModal.classList.remove("active");
      confirmacionModal.classList.remove("active");
    }
  });