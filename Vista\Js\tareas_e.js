document.addEventListener("DOMContentLoaded", () => {
  // Referencias a elementos del DOM
  const taskItems = document.querySelectorAll(".task-item")
  const taskViewModal = document.getElementById("task-view")
  const closeTaskBtn = document.getElementById("close-task-btn")

  // Añadir evento para ver detalles de la tarea
  taskItems.forEach((item) => {
    const viewBtn = item.querySelector(".task-action-btn:first-child")
    if (viewBtn) {
      viewBtn.addEventListener("click", () => {
        taskViewModal.classList.add("active")
      })
    }
  })

  // Cerrar modal de tarea
  if (closeTaskBtn) {
    closeTaskBtn.addEventListener("click", () => {
      taskViewModal.classList.remove("active")
    })
  }

  // Cerrar modal al hacer clic fuera del contenido
  if (taskViewModal) {
    taskViewModal.addEventListener("click", (e) => {
      if (e.target === taskViewModal) {
        taskViewModal.classList.remove("active")
      }
    })
  }

  // Filtrar tareas
  const searchInput = document.querySelector(".search-box input")
  const statusFilter = document.querySelector(".filter-options select:first-child")
  const courseFilter = document.querySelector(".filter-options select:last-child")

  if (searchInput) {
    searchInput.addEventListener("input", filterTasks)
  }

  if (statusFilter) {
    statusFilter.addEventListener("change", filterTasks)
  }

  if (courseFilter) {
    courseFilter.addEventListener("change", filterTasks)
  }

  function filterTasks() {
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : ""
    const statusValue = statusFilter ? statusFilter.value : "all"
    const courseValue = courseFilter ? courseFilter.value : "all"

    taskItems.forEach((item) => {
      const taskTitle = item.querySelector("h3").textContent.toLowerCase()
      const taskDescription = item.querySelector("p").textContent.toLowerCase()
      const isCompleted = item.querySelector(".task-status").classList.contains("completed")
      const courseText = item.querySelector(".task-course").textContent.toLowerCase()

      // Filtrar por texto
      const matchesSearch = taskTitle.includes(searchTerm) || taskDescription.includes(searchTerm)

      // Filtrar por estado
      let matchesStatus = true
      if (statusValue === "pending") {
        matchesStatus = !isCompleted
      } else if (statusValue === "completed") {
        matchesStatus = isCompleted
      }

      // Filtrar por curso
      let matchesCourse = true
      if (courseValue !== "all") {
        // Simplificado para demostración, en un caso real se usarían IDs
        matchesCourse = courseText.includes(courseValue.toLowerCase())
      }

      // Mostrar u ocultar según los filtros
      if (matchesSearch && matchesStatus && matchesCourse) {
        item.style.display = "flex"
      } else {
        item.style.display = "none"
      }
    })
  }
})
