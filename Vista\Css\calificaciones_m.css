/* Estilos específicos para la página de calificaciones (vista de maestros) */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Sección de calificaciones */
  .grades-table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 20px;
  }
  
  .grades-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .grades-table th,
  .grades-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }
  
  .grades-table th {
    background-color: var(--secondary-color);
    font-weight: 600;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .grades-table tr:last-child td {
    border-bottom: none;
  }
  
  .grades-table tr:hover td {
    background-color: var(--secondary-color);
  }
  
  .student-col {
    min-width: 200px;
  }
  
  .student-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .student-avatar.large {
    width: 80px;
    height: 80px;
  }
  
  .student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .student-details {
    display: flex;
    flex-direction: column;
  }
  
  .student-name {
    font-weight: 500;
    margin-bottom: 3px;
  }
  
  .student-id {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .grade-cell {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .grade-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-color);
  }
  
  .grade-max {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .grade-cell.average .grade-value {
    color: var(--primary-color);
  }
  
  .grade-cell.pending .grade-value {
    color: var(--warning-color);
  }
  
  .grade-actions {
    display: flex;
    gap: 8px;
  }
  
  .grade-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .grade-action-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .grade-action-btn.edit-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .grade-action-btn.view-btn:hover {
    color: var(--info-color);
    border-color: var(--info-color);
  }
  
  /* Filtros y búsqueda */
  .section-actions {
    display: flex;
    gap: 15px;
    align-items: center;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    padding: 8px 15px 8px 40px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    width: 250px;
  }
  
  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
  }
  
  .export-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: #217346;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }

  .export-btn:hover {
    background-color: #1a5c37;
  }
  
  /* Modal para editar calificaciones */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  .student-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .student-profile h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 5px 0;
  }
  
  .student-profile p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
  }
  
  .edit-grades-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .form-group label {
    font-weight: 500;
    color: var(--text-color);
  }
  
  .grade-input {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .grade-input input {
    width: 80px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    color: var(--text-color);
  }
  
  .grade-input .grade-max {
    font-size: 1rem;
    color: var(--text-light);
  }
  
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    resize: vertical;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 10px;
  }
  
  .btn-secondary,
  .btn-primary {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-color);
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  /* Detalles de calificaciones */
  .grade-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .grade-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: var(--text-color);
  }
  
  .grade-details-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .grade-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--secondary-color);
    border-radius: 8px;
  }
  
  .grade-detail-item.total {
    background-color: var(--primary-light);
    font-weight: 600;
  }
  
  .grade-detail-name {
    font-size: 0.95rem;
    color: var(--text-color);
  }
  
  .grade-detail-value {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .grade-comments {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
  }
  
  .grade-comments p {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
    margin: 0;
  }

  /* Divisores de sección */
  .grades-section-divider,
  .grade-section-divider {
    margin: 25px 0 15px 0;
    padding-top: 15px;
    border-top: 2px solid var(--border-color);
  }

  .grades-section-divider h5,
  .grade-section-divider h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .section-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  
    .search-input {
      width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .student-profile {
      flex-direction: column;
      text-align: center;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }
  
  