<?php
require_once 'Modelo/Conexion.php';

class ContenidoController {
    private $conexion;
    
    public function __construct() {
        $this->conexion = Conexion::getConexion();
    }
    
    /**
     * Verifica si un maestro tiene acceso a un curso específico
     */
    public function verificarAccesoMaestro($cursoId, $usuarioId) {
        // Primero obtener el maestro_id del usuario
        $query = "SELECT m.id as maestro_id 
                  FROM maestros m 
                  JOIN personas p ON m.persona_id = p.id 
                  JOIN usuarios u ON p.usuario_id = u.id 
                  WHERE u.id = ? AND u.rol = 'maestro'";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$usuarioId]);
        $maestro = $stmt->fetch();
        
        if (!$maestro) {
            return false;
        }
        
        // Ahora verificar si el curso pertenece a este maestro
        $query = "SELECT COUNT(*) as count FROM cursos WHERE id = ? AND maestro_id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId, $maestro['maestro_id']]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * Obtiene las semanas académicas de un curso
     */
    public function obtenerSemanasPorCurso($cursoId) {
        $query = "SELECT * FROM semanas_academicas WHERE curso_id = ? AND activo = 1 ORDER BY numero_semana";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Obtiene el contenido de un curso
     */
    public function obtenerContenidoPorCurso($cursoId) {
        $query = "SELECT c.*, sa.numero_semana, sa.titulo as semana_titulo 
                  FROM contenido c 
                  JOIN sesiones s ON c.sesion_id = s.id 
                  JOIN semanas_academicas sa ON s.semana_id = sa.id 
                  WHERE s.curso_id = ? AND c.activo = 1 
                  ORDER BY sa.numero_semana, s.orden, c.created_at";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Obtiene las videoconferencias de un curso
     */
    public function obtenerVideoconferenciasPorCurso($cursoId) {
        $query = "SELECT * FROM enlaces_videoconferencia WHERE curso_id = ? AND activo = 1 ORDER BY fecha DESC, created_at DESC";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Crea una nueva semana académica
     */
    public function crearSemana($datos) {
        $query = "INSERT INTO semanas_academicas (curso_id, numero_semana, titulo, fecha_inicio, fecha_fin, descripcion, activo) 
                  VALUES (?, ?, ?, ?, ?, ?, 1)";
        $stmt = $this->conexion->prepare($query);
        
        // Obtener el siguiente número de semana
        $numeroSemana = $this->obtenerSiguienteNumeroSemana($datos['curso_id']);
        
        $resultado = $stmt->execute([
            $datos['curso_id'],
            $numeroSemana,
            $datos['titulo'],
            $datos['fecha_inicio'],
            $datos['fecha_fin'],
            $datos['descripcion'] ?? ''
        ]);
        
        if ($resultado) {
            return $this->conexion->lastInsertId();
        }
        return false;
    }
    
    /**
     * Actualiza una semana académica
     */
    public function actualizarSemana($semanaId, $datos) {
        $query = "UPDATE semanas_academicas SET titulo = ?, fecha_inicio = ?, fecha_fin = ?, descripcion = ? WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([
            $datos['titulo'],
            $datos['fecha_inicio'],
            $datos['fecha_fin'],
            $datos['descripcion'] ?? '',
            $semanaId
        ]);
    }
    
    /**
     * Elimina una semana académica (eliminación física de semana, sesiones y contenidos asociados)
     */
    public function eliminarSemana($semanaId) {
        // 1. Obtener todas las sesiones asociadas a la semana
        $query = "SELECT id FROM sesiones WHERE semana_id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId]);
        $sesiones = $stmt->fetchAll();

        // 2. Eliminar todos los contenidos asociados a esas sesiones
        if ($sesiones) {
            $ids = array_column($sesiones, 'id');
            $in = str_repeat('?,', count($ids) - 1) . '?';
            $query = "DELETE FROM contenido WHERE sesion_id IN ($in)";
            $stmt = $this->conexion->prepare($query);
            $stmt->execute($ids);
        }

        // 3. Eliminar las sesiones asociadas a la semana
        $query = "DELETE FROM sesiones WHERE semana_id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId]);

        // 4. Eliminar la semana
        $query = "DELETE FROM semanas_academicas WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId]);

        return true;
    }
    
    /**
     * Crea una nueva videoconferencia
     */
    public function crearVideoconferencia($datos) {
        $query = "INSERT INTO enlaces_videoconferencia (curso_id, titulo, url, fecha, hora, activo) 
                  VALUES (?, ?, ?, ?, ?, 1)";
        $stmt = $this->conexion->prepare($query);
        
        $resultado = $stmt->execute([
            $datos['curso_id'],
            $datos['titulo'],
            $datos['url'],
            $datos['fecha'],
            $datos['hora'],
        ]);
        
        if ($resultado) {
            return $this->conexion->lastInsertId();
        }
        return false;
    }
    
    /**
     * Actualiza una videoconferencia
     */
    public function actualizarVideoconferencia($videoconferenciaId, $datos) {
        $query = "UPDATE enlaces_videoconferencia SET titulo = ?, url = ?, fecha = ?, hora = ? WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([
            $datos['titulo'],
            $datos['url'],
            $datos['fecha'],
            $datos['hora'],
            $videoconferenciaId
        ]);
    }
    
    /**
     * Elimina una videoconferencia (marca como inactiva)
     */
    public function eliminarVideoconferencia($videoconferenciaId) {
        $query = "UPDATE enlaces_videoconferencia SET activo = 0 WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([$videoconferenciaId]);
    }
    
    /**
     * Crea nuevo contenido
     */
    public function crearContenido($datos) {
        // Log temporal para depuración
        error_log('Valor de tipo recibido en crearContenido: ' . var_export($datos['tipo'], true));
        
        // Validar que el tipo no sea vacío
        if (!isset($datos['tipo']) || trim($datos['tipo']) === '') {
            throw new Exception('El tipo de contenido es obligatorio.');
        }
        
        // Validar que el tipo sea uno de los valores válidos del ENUM
        $tiposValidos = ['anuncio', 'tarea', 'examen', 'documento', 'presentacion', 'enlace', 'video', 'participacion'];
        if (!in_array($datos['tipo'], $tiposValidos)) {
            throw new Exception('El tipo de contenido debe ser uno de: ' . implode(', ', $tiposValidos));
        }
        
        // Primero crear la sesión si no existe
        $sesionId = $this->crearObtenerSesion($datos['semana_id'], $datos['curso_id']);
        $query = "INSERT INTO contenido (sesion_id, tipo, titulo, descripcion, fecha_limite, puntos, url, activo) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
        $stmt = $this->conexion->prepare($query);
        $resultado = $stmt->execute([
            $sesionId,
            $datos['tipo'],
            $datos['titulo'],
            $datos['descripcion'],
            $datos['fecha_limite'] ?? null,
            $datos['puntos'] ?? null,
            $datos['url'] ?? null
        ]);
        if ($resultado) {
            return $this->conexion->lastInsertId();
        }
        return false;
    }
    
    /**
     * Actualiza contenido existente
     */
    public function actualizarContenido($contenidoId, $datos) {
        // Log temporal para depuración
        error_log('Valor de tipo recibido en actualizarContenido: ' . var_export($datos['tipo'], true));
        
        // Validar que el tipo no sea vacío
        if (!isset($datos['tipo']) || trim($datos['tipo']) === '') {
            throw new Exception('El tipo de contenido es obligatorio.');
        }
        
        // Validar que el tipo sea uno de los valores válidos del ENUM
        $tiposValidos = ['anuncio', 'tarea', 'examen', 'documento', 'presentacion', 'enlace', 'video', 'participacion'];
        if (!in_array($datos['tipo'], $tiposValidos)) {
            throw new Exception('El tipo de contenido debe ser uno de: ' . implode(', ', $tiposValidos));
        }
        
        $query = "UPDATE contenido SET tipo = ?, titulo = ?, descripcion = ?, fecha_limite = ?, puntos = ?, url = ? WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([
            $datos['tipo'],
            $datos['titulo'],
            $datos['descripcion'],
            $datos['fecha_limite'] ?? null,
            $datos['puntos'] ?? null,
            $datos['url'] ?? null,
            $contenidoId
        ]);
    }
    
    /**
     * Elimina contenido (borrado físico)
     */
    public function eliminarContenido($contenidoId) {
        $query = "DELETE FROM contenido WHERE id = ?";
        $stmt = $this->conexion->prepare($query);
        return $stmt->execute([$contenidoId]);
    }
    
    /**
     * Obtiene el siguiente número de semana para un curso
     */
    private function obtenerSiguienteNumeroSemana($cursoId) {
        $query = "SELECT MAX(numero_semana) as max_numero FROM semanas_academicas WHERE curso_id = ? AND activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$cursoId]);
        $result = $stmt->fetch();
        return ($result['max_numero'] ?? 0) + 1;
    }
    
    /**
     * Crea o obtiene una sesión para una semana
     */
    private function crearObtenerSesion($semanaId, $cursoId) {
        // Verificar si ya existe una sesión activa para esta semana
        $query = "SELECT id FROM sesiones WHERE semana_id = ? AND curso_id = ? AND activo = 1 LIMIT 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId, $cursoId]);
        $sesion = $stmt->fetch();
        
        if ($sesion) {
            return $sesion['id'];
        }
        
        // Verificar si existe una sesión inactiva para reactivarla
        $query = "SELECT id FROM sesiones WHERE semana_id = ? AND curso_id = ? AND activo = 0 LIMIT 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId, $cursoId]);
        $sesionInactiva = $stmt->fetch();
        
        if ($sesionInactiva) {
            // Reactivar la sesión existente
            $query = "UPDATE sesiones SET activo = 1 WHERE id = ?";
            $stmt = $this->conexion->prepare($query);
            $stmt->execute([$sesionInactiva['id']]);
            return $sesionInactiva['id'];
        }
        
        // Crear nueva sesión
        $query = "INSERT INTO sesiones (semana_id, curso_id, titulo, orden, activo) VALUES (?, ?, 'Sesión principal', 1, 1)";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId, $cursoId]);
        return $this->conexion->lastInsertId();
    }
    
    /**
     * Verifica si un maestro tiene acceso a una semana específica
     */
    public function verificarAccesoSemana($semanaId, $usuarioId) {
        // Primero obtener el maestro_id del usuario
        $query = "SELECT m.id as maestro_id 
                  FROM maestros m 
                  JOIN personas p ON m.persona_id = p.id 
                  JOIN usuarios u ON p.usuario_id = u.id 
                  WHERE u.id = ? AND u.rol = 'maestro'";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$usuarioId]);
        $maestro = $stmt->fetch();
        
        if (!$maestro) {
            return false;
        }
        
        // Verificar si la semana pertenece a un curso del maestro
        $query = "SELECT COUNT(*) as count 
                  FROM semanas_academicas sa 
                  JOIN cursos c ON sa.curso_id = c.id 
                  WHERE sa.id = ? AND c.maestro_id = ? AND sa.activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId, $maestro['maestro_id']]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * Verifica si un maestro tiene acceso a un contenido específico
     */
    public function verificarAccesoContenido($contenidoId, $usuarioId) {
        // Primero obtener el maestro_id del usuario
        $query = "SELECT m.id as maestro_id 
                  FROM maestros m 
                  JOIN personas p ON m.persona_id = p.id 
                  JOIN usuarios u ON p.usuario_id = u.id 
                  WHERE u.id = ? AND u.rol = 'maestro'";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$usuarioId]);
        $maestro = $stmt->fetch();
        
        if (!$maestro) {
            return false;
        }
        
        // Verificar si el contenido pertenece a un curso del maestro
        $query = "SELECT COUNT(*) as count 
                  FROM contenido c 
                  JOIN sesiones s ON c.sesion_id = s.id 
                  JOIN cursos cur ON s.curso_id = cur.id 
                  WHERE c.id = ? AND cur.maestro_id = ? AND c.activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$contenidoId, $maestro['maestro_id']]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * Verifica si un maestro tiene acceso a una videoconferencia específica
     */
    public function verificarAccesoVideoconferencia($videoconferenciaId, $usuarioId) {
        // Primero obtener el maestro_id del usuario
        $query = "SELECT m.id as maestro_id 
                  FROM maestros m 
                  JOIN personas p ON m.persona_id = p.id 
                  JOIN usuarios u ON p.usuario_id = u.id 
                  WHERE u.id = ? AND u.rol = 'maestro'";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$usuarioId]);
        $maestro = $stmt->fetch();
        
        if (!$maestro) {
            return false;
        }
        
        // Verificar si la videoconferencia pertenece a un curso del maestro
        $query = "SELECT COUNT(*) as count 
                  FROM enlaces_videoconferencia ev 
                  JOIN cursos c ON ev.curso_id = c.id 
                  WHERE ev.id = ? AND c.maestro_id = ? AND ev.activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$videoconferenciaId, $maestro['maestro_id']]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * Obtiene contenido por ID
     */
    public function obtenerContenidoPorId($contenidoId) {
        $query = "SELECT c.*, s.semana_id as semana_id, sa.numero_semana, sa.titulo as semana_titulo 
                  FROM contenido c 
                  JOIN sesiones s ON c.sesion_id = s.id 
                  JOIN semanas_academicas sa ON s.semana_id = sa.id 
                  WHERE c.id = ? AND c.activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$contenidoId]);
        return $stmt->fetch();
    }

    /**
     * Obtiene semana por ID
     */
    public function obtenerSemanaPorId($semanaId) {
        $query = "SELECT * FROM semanas_academicas WHERE id = ? AND activo = 1";
        $stmt = $this->conexion->prepare($query);
        $stmt->execute([$semanaId]);
        return $stmt->fetch();
    }
}
?> 