/* Estilos específicos para la sección de estudiantes (vista de estudiante) */

/* Variables */
:root {
  --primary-color: #2a4db7;
  --primary-light: #e6f0ff;
  --secondary-color: #f4f7fc;
  --text-color: #333;
  --text-light: #666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Cuadrícula de compañeros de clase */
.classmates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.classmate-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.classmate-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.classmate-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--primary-light);
  margin-bottom: 15px;
}

.classmate-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.classmate-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.classmate-info p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

/* Paginación */
.pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 20px;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid var(--border-color);
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Sección de mensajes al profesor */
.teacher-message-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
}

.message-form {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.message-form h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 0.95rem;
  color: var(--text-color);
  background-color: white;
  outline: none;
  transition: var(--transition);
}

.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.send-message-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.send-message-btn:hover {
  background-color: #1e40af;
}

.message-history {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.message-history h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.message-item {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  overflow: hidden;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--secondary-color);
  border-bottom: 1px solid var(--border-color);
}

.message-info {
  display: flex;
  flex-direction: column;
}

.message-subject {
  font-weight: 600;
  color: var(--text-color);
}

.message-date {
  font-size: 0.85rem;
  color: var(--text-light);
}

.message-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.85rem;
  padding: 3px 10px;
  border-radius: 15px;
}

.message-status.replied {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.message-status.pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.message-body {
  padding: 15px;
  background-color: white;
}

.message-body p {
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
}

.message-reply {
  padding: 15px;
  background-color: var(--primary-light);
  border-top: 1px solid var(--border-color);
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.reply-author {
  font-weight: 600;
  color: var(--primary-color);
}

.reply-date {
  font-size: 0.85rem;
  color: var(--text-light);
}

.reply-content p {
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 992px) {
  .teacher-message-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .classmates-grid {
    grid-template-columns: 1fr;
  }
}

/* Estilos específicos para maestros */
.filter-actions {
    display: flex;
    gap: 10px;
}

.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.student-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.student-card.enrolled {
    border-left: 4px solid #4caf50;
}

.student-avatar {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.student-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #f0f0f0;
}

.student-info {
    text-align: center;
    margin-bottom: 15px;
}

.student-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.student-code {
    font-size: 0.85rem;
    color: #666;
    margin: 0 0 3px 0;
    font-family: 'Courier New', monospace;
}

.student-grade {
    font-size: 0.9rem;
    color: #888;
    margin: 0;
}

.student-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.student-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    background: #f5f5f5;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-action-btn:hover {
    background: #e0e0e0;
    transform: scale(1.05);
}

.student-action-btn.delete-btn:hover {
    background: #ffebee;
    color: #f44336;
}

.student-action-btn .material-icons {
    font-size: 18px;
}

/* Estilos para modal de agregar estudiante */
.search-students-section {
    margin-bottom: 20px;
}

.available-students-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 15px;
}

.student-search-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.student-search-item:last-child {
    border-bottom: none;
}

.student-search-item:hover {
    background-color: #f8f9fa;
}

.student-search-item .student-avatar {
    margin-right: 15px;
    margin-bottom: 0;
}

.student-search-item .student-avatar img {
    width: 50px;
    height: 50px;
}

.student-search-item .student-info {
    flex: 1;
    text-align: left;
    margin-bottom: 0;
}

.student-search-item .student-info h4 {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    margin: 0 0 3px 0;
}

.add-student-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.add-student-btn:hover {
    background: #45a049;
    transform: scale(1.05);
}

.add-student-btn .material-icons {
    font-size: 16px;
}

/* Estilos para modal de confirmación */
.confirmation-message {
    text-align: center;
    padding: 20px;
}

.warning-icon {
    font-size: 48px;
    color: #ff9800;
    margin-bottom: 15px;
}

.confirmation-message p {
    margin: 10px 0;
    line-height: 1.5;
}

.warning-text {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive para funcionalidad de maestros */
@media (max-width: 768px) {
    .students-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }

    .student-card {
        padding: 15px;
    }

    .student-avatar img {
        width: 60px;
        height: 60px;
    }

    .student-info h3 {
        font-size: 1rem;
    }

    .student-info p {
        font-size: 0.85rem;
    }

    .filter-actions {
        flex-direction: column;
    }

    .student-search-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .student-search-item .student-info {
        text-align: center;
    }

    .student-search-item .student-avatar {
        margin-right: 0;
    }
}