-- Tabla para calificaciones de participación
CREATE TABLE IF NOT EXISTS calificaciones_participacion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    estudiante_id INT NOT NULL,
    contenido_id INT NOT NULL,
    calificacion DECIMAL(4,2) NOT NULL DEFAULT 0.00,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Claves foráneas
    FOREIGN KEY (estudiante_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
    
    -- Índices para mejorar rendimiento
    INDEX idx_estudiante_contenido (estudiante_id, contenido_id),
    INDEX idx_contenido (contenido_id),
    INDEX idx_estudiante (estudiante_id),
    
    -- Restricción única para evitar duplicados
    UNIQUE KEY unique_estudiante_contenido (estudiante_id, contenido_id),
    
    -- Restricción para validar rango de calificación
    CHECK (calificacion >= 0 AND calificacion <= 20)
);

-- Comentarios para documentar la tabla
ALTER TABLE calificaciones_participacion 
COMMENT = 'Tabla para almacenar calificaciones de contenido tipo participación';

-- Comentarios para las columnas
ALTER TABLE calificaciones_participacion
MODIFY COLUMN id INT AUTO_INCREMENT COMMENT 'ID único de la calificación',
MODIFY COLUMN estudiante_id INT NOT NULL COMMENT 'ID del estudiante (referencia a usuarios)',
MODIFY COLUMN contenido_id INT NOT NULL COMMENT 'ID del contenido de participación',
MODIFY COLUMN calificacion DECIMAL(4,2) NOT NULL DEFAULT 0.00 COMMENT 'Calificación del estudiante (0-20)',
MODIFY COLUMN fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación de la calificación',
MODIFY COLUMN fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización';
