document.addEventListener("DOMContentLoaded", () => {
    // Actualizar fecha actual
    const dateElements = document.querySelectorAll(".current-date")
    if (dateElements.length > 0) {
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      const today = new Date()
      dateElements.forEach((element) => {
        element.textContent = today.toLocaleDateString("es-ES", options)
      })
    }
  
    // Funcionalidad para los botones de recursos
    const resourceViewBtns = document.querySelectorAll(".resource-btn:first-child")
    if (resourceViewBtns.length > 0) {
      resourceViewBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
          const resourceCard = e.currentTarget.closest(".resource-card")
          const resourceTitle = resourceCard.querySelector(".resource-title").textContent
  
          // En una implementación real, aquí se abriría una vista previa del recurso
          console.log(`Vista previa de: ${resourceTitle}`)
          alert(`Vista previa de: ${resourceTitle}`)
        })
      })
    }
  
    const resourceDownloadBtns = document.querySelectorAll(".resource-btn:last-child")
    if (resourceDownloadBtns.length > 0) {
      resourceDownloadBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
          const resourceCard = e.currentTarget.closest(".resource-card")
          const resourceTitle = resourceCard.querySelector(".resource-title").textContent
  
          // En una implementación real, aquí se iniciaría la descarga del recurso
          console.log(`Descargando: ${resourceTitle}`)
          alert(`Descargando: ${resourceTitle}`)
        })
      })
    }
  
    // Funcionalidad para las clases en el horario
    const scheduleClasses = document.querySelectorAll(".schedule-class")
    if (scheduleClasses.length > 0) {
      scheduleClasses.forEach((classItem) => {
        classItem.addEventListener("click", () => {
          const className = classItem.querySelector(".class-name").textContent
          const classGroup = classItem.querySelector(".class-group").textContent
  
          // En una implementación real, aquí se mostraría información detallada de la clase
          console.log(`Detalles de la clase: ${className} - ${classGroup}`)
          alert(`Detalles de la clase: ${className} - ${classGroup}`)
        })
      })
    }
  
    // Funcionalidad para los mensajes
    const messageItems = document.querySelectorAll(".message-item")
    if (messageItems.length > 0) {
      messageItems.forEach((message) => {
        message.addEventListener("click", () => {
          const sender = message.querySelector(".message-sender").textContent
  
          // En una implementación real, aquí se abriría la conversación con el remitente
          console.log(`Abriendo conversación con: ${sender}`)
  
          // Marcar como leído (quitar la clase unread)
          message.classList.remove("unread")
        })
      })
    }
  
    // Funcionalidad para las tareas pendientes
    const taskItems = document.querySelectorAll(".task-item")
    if (taskItems.length > 0) {
      taskItems.forEach((task) => {
        task.addEventListener("click", () => {
          const taskTitle = task.querySelector(".task-title").textContent
          const taskCourse = task.querySelector(".task-course").textContent
  
          // En una implementación real, aquí se abriría la página de calificación de la tarea
          console.log(`Calificar tarea: ${taskTitle} - ${taskCourse}`)
        })
      })
    }
  
    // Funcionalidad para los eventos
    const eventItems = document.querySelectorAll(".event-item")
    if (eventItems.length > 0) {
      eventItems.forEach((event) => {
        event.addEventListener("click", () => {
          const eventTitle = event.querySelector(".event-title").textContent
          const eventDate =
            event.querySelector(".date-day").textContent + "/" + event.querySelector(".date-month").textContent
  
          // En una implementación real, aquí se mostraría información detallada del evento
          console.log(`Detalles del evento: ${eventTitle} - ${eventDate}`)
        })
      })
    }
  })
    