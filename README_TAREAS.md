# Sistema de Gestión de Tareas y Exámenes

## Descripción General

Este sistema permite a los maestros gestionar tareas y exámenes para sus cursos, incluyendo la creación, edición, eliminación y calificación de entregas de estudiantes.

## Arquitectura del Sistema

### Backend
- **Controlador**: `Controlador/TareaController.php` - Maneja toda la lógica de negocio
- **API**: `api_tareas.php` - Endpoint REST para operaciones CRUD
- **Modelo**: Utiliza las tablas existentes de la base de datos

### Frontend
- **Vista de Tareas**: `Vista/tareas_m.php` - Vista para ver tareas y calificar entregas
- **Vista CRUD**: `Vista/tareas_crud.php` - Vista para administrar tareas
- **JavaScript**: `Vista/Js/tareas_m.js` y `Vista/Js/tareas_crud.js`

## Estructura de la Base de Datos

### Tablas Principales
- `contenido` - Almacena tareas y exámenes
- `entregas` - Almacena las entregas de los estudiantes
- `semanas_academicas` - Organiza el contenido por semanas
- `cursos` - Información de los cursos
- `estudiantes` - Información de los estudiantes

### Campos Importantes
- `contenido.tipo`: 'tarea' o 'examen'
- `contenido.fecha_limite`: Fecha límite de entrega
- `entregas.calificacion`: Puntuación otorgada
- `entregas.feedback`: Retroalimentación del maestro

## Funcionalidades Implementadas

### Para Maestros

#### 1. Vista de Tareas (`tareas_m.php`)
- **Ver tareas agrupadas por semana**
- **Buscar tareas** por título o descripción
- **Ver detalles de tarea** en modal
- **Ver entregas de estudiantes** con estadísticas
- **Calificar entregas** con retroalimentación
- **Navegar a administración** de tareas

#### 2. Vista CRUD (`tareas_crud.php`)
- **Crear nuevas tareas/exámenes**
- **Editar tareas existentes**
- **Eliminar tareas** (con confirmación)
- **Ver detalles** de tareas
- **Paginación** de resultados
- **Búsqueda** de contenido

### Operaciones Disponibles

#### Crear Tarea/Examen
```javascript
// Datos requeridos
{
    curso_id: 1,
    titulo: "Ejercicios de Fracciones",
    descripcion: "Realiza las sumas y restas...",
    tipo: "tarea", // o "examen"
    fecha_limite: "2025-03-18",
    hora_limite: "23:59",
    puntos: 10,
    semana_id: 1
}
```

#### Calificar Entrega
```javascript
{
    entrega_id: 1,
    calificacion: 8,
    feedback: "Buen trabajo, pero..."
}
```

## API Endpoints

### GET Requests
- `api_tareas.php?action=get_tareas_curso&curso_id=1` - Obtener tareas del curso
- `api_tareas.php?action=get_entregas&tarea_id=1` - Obtener entregas de una tarea
- `api_tareas.php?action=get_estudiantes_curso&curso_id=1` - Obtener estudiantes del curso
- `api_tareas.php?action=get_semanas_academicas&curso_id=1` - Obtener semanas académicas
- `api_tareas.php?action=get_estadisticas&curso_id=1` - Obtener estadísticas del curso

### POST Requests
- `api_tareas.php` - Crear/actualizar tarea
- `api_tareas.php` - Calificar entrega
- `api_tareas.php` - Eliminar tarea

## Instalación y Configuración

### 1. Requisitos
- PHP 7.4+
- MySQL 5.7+
- Servidor web (Apache/Nginx)

### 2. Configuración de Base de Datos
```sql
-- Ejecutar el archivo escuela_nv.sql para crear las tablas
-- O asegurarse de que existan las tablas: contenido, entregas, semanas_academicas
```

### 3. Configuración de Archivos
- Verificar que `config.php` tenga los datos de conexión correctos
- Asegurar que las rutas de los archivos JavaScript sean correctas

### 4. Datos de Prueba
Ejecutar el script de prueba para crear datos de ejemplo:
```bash
php test_tareas_datos.php
```

## Uso del Sistema

### Acceso a las Vistas
1. **Vista de Tareas**: `Vista/tareas_m.php?curso_id=1`
2. **Vista CRUD**: `Vista/tareas_crud.php?curso_id=1`

### Flujo de Trabajo Típico

#### 1. Crear una Tarea
1. Ir a la vista CRUD
2. Hacer clic en "Nuevo Contenido"
3. Llenar el formulario con los datos de la tarea
4. Guardar

#### 2. Ver Entregas y Calificar
1. Ir a la vista de tareas
2. Hacer clic en "Ver entregas" en la tarea deseada
3. Revisar las entregas de los estudiantes
4. Hacer clic en "Calificar" para cada entrega
5. Ingresar calificación y retroalimentación

#### 3. Administrar Tareas
1. Ir a la vista CRUD
2. Usar los botones de acción para editar/eliminar
3. Ver estadísticas de entregas

## Características Técnicas

### Seguridad
- Validación de datos en el servidor
- Sanitización de inputs
- Verificación de permisos (pendiente de implementar)

### Rendimiento
- Paginación de resultados
- Consultas optimizadas
- Carga asíncrona de datos

### UX/UI
- Diseño responsivo
- Indicadores de carga
- Mensajes de error informativos
- Estados vacíos con CTAs

## Personalización

### Estilos CSS
Los estilos se encuentran en:
- `Vista/Css/tareas_m.css` - Estilos para la vista de tareas
- `Vista/Css/tareas_crud.css` - Estilos para la vista CRUD

### Configuración de la API
Modificar `api_tareas.php` para:
- Agregar nuevos endpoints
- Cambiar la lógica de validación
- Modificar el formato de respuesta

## Troubleshooting

### Problemas Comunes

#### 1. Error de Conexión a la Base de Datos
- Verificar configuración en `config.php`
- Comprobar que el servidor MySQL esté funcionando

#### 2. Archivos JavaScript No Se Cargan
- Verificar rutas en los archivos HTML
- Comprobar permisos de archivos

#### 3. API No Responde
- Verificar que `api_tareas.php` sea accesible
- Revisar logs de errores del servidor

#### 4. Datos No Se Muestran
- Ejecutar el script de datos de prueba
- Verificar que existan registros en la base de datos

### Logs y Debugging
- Habilitar `error_reporting(E_ALL)` en desarrollo
- Revisar la consola del navegador para errores JavaScript
- Verificar la respuesta de la API con herramientas de desarrollo

## Extensiones Futuras

### Funcionalidades Pendientes
- **Sistema de archivos**: Subida y descarga de archivos
- **Notificaciones**: Alertas por correo electrónico
- **Reportes**: Generación de reportes de calificaciones
- **Plagio**: Detección de similitud entre entregas
- **Calendario**: Vista de calendario con fechas límite

### Mejoras Técnicas
- **Autenticación**: Sistema de login y permisos
- **Caché**: Implementar caché para mejorar rendimiento
- **API REST**: Completar la implementación REST
- **Tests**: Agregar pruebas unitarias y de integración

## Soporte

Para reportar problemas o solicitar nuevas funcionalidades:
1. Revisar la documentación existente
2. Verificar los logs de errores
3. Proporcionar información detallada del problema
4. Incluir pasos para reproducir el error

## Licencia

Este sistema es parte del proyecto educativo y está sujeto a las mismas condiciones de uso del proyecto principal. 