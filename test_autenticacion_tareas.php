<?php
/**
 * Script de prueba para verificar la autenticación y funcionalidad de las vistas de tareas
 * 
 * Este script simula un login de maestro y verifica que las vistas de tareas funcionen correctamente
 */

// Incluir archivos necesarios
require_once 'config.php';
require_once 'Controlador/CursoController.php';
require_once 'Controlador/TareaController.php';
require_once 'Modelo/Conexion.php';

echo "<h1>Prueba de Autenticación y Funcionalidad de Tareas</h1>\n";

// 1. Verificar conexión a la base de datos
echo "<h2>1. Verificación de conexión a la base de datos</h2>\n";
try {
    $conexion = Conexion::getConexion();
    echo "✅ Conexión a la base de datos exitosa\n";
} catch (Exception $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "\n";
    exit;
}

// 2. Buscar un maestro en la base de datos
echo "<h2>2. Buscando maestro en la base de datos</h2>\n";
$query = "SELECT u.id, u.rol, p.nombres, p.apellido_paterno 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.rol = 'maestro' 
          LIMIT 1";

$stmt = $conexion->prepare($query);
$stmt->execute();
$maestro = $stmt->fetch();

if ($maestro) {
    echo "✅ Maestro encontrado: {$maestro['nombres']} {$maestro['apellido_paterno']} (ID: {$maestro['id']})\n";
    $maestroId = $maestro['id'];
} else {
    echo "❌ No se encontró ningún maestro en la base de datos\n";
    echo "Creando maestro de prueba...\n";
    
    // Crear un maestro de prueba
    try {
        $conexion->beginTransaction();
        
        // Insertar usuario
        $query = "INSERT INTO usuarios (email, password, rol, activo) VALUES (?, ?, 'maestro', 1)";
        $stmt = $conexion->prepare($query);
        $stmt->execute(['<EMAIL>', password_hash('123456', PASSWORD_DEFAULT)]);
        $usuarioId = $conexion->lastInsertId();
        
        // Insertar persona
        $query = "INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, fecha_nacimiento, genero) 
                  VALUES (?, 'Carlos', 'García', 'López', '1980-01-01', 'M')";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$usuarioId]);
        $personaId = $conexion->lastInsertId();
        
        // Insertar maestro
        $query = "INSERT INTO maestros (persona_id, especialidad, experiencia_anos) VALUES (?, 'Matemáticas', 5)";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$personaId]);
        
        $conexion->commit();
        $maestroId = $usuarioId;
        echo "✅ Maestro de prueba creado exitosamente (ID: {$maestroId})\n";
    } catch (Exception $e) {
        $conexion->rollBack();
        echo "❌ Error al crear maestro: " . $e->getMessage() . "\n";
        exit;
    }
}

// 3. Buscar un curso del maestro
echo "<h2>3. Buscando curso del maestro</h2>\n";
$controller = new CursoController();
$cursos = $controller->obtenerCursosPorMaestro($maestroId);

if (!empty($cursos)) {
    $curso = $cursos[0];
    echo "✅ Curso encontrado: {$curso['nombre']} (ID: {$curso['id']})\n";
    $cursoId = $curso['id'];
} else {
    echo "❌ No se encontró ningún curso para el maestro\n";
    echo "Creando curso de prueba...\n";
    
    // Crear un curso de prueba
    try {
        $conexion->beginTransaction();
        
        // Insertar curso
        $query = "INSERT INTO cursos (nombre, grado, descripcion, maestro_id, activo) 
                  VALUES (?, '5', 'Curso de matemáticas de 5° grado', ?, 1)";
        $stmt = $conexion->prepare($query);
        $stmt->execute(['Matemáticas 5°', $maestroId]);
        $cursoId = $conexion->lastInsertId();
        
        // Insertar horario
        $query = "INSERT INTO horarios_cursos (curso_id, dia_semana, hora_inicio, hora_fin) 
                  VALUES (?, 'lunes', '08:00:00', '09:30:00')";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$cursoId]);
        
        $conexion->commit();
        echo "✅ Curso de prueba creado exitosamente (ID: {$cursoId})\n";
    } catch (Exception $e) {
        $conexion->rollBack();
        echo "❌ Error al crear curso: " . $e->getMessage() . "\n";
        exit;
    }
}

// 4. Verificar que el curso pertenece al maestro
echo "<h2>4. Verificando propiedad del curso</h2>\n";
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if ($curso) {
    echo "✅ El curso pertenece al maestro\n";
} else {
    echo "❌ El curso no pertenece al maestro\n";
    exit;
}

// 5. Probar el controlador de tareas
echo "<h2>5. Probando controlador de tareas</h2>\n";
$tareaController = new TareaController();

// Obtener tareas del curso
$tareas = $tareaController->obtenerTareasPorCurso($cursoId);
echo "📊 Tareas encontradas: " . count($tareas) . "\n";

// Obtener semanas académicas
$semanas = $tareaController->obtenerSemanasAcademicas($cursoId);
echo "📅 Semanas académicas encontradas: " . count($semanas) . "\n";

// 6. Crear datos de prueba si no existen
echo "<h2>6. Creando datos de prueba</h2>\n";

if (empty($semanas)) {
    echo "Creando semana académica de prueba...\n";
    try {
        $query = "INSERT INTO semanas_academicas (curso_id, numero_semana, titulo, fecha_inicio, fecha_fin, descripcion, activo) 
                  VALUES (?, 1, 'Semana 1: Introducción a las fracciones', '2025-01-06', '2025-01-10', 'Primera semana del curso', 1)";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$cursoId]);
        $semanaId = $conexion->lastInsertId();
        echo "✅ Semana académica creada (ID: {$semanaId})\n";
    } catch (Exception $e) {
        echo "❌ Error al crear semana: " . $e->getMessage() . "\n";
    }
}

if (empty($tareas)) {
    echo "Creando tarea de prueba...\n";
    try {
        // Obtener la primera sesión o crear una
        $query = "SELECT id FROM sesiones WHERE curso_id = ? LIMIT 1";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$cursoId]);
        $sesion = $stmt->fetch();
        
        if (!$sesion) {
            // Crear sesión
            $query = "INSERT INTO sesiones (curso_id, semana_id, orden, titulo, descripcion, activo) 
                      VALUES (?, ?, 1, 'Sesión 1', 'Primera sesión del curso', 1)";
            $stmt = $conexion->prepare($query);
            $stmt->execute([$cursoId, $semanaId ?? 1]);
            $sesionId = $conexion->lastInsertId();
        } else {
            $sesionId = $sesion['id'];
        }
        
        // Crear tarea
        $query = "INSERT INTO contenido (sesion_id, titulo, descripcion, tipo, fecha_limite, hora_limite, puntos, activo) 
                  VALUES (?, 'Tarea: Ejercicios de fracciones', 'Completar los ejercicios 1-10 del libro de texto', 'tarea', '2025-01-15', '23:59:00', 10, 1)";
        $stmt = $conexion->prepare($query);
        $stmt->execute([$sesionId]);
        $tareaId = $conexion->lastInsertId();
        echo "✅ Tarea creada (ID: {$tareaId})\n";
    } catch (Exception $e) {
        echo "❌ Error al crear tarea: " . $e->getMessage() . "\n";
    }
}

// 7. Probar las URLs de las vistas
echo "<h2>7. URLs de las vistas de tareas</h2>\n";
echo "🔗 <a href='Vista/tareas_menu.php?curso_id={$cursoId}' target='_blank'>Menú de Tareas</a>\n";
echo "🔗 <a href='Vista/tareas_m.php?curso_id={$cursoId}' target='_blank'>Vista Estándar de Tareas</a>\n";
echo "🔗 <a href='Vista/tareas_crud.php?curso_id={$cursoId}' target='_blank'>Vista CRUD de Tareas</a>\n";

// 8. Probar la API
echo "<h2>8. Probando API de tareas</h2>\n";
$apiUrl = "api_tareas.php?action=get_tareas_curso&curso_id={$cursoId}";
echo "🔗 <a href='{$apiUrl}' target='_blank'>API: Obtener tareas del curso</a>\n";

$apiUrl2 = "api_tareas.php?action=get_semanas_academicas&curso_id={$cursoId}";
echo "🔗 <a href='{$apiUrl2}' target='_blank'>API: Obtener semanas académicas</a>\n";

// 9. Información de sesión simulada
echo "<h2>9. Información para simular sesión</h2>\n";
echo "Para simular una sesión de maestro, puedes usar estos datos:\n";
echo "📝 Maestro ID: {$maestroId}\n";
echo "📝 Curso ID: {$cursoId}\n";
echo "📝 Email: <EMAIL>\n";
echo "📝 Password: 123456\n";

echo "<h2>✅ Prueba completada</h2>\n";
echo "Las vistas de tareas deberían funcionar correctamente con la autenticación implementada.\n";
?> 