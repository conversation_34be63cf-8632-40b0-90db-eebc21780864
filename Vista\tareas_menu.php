<?php
session_start();

// Verificar si el usuario está logueado y es maestro
if (!isset($_SESSION['usuario_id']) || $_SESSION['rol'] !== 'maestro') {
    header('Location: intranet.php');
    exit;
}

// Verificar que se proporcione el ID del curso
if (!isset($_GET['curso_id']) || empty($_GET['curso_id'])) {
    header('Location: cursos_m.php');
    exit;
}

$cursoId = (int)$_GET['curso_id'];
$maestroId = $_SESSION['usuario_id'];

require_once '../Controlador/CursoController.php';

$controller = new CursoController();

// Obtener información del curso
$curso = $controller->obtenerCursoPorId($cursoId, $maestroId);

if (!$curso) {
    header('Location: cursos_m.php');
    exit;
}

// Obtener información del maestro
require_once '../Modelo/Conexion.php';
$conexion = Conexion::getConexion();

$query = "SELECT p.nombres, p.apellido_paterno, p.apellido_materno, p.foto_perfil 
          FROM usuarios u 
          JOIN personas p ON u.id = p.usuario_id 
          JOIN maestros m ON p.id = m.persona_id 
          WHERE u.id = ? AND u.rol = 'maestro'";

$stmt = $conexion->prepare($query);
$stmt->execute([$maestroId]);
$maestro = $stmt->fetch();

// Obtener grados para mostrar el nombre del grado
$grados = $controller->obtenerGrados();
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Menú de Tareas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/tareas_menu.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <?php if ($maestro && $maestro['foto_perfil']): ?>
                          <img src="data:image/jpeg;base64,<?php echo base64_encode($maestro['foto_perfil']); ?>" alt="Foto de perfil">
                      <?php else: ?>
                          <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($maestro ? $maestro['nombres'] . ' ' . $maestro['apellido_paterno'] : 'Maestro'); ?></h3>
                      <p>Profesor</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_m.php">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil_m.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_m.php">
                              <span class="material-icons">school</span>
                              <span>Mis Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="asistencia_m.html">
                              <span class="material-icons">fact_check</span>
                              <span>Asistencia</span>
                          </a>
                      </li>
                      <li>
                          <a href="mensajes_mp.html">
                              <span class="material-icons">chat</span>
                              <span>Mensajes</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.php">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #2196f3;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_m.php" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1><?php echo htmlspecialchars($curso['nombre']); ?></h1>
                          <p><?php echo htmlspecialchars($grados[$curso['grado']] ?? $curso['grado']); ?></p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <?php if (!empty($curso['horarios_procesados'])): ?>
                              <?php 
                              $dias = [
                                  'lunes' => 'Lunes',
                                  'martes' => 'Martes',
                                  'miercoles' => 'Miércoles',
                                  'jueves' => 'Jueves',
                                  'viernes' => 'Viernes'
                              ];
                              $contador = 0;
                              foreach ($curso['horarios_procesados'] as $dia => $horario): 
                                  if ($contador < 2): // Mostrar solo los primeros 2 horarios
                              ?>
                                  <div class="schedule-day">
                                      <span class="day-label"><?php echo $dias[$dia]; ?></span>
                                      <span class="day-time"><?php echo htmlspecialchars($horario); ?></span>
                                  </div>
                              <?php 
                                  endif;
                                  $contador++;
                              endforeach; 
                              ?>
                          <?php endif; ?>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Contenido</a>
                  <a href="tareas_menu.php?curso_id=<?php echo $cursoId; ?>" class="course-tab active">Tareas</a>
                  <a href="estudiantes_m.php?curso_id=<?php echo $cursoId; ?>" class="course-tab">Estudiantes</a>
                  <a href="calificaciones_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Calificaciones</a>
                  <a href="asistencias_estudiantes.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Asistencia</a>
                  <a href="mensajes_m.html?curso_id=<?php echo $cursoId; ?>" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Sección de opciones de tareas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Gestión de Tareas</h2>
                      <p class="section-description">Seleccione el modo de visualización para gestionar las tareas del curso</p>
                  </div>
                  
                  <div class="view-options-container">
                      <div class="view-option-card" onclick="window.location.href='tareas_m.php?curso_id=<?php echo $cursoId; ?>'">
                          <div class="view-option-icon">
                              <span class="material-icons">view_agenda</span>
                          </div>
                          <div class="view-option-content">
                              <h3>Vista Estándar</h3>
                              <p>Visualiza las tareas organizadas por semanas con detalles completos de cada tarea.</p>
                              <ul class="view-features">
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Organización por semanas</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Detalles completos de cada tarea</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Gestión de entregas</span>
                                  </li>
                              </ul>
                              <button class="view-option-btn">
                                  <span>Seleccionar</span>
                                  <span class="material-icons">arrow_forward</span>
                              </button>
                          </div>
                      </div>
                      
                      <div class="view-option-card" onclick="window.location.href='tareas_crud.php?curso_id=<?php echo $cursoId; ?>'">
                          <div class="view-option-icon crud">
                              <span class="material-icons">table_view</span>
                          </div>
                          <div class="view-option-content">
                              <h3>Vista de Tabla CRUD</h3>
                              <p>Administra todas las tareas en formato de tabla con opciones para crear, editar y eliminar rápidamente.</p>
                              <ul class="view-features">
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Formato de tabla compacto</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Creación rápida de tareas</span>
                                  </li>
                                  <li>
                                      <span class="material-icons">check_circle</span>
                                      <span>Edición y eliminación en línea</span>
                                  </li>
                              </ul>
                              <button class="view-option-btn crud">
                                  <span>Seleccionar</span>
                                  <span class="material-icons">arrow_forward</span>
                              </button>
                          </div>
                      </div>
                  </div>
              </section>
          </div>
      </main>
  </div>

  <!-- Datos para JavaScript -->
  <script>
      window.cursoData = <?php echo json_encode($curso); ?>;
      window.cursoId = <?php echo $cursoId; ?>;
      window.maestroId = <?php echo $maestroId; ?>;
  </script>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/tareas_menu.js"></script>
</body>
</html>
