document.addEventListener("DOMContentLoaded", () => {
    console.log('DOM cargado, datos disponibles:', {
        contenidoData: window.contenidoData,
        semanasData: window.semanasData,
        videoconferenciasData: window.videoconferenciasData
    }); // Debug
    
    // Elementos DOM para videoconferencia
    const meetSection = document.querySelector(".meet-section")
    const createMeetBtn = document.querySelector(".create-meet-btn")
    const meetModal = document.getElementById("meet-modal")
    const meetForm = document.getElementById("meet-form")
    const meetEditBtn = document.querySelector(".meet-action-btn.edit-btn")
    const meetDeleteBtn = document.querySelector(".meet-action-btn.delete-btn")
  
    // Elementos DOM para carpetas y contenido
    const weekHeaders = document.querySelectorAll(".week-header")
    const createFolderBtn = document.querySelector(".create-folder-btn")
    const createContentBtn = document.querySelector(".create-content-btn")
    const folderModal = document.getElementById("folder-modal")
    const contentModal = document.getElementById("content-modal")
    const folderForm = document.getElementById("folder-form")
    const contentForm = document.getElementById("content-form")
    const contentType = document.getElementById("content-type")
    const taskFields = document.getElementById("task-fields")
    const fileFields = document.getElementById("file-fields")
    const linkFields = document.getElementById("link-fields")
    const participationFields = document.getElementById("participation-fields")
    const taskLock = document.getElementById("task-lock")
    const lockDateFields = document.getElementById("lock-date-fields")
    const addContentBtns = document.querySelectorAll(".add-content-btn")
    const weekActionBtns = document.querySelectorAll(".week-action-btn")
    const contentActionBtns = document.querySelectorAll(".content-action-btn")
  
    // Elementos DOM para modales
    const deleteModal = document.getElementById("delete-modal")
    const deleteModalTitle = document.getElementById("delete-modal-title")
    const deleteModalMessage = document.getElementById("delete-modal-message")
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
  
    // Elementos DOM para vista de tarea
    const taskViewModal = document.getElementById("task-view-modal")
    const taskViewCloseBtn = document.getElementById("close-task-view-btn")
  
    // Variables para almacenar elementos a eliminar
    let elementToDelete = null
    let deleteType = ""

    // Variables para rastrear IDs actuales en modales
    let currentTaskId = ""
    let currentParticipationId = ""
    let currentDocumentId = ""
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
  
      // Actualizar la sección de videoconferencia
      updateVideoconferenceSection()
    }
  
    function setupEventListeners() {
      // Event listeners para editor de texto enriquecido
      setupRichTextEditor()

      // Event listeners para videoconferencia
      if (createMeetBtn) createMeetBtn.addEventListener("click", openCreateMeetModal)
      if (meetEditBtn) meetEditBtn.addEventListener("click", openEditMeetModal)
      if (meetDeleteBtn) meetDeleteBtn.addEventListener("click", confirmDeleteMeet)
  
      // Event listeners para carpetas y contenido
      weekHeaders.forEach((header) => {
        header.addEventListener("click", toggleWeekContent)
      })
  
      if (createFolderBtn) createFolderBtn.addEventListener("click", openCreateFolderModal)
      if (createContentBtn) createContentBtn.addEventListener("click", openCreateContentModal)

      // Event listeners para tipos de contenido
      if (contentType) contentType.addEventListener("change", toggleContentFields)
      if (taskLock) taskLock.addEventListener("change", toggleLockDateFields)
  
      // Event listeners para botones de agregar contenido
      addContentBtns.forEach((btn) => {
        btn.addEventListener("click", function () {
          const weekId = this.closest(".folder-content").id.split("-")[1]
          openCreateContentModal(weekId)
        })
      })
  
      // Event listeners para botones de acción de semana
      weekActionBtns.forEach((btn) => {
        if (btn.classList.contains("edit-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const weekId = this.dataset.id
            openEditFolderModal(weekId)
          })
        } else if (btn.classList.contains("delete-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const weekId = this.dataset.id
            confirmDeleteFolder(weekId)
          })
        }
      })
  
      // Event listeners para botones de acción de contenido
      contentActionBtns.forEach((btn) => {
        console.log('Botón encontrado:', btn.className, btn.dataset.id); // Debug
        if (btn.classList.contains("edit-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            openEditContentModal(contentId)
          })
        } else if (btn.classList.contains("delete-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            confirmDeleteContent(contentId)
          })
        } else if (btn.classList.contains("view-btn")) {
          console.log('Configurando event listener para view-btn:', btn.dataset.id); // Debug
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            const contentType = this.dataset.tipo
            console.log('Clic en view-btn, contentId:', contentId, 'tipo:', contentType); // Debug
            openContentViewModal(contentId, contentType)
          })
        } else if (btn.classList.contains("grade-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            openGradeParticipationModal(contentId)
          })
        }
      })
  
      // Event listeners para modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })

      if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", handleDelete)
      }
  
      // Event listeners para formularios
      if (meetForm) meetForm.addEventListener("submit", handleMeetFormSubmit)
      if (folderForm) folderForm.addEventListener("submit", handleFolderFormSubmit)
      if (contentForm) contentForm.addEventListener("submit", handleContentFormSubmit)
  
      // Event listeners para vista de tarea y documentos
      document.querySelectorAll(".content-details").forEach((item) => {
        item.addEventListener("click", function () {
          const contentType = this.closest(".content-item").querySelector(".content-icon")
          if (contentType.classList.contains("task") || contentType.classList.contains("exam")) {
            openTaskView(this.id)
          } else if (contentType.classList.contains("announcement")) {
            // Abrir modal de anuncio
            const announcementModal = document.getElementById("announcement-modal")
            announcementModal.classList.add("active")

            // Agregar event listener para el botón de editar si no existe
            const editAnnouncementBtn = announcementModal.querySelector(".edit-announcement-btn")
            if (editAnnouncementBtn && !editAnnouncementBtn.hasAttribute("data-listener-added")) {
              editAnnouncementBtn.addEventListener("click", function() {
                closeAllModals()
                openEditContentModal(this.closest(".content-details").id)
              })
              editAnnouncementBtn.setAttribute("data-listener-added", "true")
            }
          } else if (contentType.classList.contains("pdf") || contentType.classList.contains("ppt")) {
            // Abrir vista previa de documento
            openDocumentPreview(this.id)
          } else if (contentType.classList.contains("link")) {
            // Abrir enlace en nueva pestaña
            openLinkContent(this.id)
          } else if (contentType.classList.contains("video")) {
            // Abrir video en nueva pestaña
            openVideoContent(this.id)
          } else if (contentType.classList.contains("participation")) {
            // Abrir vista de participación
            openParticipationView(this.id)
          }
        })
      })
  
      if (taskViewCloseBtn) {
        taskViewCloseBtn.addEventListener("click", closeAllModals)
      }

      // Event listener para cerrar modal de visualización de contenido
      const viewContentModal = document.getElementById('view-content-modal');
      if (viewContentModal) {
        const viewContentCloseBtn = viewContentModal.querySelector('.modal-close-btn');
        if (viewContentCloseBtn) {
          viewContentCloseBtn.addEventListener("click", function() {
            viewContentModal.classList.remove('active');
          });
        }
      }
    }
  
    // Funciones para videoconferencia
    function updateVideoconferenceSection() {
      // Usar window.videoconferenciasData
      const videoconferencia = (window.videoconferenciasData && window.videoconferenciasData.length > 0) ? window.videoconferenciasData[0] : null;
      if (videoconferencia) {
        meetSection.classList.add("active-meet")
        meetSection.innerHTML = `
                  <div class="meet-info">
                      <div class="meet-icon">
                          <span class="material-icons">videocam</span>
                      </div>
                      <div class="meet-details">
                          <h3>${videoconferencia.titulo}</h3>
                          <p>${formatDate(videoconferencia.fecha)} - ${formatTime(videoconferencia.hora)}</p>
                          <div class="meet-actions">
                              <a href="${videoconferencia.url}" class="meet-link" target="_blank">
                                  <span class="material-icons">open_in_new</span>
                                  Unirse a la videoconferencia
                              </a>
                              <button class="meet-action-btn edit-btn" data-id="${videoconferencia.id}">
                                  <span class="material-icons">edit</span>
                              </button>
                              <button class="meet-action-btn delete-btn" data-id="${videoconferencia.id}">
                                  <span class="material-icons">delete</span>
                              </button>
                          </div>
                      </div>
                  </div>
              `
        // Después de renderizar, solo acceder a .create-meet-btn si existe
        const createMeetBtnElem = document.querySelector(".create-meet-btn");
        if (createMeetBtnElem) createMeetBtnElem.style.display = "none";
        const editBtnElem = document.querySelector(".meet-action-btn.edit-btn");
        if (editBtnElem) editBtnElem.addEventListener("click", openEditMeetModal);
        const deleteBtnElem = document.querySelector(".meet-action-btn.delete-btn");
        if (deleteBtnElem) deleteBtnElem.addEventListener("click", confirmDeleteMeet);
      } else {
        meetSection.classList.remove("active-meet")
        meetSection.innerHTML = `
                  <div class="no-meet-message">
                      <span class="material-icons">videocam_off</span>
                      <p>No hay enlaces de videoconferencia disponibles por ahora</p>
                  </div>
              `
        document.querySelector(".create-meet-btn").style.display = "flex"
        document.querySelector(".create-meet-btn").textContent = "Agregar enlace de videoconferencia"
        document.querySelector(".create-meet-btn").innerHTML =
          '<span class="material-icons">add</span>Agregar enlace de videoconferencia'
      }
    }
  
    function openCreateMeetModal() {
      meetForm.reset()
      document.getElementById("meet-id").value = ""
      document.getElementById("meet-modal-title").textContent = "Agregar Enlace de Videoconferencia"
      const now = new Date()
      document.getElementById("meet-date").value = now.toISOString().split("T")[0]
      document.getElementById("meet-time").value =
        now.getHours().toString().padStart(2, "0") + ":" + now.getMinutes().toString().padStart(2, "0")
      meetModal.classList.add("active")
    }
  
    function openEditMeetModal(e) {
      const id = e && e.target ? e.target.closest(".edit-btn").dataset.id : null;
      const videoconferencia = (window.videoconferenciasData && window.videoconferenciasData.length > 0) ? window.videoconferenciasData[0] : null;
      if (!videoconferencia) return;
      document.getElementById("meet-id").value = videoconferencia.id
      document.getElementById("meet-title").value = videoconferencia.titulo
      document.getElementById("meet-date").value = videoconferencia.fecha
      document.getElementById("meet-time").value = videoconferencia.hora
      document.getElementById("meet-url").value = videoconferencia.url
      document.getElementById("meet-modal-title").textContent = "Editar Enlace de Videoconferencia"
      meetModal.classList.add("active")
    }
  
    function confirmDeleteMeet(e) {
      const id = e && e.target ? e.target.closest(".delete-btn").dataset.id : null;
      if (!id) return;
      deleteType = "meet"
      elementToDelete = id
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Videoconferencia"
      if (deleteModalMessage) deleteModalMessage.textContent = "¿Está seguro que desea eliminar esta videoconferencia? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function handleMeetFormSubmit(e) {
      e.preventDefault()
      const id = document.getElementById("meet-id").value
      const cursoId = document.getElementById("curso-id-meet").value
      const titulo = document.getElementById("meet-title").value
      const fecha = document.getElementById("meet-date").value
      const hora = document.getElementById("meet-time").value
      const url = document.getElementById("meet-url").value
      const data = { curso_id: cursoId, titulo, fecha, hora, url }
      const method = id ? "PUT" : "POST"
      const action = id ? `actualizar_videoconferencia&id=${id}` : "crear_videoconferencia"
      fetch(`../api_contenido.php?action=${action}`, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      })
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            location.reload()
          } else {
            alert(res.error || "Error al guardar la videoconferencia")
          }
        })
    }
  
    // Funciones para carpetas y contenido
    function toggleWeekContent(e) {
      // No ejecutar si se hizo clic en un botón
      if (e.target.classList.contains("week-action-btn") || e.target.closest(".week-action-btn")) {
        return
      }
  
      const weekId = this.dataset.week
      const content = document.getElementById(`week-${weekId}-content`)
      const toggleIcon = this.querySelector(".toggle-icon")
  
      if (content.style.display === "none") {
        content.style.display = "block"
        toggleIcon.textContent = "expand_less"
      } else {
        content.style.display = "none"
        toggleIcon.textContent = "expand_more"
      }
    }
  
    function openCreateFolderModal() {
      // Limpiar formulario
      folderForm.reset()
      document.getElementById("folder-id").value = ""
      document.getElementById("folder-modal-title").textContent = "Crear Nueva Carpeta"
  
      // Abrir modal
      folderModal.classList.add("active")
    }
  
    function openEditFolderModal(weekId) {
      // Llenar formulario con datos existentes
      document.getElementById("folder-id").value = weekId

      // Obtener datos de la semana desde la base de datos
      fetch(`../api_contenido.php?action=obtener_semana&id=${weekId}`)
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            const semana = res.data
            
            // Llenar campos del formulario
            document.getElementById("folder-title").value = semana.titulo
            document.getElementById("folder-start-date").value = semana.fecha_inicio
            document.getElementById("folder-end-date").value = semana.fecha_fin
            document.getElementById("folder-description").value = semana.descripcion || ""

            // Cambiar título del modal
            document.getElementById("folder-modal-title").textContent = "Editar Carpeta"

            // Abrir modal
            folderModal.classList.add("active")
          } else {
            alert("Error al obtener los datos de la carpeta: " + (res.error || "Error desconocido"))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert("Error al obtener los datos de la carpeta")
        })
    }
  
    function confirmDeleteFolder(weekId) {
      deleteType = "folder"
      elementToDelete = weekId
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Carpeta"
      if (deleteModalMessage) deleteModalMessage.textContent =
        "¿Está seguro que desea eliminar esta carpeta y todo su contenido? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function openCreateContentModal(weekId) {
      // Limpiar formulario
      contentForm.reset()
      document.getElementById("content-id").value = ""
      document.getElementById("content-modal-title").textContent = "Crear Nuevo Contenido"
  
      // Limpiar el editor de texto enriquecido
      const descriptionField = document.getElementById("content-description")
      if (descriptionField) {
        if (descriptionField.contentEditable === "true") {
          // Es un editor de texto enriquecido
          descriptionField.innerHTML = ""
        } else {
          // Es un textarea normal
          descriptionField.value = ""
        }
      }
  
      // Limpiar campos específicos
      taskFields.style.display = "none"
      fileFields.style.display = "none"
      linkFields.style.display = "none"
      participationFields.style.display = "none"
  
      // Limpiar campos específicos de tareas/exámenes
      const taskDueDate = document.getElementById("task-due-date")
      const taskDueTime = document.getElementById("task-due-time")
      const taskPoints = document.getElementById("task-points")
      if (taskDueDate) taskDueDate.value = ""
      if (taskDueTime) taskDueTime.value = ""
      if (taskPoints) taskPoints.value = "10"
  
      // Limpiar campo de URL
      const contentUrl = document.getElementById("content-url")
      if (contentUrl) contentUrl.value = ""
  
      // Limpiar selector de tipo y sección
      const contentTypeSelect = document.getElementById("content-type")
      const contentSectionSelect = document.getElementById("content-section")
      if (contentTypeSelect) contentTypeSelect.value = ""
      if (contentSectionSelect) contentSectionSelect.value = ""
  
      // Remover input hidden de carpeta anterior si existe
      const existingHiddenInput = document.getElementById("current-folder-id")
      if (existingHiddenInput) {
        existingHiddenInput.remove()
      }
  
      // Guardar el ID de la carpeta actual (sin mostrar el selector)
      if (weekId) {
        const hiddenInput = document.createElement("input")
        hiddenInput.type = "hidden"
        hiddenInput.id = "current-folder-id"
        hiddenInput.value = weekId
        contentForm.appendChild(hiddenInput)
      }
  
      // Abrir modal
      contentModal.classList.add("active")
    }
  
    function openEditContentModal(contentId) {
      // Llenar formulario con datos existentes
      document.getElementById("content-id").value = contentId

      // Obtener datos del contenido desde la base de datos
      fetch(`../api_contenido.php?action=obtener_contenido&id=${contentId}`)
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            const contenido = res.data
            
            // Determinar tipo de contenido
            let type = contenido.tipo || ""
            
            // Llenar campos básicos
            console.log("tipo que llega:", type);
            console.log("opciones tipo:", Array.from(document.getElementById('content-type').options).map(o => o.value));
            document.getElementById("content-type").value = type
            document.getElementById("content-title").value = contenido.titulo
            // Seleccionar la sección/carpeta correcta
            console.log("semana_id que llega:", contenido.semana_id);
            console.log("opciones disponibles:", Array.from(document.getElementById('content-section').options).map(o => o.value));
            if (contenido.semana_id) {
              document.getElementById("content-section").value = contenido.semana_id;
            }
            // Usar el editor de texto enriquecido si está disponible
            const descriptionField = document.getElementById("content-description")
            if (descriptionField) {
              if (descriptionField.contentEditable === "true") {
                // Es un editor de texto enriquecido
                descriptionField.innerHTML = contenido.descripcion || ""
              } else {
                // Es un textarea normal
                descriptionField.value = contenido.descripcion || ""
              }
            }

            // Llenar campos específicos según el tipo
            fillSpecificFieldsFromData(contenido)

            // Mostrar campos específicos según el tipo
            toggleContentFields()

            // Cambiar título del modal
            document.getElementById("content-modal-title").textContent = "Editar Contenido"

            // Abrir modal
            contentModal.classList.add("active")
          } else {
            alert("Error al obtener los datos del contenido: " + (res.error || "Error desconocido"))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert("Error al obtener los datos del contenido")
        })
    }

    // Función auxiliar para llenar campos específicos con datos reales
    function fillSpecificFieldsFromData(contenido) {
      // Mostrar campos específicos según el tipo en español
      if (contenido.tipo === "tarea" || contenido.tipo === "examen") {
        // Llenar campos específicos de tarea/examen
        const dueDateField = document.getElementById("task-due-date")
        const dueTimeField = document.getElementById("task-due-time")
        const pointsField = document.getElementById("task-points")

        if (dueDateField && contenido.fecha_limite) {
          const fechaLimite = new Date(contenido.fecha_limite)
          dueDateField.value = fechaLimite.toISOString().split('T')[0]
        }

        if (dueTimeField && contenido.fecha_limite) {
          const fechaLimite = new Date(contenido.fecha_limite)
          const timeString = fechaLimite.toTimeString().slice(0, 5)
          dueTimeField.value = timeString
        }

        if (pointsField) {
          pointsField.value = contenido.puntos || "10"
        }
      } else if (contenido.tipo === "enlace" || contenido.tipo === "video") {
        // Llenar campo de URL si existe en los datos
        const urlField = document.getElementById("content-url")
        if (urlField && contenido.url) {
          urlField.value = contenido.url
        }
      }
    }
  
    function confirmDeleteContent(contentId) {
      deleteType = "content"
      elementToDelete = contentId
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Contenido"
      if (deleteModalMessage) deleteModalMessage.textContent = "¿Está seguro que desea eliminar este contenido? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function handleFolderFormSubmit(e) {
      e.preventDefault()
  
      // Obtener datos del formulario
      const id = document.getElementById("folder-id").value
      const cursoId = document.getElementById("curso-id").value
      const title = document.getElementById("folder-title").value
      const startDate = document.getElementById("folder-start-date").value
      const endDate = document.getElementById("folder-end-date").value
      const description = document.getElementById("folder-description").value
  
      const data = { 
        curso_id: cursoId, 
        titulo: title, 
        fecha_inicio: startDate, 
        fecha_fin: endDate, 
        descripcion: description 
      }
      
      const method = id ? "PUT" : "POST"
      const action = id ? `actualizar_semana&id=${id}` : "crear_semana"
      
      fetch(`../api_contenido.php?action=${action}`, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      })
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            location.reload()
          } else {
            alert(res.error || "Error al guardar la carpeta")
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert("Error al guardar la carpeta")
        })
    }
  
    function cleanDescriptionHTML(html) {
      // Quitar fragmentos y estilos de color, pero permitir etiquetas básicas
      html = html.replace(/<!--.*?-->/gs, '');
      html = html.replace(/color\s*:\s*[^;"']+;?/gi, '');
      html = html.replace(/background-color\s*:\s*[^;"']+;?/gi, '');
      return html;
    }

    function handleContentFormSubmit(e) {
      e.preventDefault()
  
      // Validar que se haya seleccionado un tipo
      const type = document.getElementById("content-type").value
      if (!type || type.trim() === '') {
          alert('Por favor, selecciona un tipo de contenido.');
          document.getElementById("content-type").focus();
          return;
      }
      
      // Validar que se haya seleccionado una sección
      const section = document.getElementById("content-section").value
      if (!section || section.trim() === '') {
          alert('Por favor, selecciona una sección.');
          document.getElementById("content-section").focus();
          return;
      }
      
      // Validar título
      const title = document.getElementById("content-title").value.trim()
      if (!title) {
          alert('Por favor, ingresa un título para el contenido.');
          document.getElementById("content-title").focus();
          return;
      }
      
      // Validar descripción
      const description = document.getElementById("content-description").innerHTML.trim()
      if (!description || description === '<br>' || description === '') {
          alert('Por favor, ingresa una descripción para el contenido.');
          document.getElementById("content-description").focus();
          return;
      }
      
      // Obtener datos del formulario
      const id = document.getElementById("content-id").value
      const cursoId = document.getElementById("curso-id-content").value
      
      // Obtener campos específicos según el tipo
      let fechaLimite = null
      let puntos = null
      let url = null
      let calificacionesParticipacion = null

      if (type === "tarea" || type === "examen") {
        const dueDate = document.getElementById("task-due-date").value
        const dueTime = document.getElementById("task-due-time").value
        if (dueDate && dueTime) {
          fechaLimite = `${dueDate} ${dueTime}:00`
        }
        puntos = document.getElementById("task-points").value || 10
      } else if (type === "enlace" || type === "video") {
        url = document.getElementById("content-url").value
      } else if (type === "participacion") {
        // Recopilar calificaciones de participación
        const gradeInputs = document.querySelectorAll('#participation-fields .participation-grade');
        calificacionesParticipacion = [];
        gradeInputs.forEach(input => {
          const studentId = input.dataset.studentId;
          const grade = parseFloat(input.value);
          if (studentId && grade >= 0 && grade <= 20) {
            calificacionesParticipacion.push({
              estudiante_id: studentId,
              calificacion: grade
            });
          }
        });
      }

      const data = {
        curso_id: cursoId,
        semana_id: section,
        tipo: type,
        titulo: title,
        descripcion: description,
        fecha_limite: fechaLimite,
        puntos: puntos,
        url: url,
        calificaciones_participacion: calificacionesParticipacion
      }
      
      const method = id ? "PUT" : "POST"
      const action = id ? `actualizar_contenido&id=${id}` : "crear_contenido"
      
      fetch(`../api_contenido.php?action=${action}`, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      })
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            location.reload()
          } else {
            alert(res.error || "Error al guardar el contenido")
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert("Error al guardar el contenido")
        })
    }
  
    function toggleContentFields() {
      const type = document.getElementById("content-type").value
      // Ocultar todos los campos específicos primero
      document.getElementById("task-fields").style.display = "none"
      document.getElementById("file-fields").style.display = "none"
      document.getElementById("link-fields").style.display = "none"
      document.getElementById("participation-fields").style.display = "none"

      if (type === "tarea" || type === "examen") {
        document.getElementById("task-fields").style.display = "block"
      } else if (type === "documento" || type === "presentacion") {
        document.getElementById("file-fields").style.display = "block"
      } else if (type === "enlace" || type === "video") {
        document.getElementById("link-fields").style.display = "block"
      } else if (type === "participacion") {
        document.getElementById("participation-fields").style.display = "block"
      }
    }
  
    function toggleLockDateFields() {
      if (taskLock.checked) {
        lockDateFields.style.display = "block"
      } else {
        lockDateFields.style.display = "none"
      }
    }
  
    // Funciones para eliminar elementos
    function handleDelete() {
      if (deleteType === "meet" && elementToDelete) {
        fetch(`../api_contenido.php?action=eliminar_videoconferencia&id=${elementToDelete}`, {
          method: "DELETE"
        })
          .then(res => res.json())
          .then(res => {
            if (res.success) {
              location.reload()
            } else {
              alert("Error al eliminar la videoconferencia: " + (res.error || "Error desconocido"))
            }
          })
          .catch(error => {
            console.error('Error:', error)
            alert("Error al eliminar la videoconferencia")
          })
      } else if (deleteType === "folder" && elementToDelete) {
        fetch(`../api_contenido.php?action=eliminar_semana&id=${elementToDelete}`, {
          method: "DELETE"
        })
          .then(res => res.json())
          .then(res => {
            if (res.success) {
              location.reload()
            } else {
              alert("Error al eliminar la carpeta: " + (res.error || "Error desconocido"))
            }
          })
          .catch(error => {
            console.error('Error:', error)
            alert("Error al eliminar la carpeta")
          })
      } else if (deleteType === "content" && elementToDelete) {
        fetch(`../api_contenido.php?action=eliminar_contenido&id=${elementToDelete}`, {
          method: "DELETE"
        })
          .then(res => res.json())
          .then(res => {
            if (res.success) {
              location.reload()
            } else {
              alert("Error al eliminar el contenido: " + (res.error || "Error desconocido"))
            }
          })
          .catch(error => {
            console.error('Error:', error)
            alert("Error al eliminar el contenido")
          })
      }
  
      // Cerrar modal
      closeAllModals()
    }
  
    // Funciones para vista de tarea
    function openTaskView(taskId) {
      // Guardar ID actual
      currentTaskId = taskId

      // Obtener datos del contenido
      const contentItem = document.getElementById(taskId).closest(".content-item")
      const contentIcon = contentItem.querySelector(".content-icon")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Si no existe el modal de vista de tarea, crearlo
      if (!document.getElementById("task-view-modal")) {
        createTaskViewModal(title, description, date)
      } else {
        // Actualizar contenido del modal existente
        updateTaskViewModal(title, description, date)
      }

      // Abrir modal
      document.getElementById("task-view-modal").classList.add("active")
    }
  
    // Crear modal de vista de tarea con datos dinámicos
    function createTaskViewModal(title, description, date) {
      const modal = document.createElement("div")
      modal.id = "task-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content assignment-container">
              <div class="assignment-header">
                  <h1 class="assignment-title">
                      <span class="material-icons">assignment</span>
                      ${title}
                  </h1>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>

              <div class="assignment-content">
                  <div class="assignment-section">
                      <h2 class="assignment-section-title">Descripción de la tarea</h2>
                      <p class="assignment-description">${getFullDescription(title, description)}</p>
                      <img src="unnie.png" alt="Ejercicios de sumas y restas" class="assignment-image">
                  </div>

                  <div class="assignment-section">
                      <div class="assignment-meta">
                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">event</span>
                                  Fecha de entrega
                              </div>
                              <div class="meta-value">${date.replace('Fecha límite: ', '')}, 12:00 AM</div>
                          </div>

                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">grade</span>
                                  Puntos
                              </div>
                              <div class="meta-value">10 puntos máximos</div>
                          </div>

                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">person</span>
                                  Asignado por
                              </div>
                              <div class="meta-value">Prof. Carlos García</div>
                          </div>
                      </div>
                  </div>

                  <div class="assignment-section">
                      <div class="submission-section">
                          <h2 class="submission-title">Estado de la tarea</h2>
                          <div class="task-status-info">
                              <div class="status-item">
                                  <span class="material-icons status-icon">schedule</span>
                                  <div class="status-content">
                                      <span class="status-label">Estado actual</span>
                                      <span class="status-value">Activa - Los estudiantes pueden entregar hasta el ${date.replace('Fecha límite: ', '')}</span>
                                  </div>
                              </div>
                          </div>
                          <div class="submission-options">
                              <div class="form-actions">
                                  <button type="button" class="btn-secondary" id="close-task-view-btn">Cerrar</button>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      document.getElementById("close-task-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de tarea con nuevos datos
    function updateTaskViewModal(title, description, date) {
      const modal = document.getElementById("task-view-modal")
      const titleElement = modal.querySelector(".assignment-title")
      const descriptionElement = modal.querySelector(".assignment-description")
      const dateElement = modal.querySelector(".meta-value")
      const statusElement = modal.querySelector(".status-value")

      titleElement.innerHTML = `<span class="material-icons">assignment</span>${title}`
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.textContent = `${date.replace('Fecha límite: ', '')}, 12:00 AM`
      if (statusElement) {
        statusElement.textContent = `Activa - Los estudiantes pueden entregar hasta el ${date.replace('Fecha límite: ', '')}`
      }
    }

    // Función para abrir vista previa de documentos
    function openDocumentPreview(documentId) {
      // Guardar ID actual
      currentDocumentId = documentId

      // Obtener datos del contenido
      const contentItem = document.getElementById(documentId).closest(".content-item")
      const contentIcon = contentItem.querySelector(".content-icon")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Actualizar contenido del modal
      const documentModal = document.getElementById("document-preview-modal")
      const documentTitle = document.getElementById("document-title")
      const documentDescription = documentModal.querySelector(".document-description")
      const documentDate = documentModal.querySelector(".document-date")
      const documentType = documentModal.querySelector(".document-type")
      const documentIcon = documentModal.querySelector(".document-placeholder .material-icons")
      const documentFilename = documentModal.querySelector(".document-filename")

      if (documentTitle) documentTitle.textContent = title
      if (documentDescription) documentDescription.textContent = getFullDescription(title, description)
      if (documentDate) documentDate.textContent = date

      // Determinar tipo de documento, icono y nombre de archivo
      if (contentIcon.classList.contains("pdf")) {
        if (documentType) documentType.textContent = "Documento PDF"
        if (documentIcon) documentIcon.textContent = "picture_as_pdf"
        if (documentFilename) documentFilename.textContent = "guia_ejercicios_fracciones.pdf"
      } else if (contentIcon.classList.contains("ppt")) {
        if (documentType) documentType.textContent = "Presentación PowerPoint"
        if (documentIcon) documentIcon.textContent = "slideshow"
        if (documentFilename) documentFilename.textContent = "presentacion_fracciones.pptx"
      }

      if (documentModal) {
        documentModal.classList.add("active")

        // Agregar event listeners para los botones si no existen
        const editBtn = document.getElementById("edit-document-btn")
        const downloadBtn = document.getElementById("download-document-btn")

        if (editBtn && !editBtn.hasAttribute("data-listener-added")) {
          editBtn.addEventListener("click", function() {
            closeAllModals()
            openEditContentModal(currentDocumentId)
          })
          editBtn.setAttribute("data-listener-added", "true")
        }

        if (downloadBtn && !downloadBtn.hasAttribute("data-listener-added")) {
          downloadBtn.addEventListener("click", function() {
            // Simular descarga
            alert("Descargando documento...")
          })
          downloadBtn.setAttribute("data-listener-added", "true")
        }
      }
    }

    // Función para abrir contenido de tipo enlace
    function openLinkContent(linkId) {
      // Obtener datos del contenido
      const contentItem = document.getElementById(linkId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Determinar URL basada en el título (en un caso real vendría de la base de datos)
      let url = ""
      if (title.toLowerCase().includes("video") || title.toLowerCase().includes("fracciones")) {
        url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // URL de ejemplo
      } else {
        url = "https://www.google.com" // URL por defecto
      }

      // Si no existe el modal de vista de enlace, crearlo
      if (!document.getElementById("link-view-modal")) {
        createLinkViewModal(title, description, date, url)
      } else {
        // Actualizar contenido del modal existente
        updateLinkViewModal(title, description, date, url)
      }

      // Abrir modal
      document.getElementById("link-view-modal").classList.add("active")
    }

    // Función para abrir contenido de tipo video
    function openVideoContent(videoId) {
      // Obtener datos del contenido
      const contentItem = document.getElementById(videoId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Determinar URL del video basada en el título (en un caso real vendría de la base de datos)
      let url = ""
      if (title.toLowerCase().includes("tutorial") || title.toLowerCase().includes("fracciones")) {
        url = "https://www.youtube.com/watch?v=3MRlBKgVqp8" // URL de ejemplo para tutorial de fracciones
      } else {
        url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // URL por defecto
      }

      // Si no existe el modal de vista de video, crearlo
      if (!document.getElementById("video-view-modal")) {
        createVideoViewModal(title, description, date, url)
      } else {
        // Actualizar contenido del modal existente
        updateVideoViewModal(title, description, date, url)
      }

      // Abrir modal
      document.getElementById("video-view-modal").classList.add("active")
    }

    // Función para abrir vista de participación
    function openParticipationView(participationId) {
      // Guardar ID actual
      currentParticipationId = participationId

      // Obtener datos del contenido
      const contentItem = document.getElementById(participationId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Si no existe el modal de vista de participación, crearlo
      if (!document.getElementById("participation-view-modal")) {
        createParticipationViewModal(title, description, date)
      } else {
        // Actualizar contenido del modal existente
        updateParticipationViewModal(title, description, date)
      }

      // Abrir modal
      document.getElementById("participation-view-modal").classList.add("active")
    }

    // Crear modal de vista de participación con datos dinámicos
    function createParticipationViewModal(title, description, date) {
      const modal = document.createElement("div")
      modal.id = "participation-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content modal-large">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="participation-container">
                      <div class="participation-info">
                          <p><strong>Descripción:</strong></p>
                          <p>${getFullDescription(title, description)}</p>
                          <p><strong>${date}</strong></p>
                      </div>

                      <div class="participation-grades">
                          <h3>Calificaciones de Participación</h3>
                          <div class="students-list">
                              <div class="student-item">
                                  <span class="student-name">Ana García López</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="18" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">Carlos Mendoza Silva</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="16" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">María Rodríguez Torres</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="19" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">José Fernández Cruz</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="15" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">Lucía Vargas Morales</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="17" readonly>
                              </div>
                          </div>

                          <div class="participation-actions">
                              <button type="button" class="btn-secondary" id="close-participation-view-btn">Cerrar</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listener al botón de cerrar
      document.getElementById("close-participation-view-btn").addEventListener("click", closeAllModals)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      document.getElementById("close-participation-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de participación con nuevos datos
    function updateParticipationViewModal(title, description, date) {
      const modal = document.getElementById("participation-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".participation-info p:nth-child(2)")
      const dateElement = modal.querySelector(".participation-info p:last-child")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
    }


  
    // Crear modal de vista de enlace con datos dinámicos
    function createLinkViewModal(title, description, date, url) {
      const modal = document.createElement("div")
      modal.id = "link-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="content-details-view">
                      <div class="content-icon-large">
                          <span class="material-icons">link</span>
                      </div>
                      <div class="content-info">
                          <p class="content-description">${description}</p>
                          <p class="content-date"><strong>${date}</strong></p>
                          <div class="content-url">
                              <p><strong>Enlace:</strong> <a href="${url}" target="_blank">${url}</a></p>
                          </div>
                      </div>
                  </div>
                  <div class="form-actions">
                      <button type="button" class="btn-secondary close-link-view-btn">Cerrar</button>
                      <button type="button" class="btn-primary" onclick="window.open('${url}', '_blank')">
                          <span class="material-icons">open_in_new</span>
                          Abrir enlace
                      </button>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      modal.querySelector(".close-link-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de enlace con nuevos datos
    function updateLinkViewModal(title, description, date, url) {
      const modal = document.getElementById("link-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".content-description")
      const dateElement = modal.querySelector(".content-date")
      const urlElement = modal.querySelector(".content-url a")
      const openButton = modal.querySelector(".btn-primary")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
      urlElement.textContent = url
      urlElement.href = url
      openButton.onclick = () => window.open(url, '_blank')
    }

    // Crear modal de vista de video con datos dinámicos
    function createVideoViewModal(title, description, date, url) {
      const modal = document.createElement("div")
      modal.id = "video-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="content-details-view">
                      <div class="content-icon-large">
                          <span class="material-icons">play_circle</span>
                      </div>
                      <div class="content-info">
                          <p class="content-description">${description}</p>
                          <p class="content-date"><strong>${date}</strong></p>
                          <div class="video-preview">
                              <div class="video-thumbnail">
                                  <span class="material-icons play-icon">play_circle_filled</span>
                                  <p>Haz clic para reproducir el video</p>
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="form-actions">
                      <button type="button" class="btn-secondary close-video-view-btn">Cerrar</button>
                      <button type="button" class="btn-primary" onclick="window.open('${url}', '_blank')">
                          <span class="material-icons">play_arrow</span>
                          Reproducir video
                      </button>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      modal.querySelector(".close-video-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de video con nuevos datos
    function updateVideoViewModal(title, description, date, url) {
      const modal = document.getElementById("video-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".content-description")
      const dateElement = modal.querySelector(".content-date")
      const openButton = modal.querySelector(".btn-primary")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
      openButton.onclick = () => window.open(url, '_blank')
    }

    // Función para obtener descripción completa basada en el título
    function getFullDescription(title, shortDescription) {
      // Mapeo de descripciones completas basadas en el título
      const fullDescriptions = {
        "Tarea: Ejercicios de Fracciones": "Realiza las sumas y restas de la pág. 19 - 20 del libro de texto. Incluye ejercicios de aplicación práctica donde debes resolver problemas de la vida cotidiana usando fracciones. Muestra todos los pasos de tu trabajo y simplifica las respuestas cuando sea posible. Tiempo estimado: 45 minutos.",
        "Tarea: Multiplicación de fracciones": "Resuelve los ejercicios de multiplicación de fracciones de las páginas 25-27 del libro de texto. Incluye problemas de aplicación práctica donde debes multiplicar fracciones para resolver situaciones de la vida real como recetas de cocina, medidas de construcción y distribución de recursos. Muestra todos los pasos de tu trabajo y simplifica las respuestas cuando sea posible.",
        "Presentación: Introducción a las fracciones": "Presentación interactiva de 25 diapositivas que cubre conceptos básicos de fracciones, representación gráfica con ejemplos visuales, ejercicios prácticos y actividades de comprensión. Incluye animaciones y elementos multimedia para facilitar el aprendizaje.",
        "Documento: Guía de ejercicios": "Guía completa de 15 páginas con ejercicios prácticos sobre fracciones organizados por nivel de dificultad. Incluye problemas de suma, resta, multiplicación y división de fracciones, con ejemplos resueltos paso a paso y espacios para que los estudiantes practiquen. Formato PDF descargable e imprimible.",
        "Enlace: Video explicativo sobre fracciones": "Video educativo de 12 minutos que explica de manera visual y didáctica los conceptos fundamentales de las fracciones. Incluye ejemplos con objetos cotidianos, representaciones gráficas coloridas y ejercicios interactivos. Ideal como material de apoyo para reforzar lo aprendido en clase y para estudiantes que necesiten repasar los conceptos básicos.",
        "Video: Tutorial interactivo de fracciones": "Video tutorial de 18 minutos con ejercicios interactivos y animaciones 3D que enseña los conceptos básicos de fracciones de manera dinámica. Incluye subtítulos, controles de velocidad de reproducción y marcadores de capítulos para facilitar la navegación. Los estudiantes pueden pausar en cualquier momento para practicar con los ejercicios integrados.",
        "Participación: Discusión sobre fracciones en la vida cotidiana": "Actividad de participación oral donde los estudiantes comparten ejemplos de fracciones que encuentran en su vida diaria. Se evalúa la participación activa, la creatividad de los ejemplos y la comprensión de los conceptos. Los estudiantes pueden ganar puntos adicionales por ayudar a sus compañeros y hacer preguntas reflexivas.",
        "Anuncio: Bienvenidos al curso de matemáticas": "¡Bienvenidos al curso de matemáticas de 5° grado! En este curso aprenderemos sobre fracciones, decimales, geometría y muchos temas más. Es importante que revisen el material de clase regularmente y completen las tareas asignadas. Objetivos del curso: Comprender y aplicar operaciones con fracciones, resolver problemas matemáticos de la vida cotidiana, desarrollar habilidades de razonamiento lógico y aprender conceptos básicos de geometría."
      }

      return fullDescriptions[title] || shortDescription
    }

    // Función para abrir modal de visualización de contenido
    function openContentViewModal(contentId, contentType) {
      console.log('openContentViewModal llamado con contentId:', contentId, 'tipo:', contentType); // Debug
      const contenido = window.contenidoData.find(item => item.id == contentId);
      console.log('Contenido encontrado:', contenido); // Debug
      if (contenido) {
        document.getElementById('view-content-title').textContent = contenido.titulo;
        let meta = '';
        if (contenido.tipo === 'tarea' || contenido.tipo === 'examen') {
          meta = `Fecha límite: ${contenido.fecha_limite ? new Date(contenido.fecha_limite).toLocaleDateString() : ''}`;
        } else {
          meta = `Publicado: ${contenido.created_at ? new Date(contenido.created_at).toLocaleDateString() : ''}`;
        }
        document.getElementById('view-content-meta').textContent = meta;
        document.getElementById('view-content-description').innerHTML = contenido.descripcion;

        // Mostrar sección de calificaciones si es participación
        const participationSection = document.getElementById('participation-grades-section');
        if (contenido.tipo === 'participacion') {
          participationSection.style.display = 'block';
          loadParticipationGrades(contentId);
        } else {
          participationSection.style.display = 'none';
        }

        document.getElementById('view-content-modal').classList.add('active');
        console.log('Modal abierto correctamente'); // Debug
      } else {
        console.log('No se encontró contenido con ID:', contentId); // Debug
      }
    }

    // Funciones auxiliares
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
      
      // Limpiar formularios cuando se cierran los modales
      // Limpiar formulario de contenido si está abierto
      const contentModal = document.getElementById("content-modal")
      if (contentModal && contentModal.classList.contains("active")) {
        const contentForm = document.getElementById("content-form")
        if (contentForm) {
          contentForm.reset()
          
          // Limpiar editor de texto enriquecido
          const descriptionField = document.getElementById("content-description")
          if (descriptionField) {
            if (descriptionField.contentEditable === "true") {
              descriptionField.innerHTML = ""
            } else {
              descriptionField.value = ""
            }
          }
          
          // Limpiar campos específicos
          const taskDueDate = document.getElementById("task-due-date")
          const taskDueTime = document.getElementById("task-due-time")
          const taskPoints = document.getElementById("task-points")
          const contentUrl = document.getElementById("content-url")
          const contentTypeSelect = document.getElementById("content-type")
          const contentSectionSelect = document.getElementById("content-section")
          
          if (taskDueDate) taskDueDate.value = ""
          if (taskDueTime) taskDueTime.value = ""
          if (taskPoints) taskPoints.value = "10"
          if (contentUrl) contentUrl.value = ""
          if (contentTypeSelect) contentTypeSelect.value = ""
          if (contentSectionSelect) contentSectionSelect.value = ""
          
          // Ocultar campos específicos
          const taskFields = document.getElementById("task-fields")
          const fileFields = document.getElementById("file-fields")
          const linkFields = document.getElementById("link-fields")
          const participationFields = document.getElementById("participation-fields")
          
          if (taskFields) taskFields.style.display = "none"
          if (fileFields) fileFields.style.display = "none"
          if (linkFields) linkFields.style.display = "none"
          if (participationFields) participationFields.style.display = "none"
        }
      }
      
      // Limpiar formulario de carpeta si está abierto
      const folderModal = document.getElementById("folder-modal")
      if (folderModal && folderModal.classList.contains("active")) {
        const folderForm = document.getElementById("folder-form")
        if (folderForm) {
          folderForm.reset()
        }
      }
      
      // Limpiar formulario de videoconferencia si está abierto
      const meetModal = document.getElementById("meet-modal")
      if (meetModal && meetModal.classList.contains("active")) {
        const meetForm = document.getElementById("meet-form")
        if (meetForm) {
          meetForm.reset()
        }
      }
    }
  
    // Funciones auxiliares para obtener IDs actuales
    function getCurrentTaskId() {
      return currentTaskId
    }

    function getCurrentParticipationId() {
      return currentParticipationId
    }

    function getCurrentDocumentId() {
      return currentDocumentId
    }

    function formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString("es-ES", { day: "2-digit", month: "2-digit", year: "numeric" })
    }
  
    function formatDateForInput(dateString) {
      // Convertir formato DD/MM/YYYY a YYYY-MM-DD
      const parts = dateString.split("/")
      return `${parts[2]}-${parts[1]}-${parts[0]}`
    }
  
    function formatTime(timeString) {
      // Convertir formato 24h a 12h
      const [hours, minutes] = timeString.split(":")
      const hour = Number.parseInt(hours)
      const ampm = hour >= 12 ? "PM" : "AM"
      const hour12 = hour % 12 || 12
      return `${hour12}:${minutes} ${ampm}`
    }

    // Configurar editor de texto enriquecido
    function setupRichTextEditor() {
      const editor = document.getElementById("content-description");
      if (!editor) return;

      // Configurar event listeners para los botones de la barra de herramientas
      const toolbarButtons = document.querySelectorAll('.toolbar-btn');
      
      toolbarButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          const command = this.getAttribute('data-command');
          
          if (command === 'createLink') {
            const url = prompt('Ingresa la URL del enlace:');
            if (url) {
              document.execCommand(command, false, url);
            }
          } else {
            document.execCommand(command, false, null);
          }
          
          // Mantener el foco en el editor
          editor.focus();
          
          // Actualizar estado de los botones
          updateToolbarState();
        });
      });

      // Event listener para botón de imagen
      const imageButton = document.getElementById('insert-image-btn');
      const imageInput = document.getElementById('image-upload');
      
      if (imageButton && imageInput) {
        imageButton.addEventListener('click', function(e) {
          e.preventDefault();
          imageInput.click();
        });
        
        imageInput.addEventListener('change', function(e) {
          const file = e.target.files[0];
          if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
              const img = `<img src="${e.target.result}" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
              document.execCommand('insertHTML', false, img);
              editor.focus();
            };
            reader.readAsDataURL(file);
          }
        });
      }

      // Event listeners para actualizar estado de botones
      editor.addEventListener('mouseup', updateToolbarState);
      editor.addEventListener('keyup', updateToolbarState);
      editor.addEventListener('focus', updateToolbarState);
    }

    function updateToolbarState() {
      const toolbarBtns = document.querySelectorAll('.toolbar-btn')

      toolbarBtns.forEach(btn => {
        const command = btn.getAttribute('data-command')
        try {
          if (command && document.queryCommandState && document.queryCommandState(command)) {
            btn.classList.add('active')
          } else {
            btn.classList.remove('active')
          }
        } catch (e) {
          // Comando no soportado, simplemente remover active
          btn.classList.remove('active')
        }
      })
    }
  })

  // Función global para descargar archivos
  function downloadFile(filename) {
    console.log("Descargando archivo:", filename)

    // Crear un enlace temporal para la descarga
    const link = document.createElement('a')
    link.href = `./archivos/${filename}`
    link.download = filename
    link.style.display = 'none'

    // Agregar al DOM, hacer clic y remover
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Mostrar mensaje de confirmación
    showDownloadMessage(filename)
  }

  // Función para mostrar mensajes de descarga
  function showDownloadMessage(filename) {
    // Crear elemento de notificación
    const notification = document.createElement('div')
    notification.className = 'download-notification'
    notification.innerHTML = `
      <div class="notification-content">
        <span class="material-icons">download_done</span>
        <span>Descargando: ${filename}</span>
      </div>
    `

    // Agregar estilos
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      animation: slideIn 0.3s ease-out;
    `

    // Agregar animación CSS si no existe
    if (!document.querySelector('#download-animation-styles')) {
      const animationStyles = document.createElement('style')
      animationStyles.id = 'download-animation-styles'
      animationStyles.textContent = `
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes slideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
      `
      document.head.appendChild(animationStyles)
    }

    // Mostrar notificación
    document.body.appendChild(notification)

    // Remover después de 3 segundos
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-out'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // ================================
  // Funcionalidad de Calificaciones
  // ================================

  // Elementos para calificaciones
  const gradeInputs = document.querySelectorAll('.grade-input');
  const saveGradeBtns = document.querySelectorAll('.btn-save-grade');
  const exportGradesBtn = document.querySelector('.export-grades-btn');

  // Calcular promedio final automáticamente
  function calculateFinalGrade(row) {
    const inputs = row.querySelectorAll('.grade-input');
    const tareas = parseFloat(inputs[0].value) || 0;
    const examenes = parseFloat(inputs[1].value) || 0;
    const participacion = parseFloat(inputs[2].value) || 0;

    // Promedio simple de las tres notas
    const promedio = (tareas + examenes + participacion) / 3;

    const gradeDisplay = row.querySelector('.grade-display');
    if (promedio > 0) {
      gradeDisplay.textContent = promedio.toFixed(1);

      // Aplicar clases según el promedio
      gradeDisplay.className = 'grade-display';
      if (promedio >= 16) {
        gradeDisplay.classList.add('excellent');
      } else if (promedio >= 11) {
        gradeDisplay.classList.add('good');
      } else if (promedio > 0) {
        gradeDisplay.classList.add('poor');
      }
    } else {
      gradeDisplay.textContent = '--';
      gradeDisplay.className = 'grade-display';
    }
  }

  // Event listeners para inputs de calificaciones
  gradeInputs.forEach(input => {
    input.addEventListener('input', function() {
      const row = this.closest('tr');
      calculateFinalGrade(row);
    });

    // Validar rango de notas
    input.addEventListener('blur', function() {
      const value = parseFloat(this.value);
      if (value < 0) this.value = 0;
      if (value > 20) this.value = 20;
    });
  });

  // Guardar calificaciones
  saveGradeBtns.forEach(btn => {
    btn.addEventListener('click', async function() {
      const row = this.closest('tr');
      const studentId = row.dataset.studentId;
      const inputs = row.querySelectorAll('.grade-input');

      const grades = {
        estudiante_id: studentId,
        curso_id: window.cursoId,
        tareas: parseFloat(inputs[0].value) || 0,
        examenes: parseFloat(inputs[1].value) || 0,
        participacion: parseFloat(inputs[2].value) || 0
      };

      try {
        // Llamada a la API para guardar calificaciones
        const response = await fetch(`../api_calificaciones.php?action=guardar&curso_id=${window.cursoId}`, {
          method: 'POST',
          credentials: 'same-origin',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          },
          body: JSON.stringify(grades)
        });

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Error al guardar');
        }

        // Mostrar éxito
        this.style.background = '#4caf50';
        this.innerHTML = '<span class="material-icons">check</span>';

        setTimeout(() => {
          this.style.background = '';
          this.innerHTML = '<span class="material-icons">save</span>';
        }, 2000);

        console.log('Calificaciones guardadas exitosamente');

      } catch (error) {
        console.error('Error guardando calificaciones:', error);
        alert('Error al guardar las calificaciones: ' + error.message);
      }
    });
  });

  // Exportar calificaciones
  if (exportGradesBtn) {
    exportGradesBtn.addEventListener('click', function() {
      const rows = document.querySelectorAll('.grades-table tbody tr');
      const data = [];

      rows.forEach(row => {
        const studentName = row.querySelector('.student-name').textContent;
        const inputs = row.querySelectorAll('.grade-input');
        const finalGrade = row.querySelector('.grade-display').textContent;

        data.push({
          estudiante: studentName,
          tareas: inputs[0].value || '0',
          examenes: inputs[1].value || '0',
          participacion: inputs[2].value || '0',
          promedio: finalGrade
        });
      });

      // Crear CSV
      const csvContent = [
        ['Estudiante', 'Tareas', 'Exámenes', 'Participación', 'Promedio Final'],
        ...data.map(row => [row.estudiante, row.tareas, row.examenes, row.participacion, row.promedio])
      ].map(row => row.join(',')).join('\n');

      // Descargar archivo
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `calificaciones_curso_${window.cursoId}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    });
  }

  // Cargar calificaciones existentes (si las hay)
  async function loadExistingGrades() {
    try {
      const response = await fetch(`../api_calificaciones.php?action=obtener&curso_id=${window.cursoId}`, {
        credentials: 'same-origin',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        console.log('No hay calificaciones previas o error:', data.error);
        return;
      }

      // Llenar los campos con las calificaciones existentes
      data.data.forEach(calificacion => {
        const row = document.querySelector(`tr[data-student-id="${calificacion.estudiante_id}"]`);
        if (row) {
          const inputs = row.querySelectorAll('.grade-input');
          inputs[0].value = calificacion.promedio_tareas || '';
          inputs[1].value = calificacion.promedio_examenes || '';
          inputs[2].value = calificacion.promedio_participacion || '';

          // Recalcular promedio final
          calculateFinalGrade(row);
        }
      });

      console.log('Calificaciones cargadas exitosamente');
    } catch (error) {
      console.error('Error cargando calificaciones:', error);
    }
  }

  // ================================
  // Funcionalidad de Participación
  // ================================

  // Función para cargar calificaciones de participación en el modal de vista
  async function loadParticipationGrades(contentId) {
    const participationList = document.getElementById('participation-students-list');
    if (!participationList) return;

    try {
      // Cargar calificaciones existentes desde el servidor
      const response = await fetch(`../Controlador/obtener_calificaciones_participacion.php?contenido_id=${contentId}`);
      const result = await response.json();

      const estudiantes = window.estudiantesInscritos || [];

      if (estudiantes.length === 0) {
        participationList.innerHTML = '<p style="text-align: center; color: #666;">No hay estudiantes inscritos en este curso.</p>';
        return;
      }

      // Crear un mapa de calificaciones por estudiante
      const calificacionesMap = {};
      if (result.success && result.data.calificaciones) {
        result.data.calificaciones.forEach(cal => {
          calificacionesMap[cal.estudiante_id] = cal.calificacion;
        });
      }

      participationList.innerHTML = estudiantes.map(estudiante => {
        const calificacion = calificacionesMap[estudiante.id];
        let gradeDisplay = 'Sin calificar';
        let gradeClass = '';

        if (calificacion !== undefined) {
          gradeDisplay = `${calificacion}/20`;
          if (calificacion >= 16) gradeClass = 'excellent';
          else if (calificacion >= 11) gradeClass = 'good';
          else gradeClass = 'poor';
        }

        return `
          <div class="participation-student-item">
            <div class="participation-student-info">
              <div class="participation-student-avatar">
                <img src="${estudiante.avatar}" alt="${estudiante.nombre}">
              </div>
              <div class="participation-student-details">
                <h5>${estudiante.nombre}</h5>
                <p>${estudiante.grado}</p>
              </div>
            </div>
            <div class="participation-grade">
              <span class="participation-grade-display ${gradeClass}" id="grade-display-${estudiante.id}">${gradeDisplay}</span>
            </div>
          </div>
        `;
      }).join('');

    } catch (error) {
      console.error('Error cargando calificaciones de participación:', error);
      participationList.innerHTML = '<p style="text-align: center; color: #f44336;">Error cargando calificaciones.</p>';
    }
  }

  // Función para abrir modal de calificación de participación
  function openGradeParticipationModal(contentId) {
    const contenido = window.contenidoData.find(item => item.id == contentId);
    if (!contenido) return;

    // Configurar título del modal
    document.getElementById('grade-modal-title').textContent = `Calificar: ${contenido.titulo}`;

    // Mostrar información del contenido
    const contentInfo = document.getElementById('grade-content-info');
    contentInfo.innerHTML = `
      <h5>${contenido.titulo}</h5>
      <p>${contenido.descripcion.replace(/<[^>]*>/g, '').substring(0, 200)}...</p>
    `;

    // Cargar estudiantes para calificar
    loadStudentsForGrading(contentId);

    // Mostrar modal
    document.getElementById('grade-participation-modal').classList.add('active');
  }

  // Función para cargar estudiantes en el modal de calificación
  async function loadStudentsForGrading(contentId) {
    const studentsList = document.getElementById('students-grading-list');
    if (!studentsList) return;

    try {
      // Cargar calificaciones existentes
      const response = await fetch(`../Controlador/obtener_calificaciones_participacion.php?contenido_id=${contentId}`);
      const result = await response.json();

      const estudiantes = window.estudiantesInscritos || [];

      if (estudiantes.length === 0) {
        studentsList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No hay estudiantes inscritos en este curso.</p>';
        return;
      }

      // Crear un mapa de calificaciones por estudiante
      const calificacionesMap = {};
      if (result.success && result.data.calificaciones) {
        result.data.calificaciones.forEach(cal => {
          calificacionesMap[cal.estudiante_id] = cal.calificacion;
        });
      }

      studentsList.innerHTML = estudiantes.map(estudiante => {
        const calificacionExistente = calificacionesMap[estudiante.id] || '';

        return `
          <div class="student-grading-item" data-student-id="${estudiante.id}">
            <div class="student-grading-info">
              <div class="student-grading-avatar">
                <img src="${estudiante.avatar}" alt="${estudiante.nombre}">
              </div>
              <div class="student-grading-details">
                <h5>${estudiante.nombre}</h5>
                <p>${estudiante.grado}</p>
              </div>
            </div>
            <div class="student-grading-input">
              <input type="number"
                     class="participation-grade-input"
                     data-student-id="${estudiante.id}"
                     data-content-id="${contentId}"
                     min="0" max="20" step="0.1"
                     value="${calificacionExistente}"
                     placeholder="0.0">
              <button class="save-individual-grade"
                      data-student-id="${estudiante.id}"
                      data-content-id="${contentId}">
                Guardar
              </button>
            </div>
          </div>
        `;
      }).join('');

      // Agregar event listeners a los botones de guardar individual
      studentsList.querySelectorAll('.save-individual-grade').forEach(btn => {
        btn.addEventListener('click', function() {
          const studentId = this.dataset.studentId;
          const contentId = this.dataset.contentId;
          const input = studentsList.querySelector(`input[data-student-id="${studentId}"]`);
          const grade = parseFloat(input.value) || 0;

          saveIndividualGrade(studentId, contentId, grade, this);
        });
      });

    } catch (error) {
      console.error('Error cargando estudiantes para calificar:', error);
      studentsList.innerHTML = '<p style="text-align: center; color: #f44336; padding: 20px;">Error cargando estudiantes.</p>';
    }
  }

  // Función para guardar calificación individual
  async function saveIndividualGrade(studentId, contentId, grade, button) {
    try {
      // Validar calificación
      if (grade < 0 || grade > 20) {
        alert('La calificación debe estar entre 0 y 20');
        return;
      }

      // Cambiar estado del botón
      const originalText = button.textContent;
      button.disabled = true;
      button.textContent = 'Guardando...';
      button.style.background = '#ccc';

      // Enviar calificación al servidor
      const response = await fetch('../Controlador/guardar_calificacion_participacion.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          estudiante_id: studentId,
          contenido_id: contentId,
          calificacion: grade
        })
      });

      const result = await response.json();

      if (result.success) {
        // Éxito
        button.style.background = '#4caf50';
        button.textContent = '✓';

        setTimeout(() => {
          button.style.background = '';
          button.textContent = originalText;
          button.disabled = false;
        }, 2000);

        console.log('Calificación guardada exitosamente:', result);
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error guardando calificación:', error);
      alert('Error al guardar la calificación: ' + error.message);

      // Restaurar botón
      button.style.background = '';
      button.textContent = originalText;
      button.disabled = false;
    }
  }

  // Event listeners para modales de participación
  const cancelGradingBtn = document.getElementById('cancel-grading');
  const saveAllGradesBtn = document.getElementById('save-all-grades');

  if (cancelGradingBtn) {
    cancelGradingBtn.addEventListener('click', closeAllModals);
  }

  if (saveAllGradesBtn) {
    saveAllGradesBtn.addEventListener('click', function() {
      const inputs = document.querySelectorAll('.participation-grade-input');
      const grades = [];

      inputs.forEach(input => {
        const grade = parseFloat(input.value);
        if (grade > 0) {
          grades.push({
            studentId: input.dataset.studentId,
            contentId: input.dataset.contentId,
            grade: grade
          });
        }
      });

      if (grades.length > 0) {
        saveAllParticipationGrades(grades);
      } else {
        alert('No hay calificaciones para guardar');
      }
    });
  }

  // Función para guardar todas las calificaciones
  async function saveAllParticipationGrades(grades) {
    try {
      const saveAllBtn = document.getElementById('save-all-grades');
      const originalText = saveAllBtn.textContent;

      // Cambiar estado del botón
      saveAllBtn.disabled = true;
      saveAllBtn.textContent = 'Guardando...';

      // Guardar cada calificación
      const promises = grades.map(grade =>
        fetch('../Controlador/guardar_calificacion_participacion.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            estudiante_id: grade.studentId,
            contenido_id: grade.contentId,
            calificacion: grade.grade
          })
        }).then(response => response.json())
      );

      const results = await Promise.all(promises);

      // Verificar si todas las operaciones fueron exitosas
      const errors = results.filter(result => !result.success);

      if (errors.length === 0) {
        alert('Todas las calificaciones han sido guardadas exitosamente');
        closeAllModals();
      } else {
        console.error('Errores al guardar:', errors);
        alert(`Se guardaron ${results.length - errors.length} de ${results.length} calificaciones. Revisa la consola para más detalles.`);
      }

    } catch (error) {
      console.error('Error guardando calificaciones:', error);
      alert('Error al guardar las calificaciones: ' + error.message);
    } finally {
      // Restaurar botón
      const saveAllBtn = document.getElementById('save-all-grades');
      if (saveAllBtn) {
        saveAllBtn.disabled = false;
        saveAllBtn.textContent = originalText;
      }
    }
  }

  // Inicializar calificaciones
  loadExistingGrades();
