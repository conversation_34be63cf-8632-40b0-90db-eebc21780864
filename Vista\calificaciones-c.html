<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Calificaciones de Matemáticas</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/calificaciones.css">
</head>
<body class="has-course-header">
  <div class="plataforma-container has-course-header">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar">
                      <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                  </div>
                  <div class="user-details">
                      <h3>Juan Pérez</h3>
                      <p>5° Primaria - Sección A</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_e.html">
                              <span class="material-icons">home</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li>
                          <a href="perfil.html">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="cursos_e.html">
                              <span class="material-icons">book</span>
                              <span>Cursos</span>
                          </a>
                      </li>
                      <li>
                          <a href="notas_e.html">
                              <span class="material-icons">grade</span>
                              <span>Notas</span>
                          </a>
                      </li>
                      <li>
                          <a href="tareas_e.html">
                              <span class="material-icons">assignment</span>
                              <span>Tareas</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="intranet.html">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <!-- Encabezado del curso -->
          <header class="course-header" style="background-color: #4caf50;">
              <div class="course-header-content">
                  <div class="course-header-left">
                      <a href="cursos_e.html" class="back-button">
                          <span class="material-icons">arrow_back</span>
                      </a>
                      <div class="course-title">
                          <h1>Matemáticas</h1>
                          <p>Prof. Carlos García</p>
                      </div>
                  </div>
                  <div class="course-header-right">
                      <div class="course-schedule-info">
                          <div class="schedule-day">
                              <span class="day-label">Lunes</span>
                              <span class="day-time">7:30 - 8:15</span>
                          </div>
                          <div class="schedule-day">
                              <span class="day-label">Miércoles</span>
                              <span class="day-time">7:30 - 8:15</span>
                          </div>
                      </div>
                  </div>
              </div>
          </header>
          
          <!-- Navegación del curso -->
          <div class="course-navigation">
              <div class="course-nav-tabs">
                  <a href="contenido_c.html" class="course-tab">Contenido</a>
                  <a href="tareas_c.html" class="course-tab">Tareas</a>
                  <a href="estudiantes_c.html" class="course-tab">Estudiantes</a>
                  <a href="calificaciones-c.html" class="course-tab active">Calificaciones</a>
                  <a href="asistencias_c.html" class="course-tab">Asistencias</a>
                  <a href="mensajes_c.html" class="course-tab">Mensajes</a>
              </div>
          </div>
          
          <div class="content-body">
              <!-- Tareas calificadas -->
              <section class="dashboard-section">
                  <div class="section-header">
                      <h2>Calificaciones</h2>
                  </div>

                  <div class="grades-table-container">
                      <table class="grades-table">
                          <thead>
                              <tr>
                                  <th>Nombre del elemento</th>
                                  <th>Fecha de registro</th>
                                  <th>Calificación</th>
                                  <th>Comentarios</th>
                                  <th>Acciones</th>
                              </tr>
                          </thead>
                          <tbody>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Sumas y restas</span>
                                  </td>
                                  <td>10/03/2025</td>
                                  <td>
                                      <div class="grade-badge good">
                                          <span class="grade-value">15</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Trabajo completo y bien desarrollado. Excelente comprensión de los conceptos básicos.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Fracciones básicas</span>
                                  </td>
                                  <td>12/03/2025</td>
                                  <td>
                                      <div class="grade-badge excellent">
                                          <span class="grade-value">16.5</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Muy buen trabajo. Demuestra comprensión clara de las fracciones equivalentes.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Problemas de aplicación</span>
                                  </td>
                                  <td>15/03/2025</td>
                                  <td>
                                      <div class="grade-badge good">
                                          <span class="grade-value">14</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Buen esfuerzo, pero necesita mejorar en la interpretación de problemas complejos.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Geometría - Figuras planas</span>
                                  </td>
                                  <td>18/03/2025</td>
                                  <td>
                                      <div class="grade-badge excellent">
                                          <span class="grade-value">17</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Excelente identificación y clasificación de figuras. Muy ordenado y claro.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Operaciones combinadas</span>
                                  </td>
                                  <td>20/03/2025</td>
                                  <td>
                                      <div class="grade-badge good">
                                          <span class="grade-value">13.5</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Necesita practicar más el orden de las operaciones. Revisar la jerarquía matemática.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                              <tr>
                                  <td class="task-name">
                                      <div class="task-icon">
                                          <span class="material-icons">assignment</span>
                                      </div>
                                      <span>Medidas de longitud</span>
                                  </td>
                                  <td>22/03/2025</td>
                                  <td>
                                      <div class="grade-badge excellent">
                                          <span class="grade-value">18</span>
                                      </div>
                                  </td>
                                  <td class="comment-cell">Excelente comprensión de las conversiones. Trabajo muy bien presentado.</td>
                                  <td>
                                      <button class="view-task-btn">
                                          <span class="material-icons">visibility</span>
                                          Ver
                                      </button>
                                  </td>
                              </tr>
                          </tbody>
                      </table>
                  </div>
              </section>
          </div>
      </main>
  </div>

  <!-- Modal para ver tarea calificada -->
  <div id="graded-task-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <div class="task-header-info">
                  <span class="material-icons task-icon-modal">assignment</span>
                  <h3 id="modal-task-title">Tarea: Sumas y restas</h3>
              </div>
              <div class="task-grade-header">
                  <span class="grade-label-header">Calificación:</span>
                  <span id="modal-grade-display" class="grade-value-header">15.0</span>
              </div>
              <button class="modal-close" id="close-graded-task-modal">
                  <span class="material-icons">close</span>
              </button>
          </div>

          <div class="modal-body">
              <div class="task-description-section">
                  <h4>Descripción de la tarea</h4>
                  <p id="modal-task-description">Realiza las sumas y restas de la pág. 19 - 20 del libro de texto.</p>
              </div>

              <div class="task-image-section">
                  <img id="modal-task-image" src="./Imagenes/ejercicios-sumas-restas.jpg" alt="Ejercicios de sumas y restas" style="width: 100%; border-radius: 8px; margin-bottom: 15px;">
              </div>

              <div class="task-submission-info">
                  <div class="submission-detail">
                      <span class="material-icons">schedule</span>
                      <div>
                          <span class="detail-label">Fecha de entrega</span>
                          <span class="detail-value" id="modal-due-date">23/03/2025, 12:00 AM</span>
                      </div>
                  </div>
                  <div class="submission-detail">
                      <span class="material-icons">star</span>
                      <div>
                          <span class="detail-label">Puntos</span>
                          <span class="detail-value">10 puntos máximos</span>
                      </div>
                  </div>
                  <div class="submission-detail">
                      <span class="material-icons">person</span>
                      <div>
                          <span class="detail-label">Asignado por</span>
                          <span class="detail-value">Prof. Carlos García</span>
                      </div>
                  </div>
              </div>

              <div class="submission-status completed">
                  <span class="material-icons">check_circle</span>
                  <span>Entregado - Calificado el 25/03/2025</span>
              </div>

              <div class="submitted-work-section">
                  <h4>Tu entrega</h4>
                  <div class="submitted-file-card">
                      <div class="file-info">
                          <span class="material-icons">description</span>
                          <div class="file-details">
                              <span class="file-name" id="modal-file-name">Tarea_Sumas_Restas_Juan.pdf</span>
                              <span class="file-size">245 KB</span>
                          </div>
                      </div>
                      <button class="download-file-btn">
                          <span class="material-icons">download</span>
                      </button>
                  </div>
              </div>

              <div class="student-comment-section">
                  <h4>Puedes añadir comentarios a tu entrega:</h4>
                  <div class="student-comment-card">
                      <p id="modal-student-comment">Profesor, tuve algunas dificultades con los ejercicios 5 y 6, pero creo que logré resolverlos correctamente. Espero su retroalimentación.</p>
                  </div>
              </div>

              <div class="teacher-feedback-section">
                  <h4>Comentarios del docente</h4>
                  <div class="feedback-card">
                      <p id="modal-teacher-comment">Trabajo completo y bien desarrollado. Excelente comprensión de los conceptos básicos.</p>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/calificaciones.js"></script>
</body>
</html>

