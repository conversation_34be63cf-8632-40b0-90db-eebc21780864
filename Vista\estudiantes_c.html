<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Compañeros de Clase</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/tareas.css">
    <link rel="stylesheet" href="./Css/estudiantes.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Juan Pérez</h3>
                        <p>5° Primaria</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_e.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_e.html">
                                <span class="material-icons">book</span>
                                <span>Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span class="material-icons">grade</span>
                                <span>Notas</span>
                            </a>
                        </li>
                        <li>
                            <a href="tareas_e.html">
                                <span class="material-icons">assignment</span>
                                <span>Tareas</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #4caf50;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_e.html" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1>Matemáticas</h1>
                            <p>Prof. Carlos García</p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <div class="schedule-day">
                                <span class="day-label">Lunes</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                            <div class="schedule-day">
                                <span class="day-label">Miércoles</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_c.html" class="course-tab">Contenido</a>
                    <a href="tareas_c.html" class="course-tab">Tareas</a>
                    <a href="estudiantes_c.html" class="course-tab active">Estudiantes</a>
                    <a href="calificaciones-c.html" class="course-tab">Calificaciones</a>
                    <a href="asistencias_c.html" class="course-tab">Asistencias</a>
                    <a href="mensajes_c.html" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de búsqueda de estudiantes -->
                <section class="filter-section">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="student-search" placeholder="Buscar compañeros por nombre...">
                        </div>
                    </div>
                </section>
                
                <!-- Lista de estudiantes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Compañeros de clase</h2>
                        <span class="student-count">25 estudiantes</span>
                    </div>
                    
                    <div class="classmates-grid">
                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Juan Pérez</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>María López</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Pedro Gómez</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Ana Rodríguez</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Carlos Martínez</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Laura Sánchez</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Miguel Torres</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>

                        <div class="classmate-card">
                            <div class="classmate-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="classmate-info">
                                <h3>Sofía Vargas</h3>
                                <p>5° Primaria</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pagination">
                        <button class="pagination-btn" disabled>
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/estudiantes_c.js"></script>
</body>
</html>