/* Estilos específicos para la página de notas */

:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --math-color: #2196f3;
    --language-color: #9c27b0;
    --science-color: #4caf50;
    --social-color: #ff9800;
    --english-color: #f44336;
    --art-color: #673ab7;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Resumen de notas */
  .grades-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .summary-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .summary-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
  }
  
  .summary-label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 5px;
  }
  
  .summary-detail {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  /* Selector de período */
  .period-selector {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
    width: 100%;
  }
  
  .period-btn {
    padding: 10px 20px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
  }
  
  .period-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .period-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  /* Tabla de notas */
  .grades-table-container {
    overflow-x: auto;
  }
  
  .grades-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  .grades-table thead {
    background-color: var(--secondary-color);
  }
  
  .grades-table th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .grades-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
  }
  
  .grades-table tbody tr:last-child td {
    border-bottom: none;
  }
  
  .grades-table tbody tr:hover {
    background-color: var(--secondary-color);
  }
  
  .course-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
  }
  
  .course-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .course-icon.math {
    background-color: rgba(33, 150, 243, 0.1);
  }
  
  .course-icon.math .material-icons {
    color: var(--math-color);
  }
  
  .course-icon.language {
    background-color: rgba(156, 39, 176, 0.1);
  }
  
  .course-icon.language .material-icons {
    color: var(--language-color);
  }
  
  .course-icon.science {
    background-color: rgba(76, 175, 80, 0.1);
  }
  
  .course-icon.science .material-icons {
    color: var(--science-color);
  }
  
  .course-icon.social {
    background-color: rgba(255, 152, 0, 0.1);
  }
  
  .course-icon.social .material-icons {
    color: var(--social-color);
  }
  
  .course-icon.english {
    background-color: rgba(244, 67, 54, 0.1);
  }
  
  .course-icon.english .material-icons {
    color: var(--english-color);
  }
  
  .course-icon.art {
    background-color: rgba(103, 58, 183, 0.1);
  }

  .course-icon.art .material-icons {
    color: var(--art-color);
  }

  .course-icon.physical {
    background-color: rgba(255, 152, 0, 0.1);
  }

  .course-icon.physical .material-icons {
    color: #ff9800;
  }
  
  .grade-average {
    font-weight: 600;
    color: var(--primary-color);
  }
  
  /* Gráfico de rendimiento */
  .performance-chart {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .chart-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
  }
  
  .legend-color {
    width: 15px;
    height: 15px;
    border-radius: 3px;
  }
  
  .legend-color.math {
    background-color: var(--math-color);
  }
  
  .legend-color.language {
    background-color: var(--language-color);
  }
  
  .legend-color.science {
    background-color: var(--science-color);
  }
  
  .legend-color.social {
    background-color: var(--social-color);
  }
  
  .legend-color.english {
    background-color: var(--english-color);
  }
  
  .legend-color.art {
    background-color: var(--art-color);
  }

  .legend-color.physical {
    background-color: #ff9800;
  }
  
  .chart-visual {
    display: flex;
    height: 300px;
    position: relative;
    margin-top: 20px;
  }
  
  .chart-bars {
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    position: relative;
    z-index: 2;
  }
  
  .chart-period {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .period-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 10px;
    text-align: center;
  }
  
  .period-bars {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: 250px;
  }
  
  .bar {
    width: 30px;
    border-radius: 5px 5px 0 0;
    position: relative;
    transition: var(--transition);
    cursor: pointer;
  }
  
  .bar:hover {
    opacity: 0.8;
  }
  
  .bar.math {
    background-color: var(--math-color);
  }
  
  .bar.language {
    background-color: var(--language-color);
  }
  
  .bar.science {
    background-color: var(--science-color);
  }
  
  .bar.social {
    background-color: var(--social-color);
  }
  
  .bar.english {
    background-color: var(--english-color);
  }
  
  .bar.art {
    background-color: var(--art-color);
  }

  .bar.physical {
    background-color: #ff9800;
  }
  
  .bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-color);
    background-color: white;
    padding: 2px 5px;
    border-radius: 3px;
    box-shadow: var(--shadow-sm);
  }
  
  .chart-scale {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 250px;
  }
  
  .scale-line {
    position: absolute;
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    left: 0;
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
  }
  
  .scale-line::before {
    content: "";
    width: 5px;
    height: 1px;
    background-color: var(--text-light);
    margin-right: 5px;
  }
  
  /* Comentarios de profesores */
  .teacher-comments {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .comment-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .comment-card:hover {
    box-shadow: var(--shadow-md);
  }
  
  .comment-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .teacher-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--primary-light);
  }
  
  .teacher-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .comment-info h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color);
  }
  
  .comment-info p {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .comment-body p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-color);
  }
  
  /* Responsive */
  @media (max-width: 992px) {
    .grades-summary {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .period-bars {
      gap: 10px;
    }
  
    .bar {
      width: 25px;
    }
  }
  
  @media (max-width: 768px) {
    .grades-summary {
      grid-template-columns: 1fr;
    }
  
    .period-selector {
      justify-content: flex-start;
    }
  
    .bar {
      width: 20px;
    }
  
    .bar-value {
      font-size: 0.7rem;
    }
  }
    