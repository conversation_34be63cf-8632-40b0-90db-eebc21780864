/* Estilos específicos para calificaciones */

:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --exams-color: #f44336;
    --tasks-color: #4caf50;
    --participation-color: #2196f3;
    --average-color: #9c27b0;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  /* Resumen de calificaciones */
  .grades-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .summary-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .summary-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
  }
  
  .summary-label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 5px;
  }
  
  .summary-detail {
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  /* Selector de período */
  .period-selector {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
    width: 100%;
  }
  
  .period-btn {
    padding: 10px 20px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
  }
  
  .period-btn:hover {
    background-color: var(--secondary-color);
  }
  
  .period-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  /* Tabla de calificaciones */
  .grades-table-container {
    overflow-x: auto;
  }
  
  .grades-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    table-layout: fixed;
  }

  .grades-table thead {
    background-color: var(--secondary-color);
  }

  .grades-table th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
  }

  .grades-table th:nth-child(1) { width: 25%; }
  .grades-table th:nth-child(2) { width: 15%; }
  .grades-table th:nth-child(3) { width: 12%; }
  .grades-table th:nth-child(4) { width: 35%; }
  .grades-table th:nth-child(5) { width: 13%; }

  .grades-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    vertical-align: middle;
    word-wrap: break-word;
    font-size: 0.9rem;
  }

  .grades-table tbody tr:last-child td {
    border-bottom: none;
  }

  .grades-table tbody tr:hover {
    background-color: var(--secondary-color);
  }
  
  .evaluation-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
  }
  
  .evaluation-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .evaluation-icon.exam {
    background-color: rgba(244, 67, 54, 0.1);
  }
  
  .evaluation-icon.exam .material-icons {
    color: var(--exams-color);
  }
  
  .evaluation-icon.task {
    background-color: rgba(76, 175, 80, 0.1);
  }
  
  .evaluation-icon.task .material-icons {
    color: var(--tasks-color);
  }
  
  .evaluation-icon.participation {
    background-color: rgba(33, 150, 243, 0.1);
  }
  
  .evaluation-icon.participation .material-icons {
    color: var(--participation-color);
  }
  
  .grade {
    font-weight: 600;
    color: var(--primary-color);
  }
  
  /* Gráfico de rendimiento */
  .performance-chart {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .chart-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
  }
  
  .legend-color {
    width: 15px;
    height: 15px;
    border-radius: 3px;
  }
  
  .legend-color.exams {
    background-color: var(--exams-color);
  }
  
  .legend-color.tasks {
    background-color: var(--tasks-color);
  }
  
  .legend-color.participation {
    background-color: var(--participation-color);
  }
  
  .legend-color.average {
    background-color: var(--average-color);
  }
  
  .chart-visual {
    display: flex;
    height: 300px;
    position: relative;
    margin-top: 20px;
  }
  
  .chart-bars {
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    position: relative;
    z-index: 2;
  }
  
  .chart-period {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .period-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 10px;
    text-align: center;
  }
  
  .period-bars {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: 250px;
  }
  
  .bar {
    width: 30px;
    border-radius: 5px 5px 0 0;
    position: relative;
    transition: var(--transition);
    cursor: pointer;
  }
  
  .bar:hover {
    opacity: 0.8;
  }
  
  .bar.exams {
    background-color: var(--exams-color);
  }
  
  .bar.tasks {
    background-color: var(--tasks-color);
  }
  
  .bar.participation {
    background-color: var(--participation-color);
  }
  
  .bar.average {
    background-color: var(--average-color);
  }
  
  .bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-color);
    background-color: white;
    padding: 2px 5px;
    border-radius: 3px;
    box-shadow: var(--shadow-sm);
  }
  
  .chart-scale {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 250px;
  }
  
  .scale-line {
    position: absolute;
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    left: 0;
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
  }
  
  .scale-line::before {
    content: "";
    width: 5px;
    height: 1px;
    background-color: var(--text-light);
    margin-right: 5px;
  }

  /* Estilos para tareas calificadas en columnas */
  .graded-tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .task-name {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .task-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
  }

  .grade-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 35px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
  }

  .grade-badge.excellent {
    background-color: #4caf50;
  }

  .grade-badge.good {
    background-color: #ff9800;
  }

  .grade-badge.poor {
    background-color: #f44336;
  }

  .comment-cell {
    max-width: 300px;
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.4;
  }

  .view-task-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    background-color: var(--text-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
  }

  .view-task-btn:hover {
    background-color: var(--text-dark);
  }

  .view-task-btn .material-icons {
    font-size: 16px;
  }

  /* Modal para tarea calificada */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-overlay.active {
    display: flex;
  }

  .modal-content {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-light);
  }

  .task-header-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .task-icon-modal {
    color: var(--primary-color);
    font-size: 24px;
  }

  .modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.2rem;
  }

  .task-grade-header {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .grade-label-header {
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .grade-value-header {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
  }

  .modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
  }

  .modal-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .modal-body {
    padding: 20px 40px;
  }

  .task-description-section {
    margin-bottom: 20px;
  }

  .task-description-section h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
  }

  .task-description-section p {
    margin: 0;
    color: var(--text-light);
    line-height: 1.5;
  }

  .task-submission-info {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 8px;
  }

  .submission-detail {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .submission-detail .material-icons {
    color: var(--primary-color);
    font-size: 18px;
  }

  .submission-detail div {
    display: flex;
    flex-direction: column;
  }

  .detail-label {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
  }

  .detail-value {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .submission-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .submission-status.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
  }

  .submission-status.completed .material-icons {
    color: #2e7d32;
  }

  .submitted-work-section,
  .student-comment-section,
  .teacher-feedback-section {
    margin-bottom: 20px;
  }

  .submitted-work-section h4,
  .student-comment-section h4,
  .teacher-feedback-section h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
  }

  .submitted-file-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 8px;
    border: 1px solid var(--border-color);
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .file-info .material-icons {
    color: var(--primary-color);
    font-size: 24px;
  }

  .file-details {
    display: flex;
    flex-direction: column;
  }

  .file-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
  }

  .file-size {
    font-size: 0.8rem;
    color: var(--text-light);
  }

  .download-file-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
  }

  .download-file-btn:hover {
    background-color: var(--primary-color);
    color: white;
  }

  .download-file-btn .material-icons {
    color: var(--primary-color);
    font-size: 20px;
  }

  .download-file-btn:hover .material-icons {
    color: white;
  }

  .student-comment-card {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6c757d;
  }

  .student-comment-card p {
    margin: 0;
    color: var(--text-color);
    line-height: 1.5;
    font-style: italic;
  }

  .feedback-card {
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }

  .feedback-card p {
    margin: 0;
    color: var(--text-color);
    line-height: 1.5;
  }

  .task-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
  }

  .task-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .task-icon .material-icons {
    color: var(--tasks-color);
    font-size: 20px;
  }

  .task-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
  }

  .task-grade {
    display: flex;
    align-items: baseline;
    gap: 3px;
    margin-bottom: 10px;
  }

  .grade-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
  }

  .grade-max {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-light);
  }

  .task-date {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 15px;
    font-weight: 500;
  }

  .teacher-comment {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
  }

  .teacher-comment h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 8px 0;
  }

  .teacher-comment p {
    font-size: 0.9rem;
    color: var(--text-color);
    line-height: 1.4;
    margin: 0;
  }

  /* Responsive */
  @media (max-width: 992px) {
    .grades-summary {
      grid-template-columns: repeat(2, 1fr);
    }

    .graded-tasks-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 15px;
    }

    .period-bars {
      gap: 10px;
    }

    .bar {
      width: 25px;
    }
  }

  @media (max-width: 768px) {
    .grades-summary {
      grid-template-columns: 1fr;
    }

    .graded-tasks-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .task-grade-card {
      padding: 15px;
    }

    .grade-value {
      font-size: 0.9rem;
    }

    .period-selector {
      justify-content: flex-start;
    }

    .bar {
      width: 20px;
    }

    .bar-value {
      font-size: 0.7rem;
    }
  }
  
  